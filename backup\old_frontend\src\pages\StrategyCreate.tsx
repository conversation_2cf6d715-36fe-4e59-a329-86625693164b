import React, { useState, useCallback, useEffect, useMemo, useRef } from 'react';
import { Layout, Space, App, Modal, Form, Tooltip } from 'antd';
import { useNavigate } from 'react-router-dom';
import { Node, Edge } from 'reactflow';
import { LayoutOutlined, CodeSandboxOutlined, SaveOutlined, RocketOutlined, QuestionCircleOutlined, PlayCircleOutlined, CloseOutlined, AppstoreOutlined } from '@ant-design/icons';
import classNames from 'classnames';
import PageTitle from '../components/common/PageTitle';
import StrategyCanvas from '../components/strategy/StrategyCanvas';
import SimpleList from '../components/strategy/SimpleList';
import ModeToggle from '../components/strategy/ModeToggle';
import QuantumButton from '../components/common/QuantumButton';
import StrategyMarket from '../components/strategy/StrategyMarket';
import TourGuide from '../components/strategy/TourGuide';
import SaveStrategyModal from '../components/strategy/SaveStrategyModal';
import { strategyTemplate<PERSON>pi, strategyGroup<PERSON>pi, debugStrategy } from '../services/strategyService';
import { StrategyCardTemplate, StrategyCardInstance, StrategyGroup } from '../types/api';
import StrategyDebugConsole from '../components/StrategyDebugConsole';
import { calculateOptimalPosition, autoLayout } from '../utils/layout';
import './StrategyCreate.scss';

const { Content, Sider } = Layout;

type CreationMode = 'simple' | 'canvas';
type ExecutionType = 'onetime' | 'continuous';

interface IStrategyNode extends Node {
  id: string;
  type: 'strategy';
  position: { x: number; y: number };
  data: {
    id: string;
    name: string;
    description: string;
    parameters: Record<string, any>;
    template?: StrategyCardTemplate;
  };
}

// 生成唯一ID
const generateId = () => `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

const StrategyCreate: React.FC = () => {
  const { message } = App.useApp();
  const navigate = useNavigate();
  const [form] = Form.useForm();
  
  // 基础状态
  const [mode, setMode] = useState<CreationMode>('simple');
  const [loading, setLoading] = useState(false);
  const [templates, setTemplates] = useState<StrategyCardTemplate[]>([]);
  const [selectedCards, setSelectedCards] = useState<StrategyCardInstance[]>([]);
  const [executionMode, setExecutionMode] = useState<'sequential' | 'parallel'>('sequential');
  const [nodes, setNodes] = useState<IStrategyNode[]>([]);
  const [edges, setEdges] = useState<Edge[]>([]);
  
  // 分页状态
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
    hasMore: true
  });
  
  // 策略组类型状态
  const [groupType, setGroupType] = useState<'filter' | 'timing'>('filter');
  const [timingSymbols, setTimingSymbols] = useState<string>('');
  const [klinePeriod, setKlinePeriod] = useState<string>('5min');
  
  // 修改抽屉状态为模态框状态
  const [saveModalVisible, setSaveModalVisible] = useState(false);
  
  // 修改引导状态管理
  const [tourOpen, setTourOpen] = useState(false);
  const [tourStep, setTourStep] = useState(0);
  
  // 模式切换状态管理
  const [modeChangeModalVisible, setModeChangeModalVisible] = useState(false);
  const [pendingMode, setPendingMode] = useState<CreationMode | null>(null);
  
  // 调试状态管理
  const [isDebugging, setIsDebugging] = useState(false);
  const [debuggingCardId, setDebuggingCardId] = useState<string | null>(null);
  const [finalResult, setFinalResult] = useState<any>(null);
  
  const canvasRef = useRef<HTMLDivElement>(null);
  // 模板加载标志 - 放在组件顶层，避免在useEffect中调用useRef
  const hasLoadedTemplates = useRef(false);

  // 检查是否是首次访问
  useEffect(() => {
    const checkFirstVisit = () => {
      const hasShownTour = localStorage.getItem('strategy_create_tour_shown');
      if (!hasShownTour) {
        setTourOpen(true);
        localStorage.setItem('strategy_create_tour_shown', 'true');
      }
    };
    checkFirstVisit();
  }, []);

  // 从URL参数获取策略组类型，并加载对应模板
  useEffect(() => {
    // 使用顶层定义的ref，避免在useEffect中调用useRef
    if (hasLoadedTemplates.current) return;
    
    const loadTemplates = async () => {
      try {
        // 标记为已加载，确保只执行一次
        hasLoadedTemplates.current = true;
        
        // 解析URL参数
        const urlParams = new URLSearchParams(window.location.search);
        const urlGroupType = urlParams.get('groupType') as 'filter' | 'timing' | null;
        const urlTimingSymbols = urlParams.get('timingSymbols');
        const urlKlinePeriod = urlParams.get('klinePeriod');
        
        // 设置策略组类型和择时标的
        const currentGroupType = (urlGroupType === 'filter' || urlGroupType === 'timing') 
          ? urlGroupType : 'filter';
        
        if (currentGroupType !== groupType) {
          setGroupType(currentGroupType);
        }
        
        if (urlTimingSymbols) {
          setTimingSymbols(urlTimingSymbols);
        }
        
        if (urlKlinePeriod && currentGroupType === 'timing') {
          setKlinePeriod(urlKlinePeriod);
        }
        
        // 加载模板
        setLoading(true);
        const tagFilter = currentGroupType === 'filter' ? '选股' : '择时';
        
        const response = await strategyTemplateApi.getTemplates({
          page: 1,
          pageSize: pagination.pageSize,
          tags: tagFilter
        });
        
        if (response.items?.length > 0) {
          setTemplates(response.items);
          setPagination({
            current: 1,
            pageSize: pagination.pageSize,
            total: response.total,
            hasMore: response.items.length === pagination.pageSize
          });
          
          // 只显示一次成功消息
          message.success(`成功加载 ${response.items.length} 个${tagFilter}策略卡模板`);
        } else {
          message.warning(`暂无可用的${tagFilter}策略卡模板`);
          setTemplates([]);
        }
      } catch (error) {
        console.error('加载策略卡模板失败:', error);
        message.error('加载策略卡模板失败，请刷新页面重试');
      } finally {
        setLoading(false);
      }
    };

    loadTemplates();
    
    // 清除localStorage相关项
    localStorage.removeItem('strategy_group_type');
    localStorage.removeItem('strategy_timing_symbols');
  }, []);
  
  // 加载更多策略卡
  const handleLoadMore = useCallback(() => {
    if (!pagination.hasMore || loading) return;
    
    // 只有已经完成初始加载才能加载更多
    if (!hasLoadedTemplates.current) return;
    
    setLoading(true);
    const nextPage = pagination.current + 1;
    const tagFilter = groupType === 'filter' ? '选股' : '择时';
    
    strategyTemplateApi.getTemplates({
      page: nextPage,
      pageSize: pagination.pageSize,
      tags: tagFilter
    })
    .then(response => {
      if (response.items?.length > 0) {
        // 只追加新数据，不修改现有数据
        setTemplates(prev => [...prev, ...response.items]);
        setPagination({
          current: nextPage,
          pageSize: pagination.pageSize,
          total: response.total,
          hasMore: response.items.length === pagination.pageSize
        });
      } else {
        message.info('没有更多策略卡模板');
        setPagination(prev => ({ ...prev, hasMore: false }));
      }
    })
    .catch(error => {
      console.error('加载更多策略卡失败:', error);
      message.error('加载更多策略卡失败');
    })
    .finally(() => {
      setLoading(false);
    });
  }, [pagination, groupType, loading]);

  // 处理参数更新
  const handleParamUpdate = useCallback((cardId: string, parameters: Record<string, any>) => {
    // 添加调试日志，用于观察参数是否包含特殊参数
    const hasSpecialParams = Object.keys(parameters).some(key => key.startsWith('@'));
    if (hasSpecialParams) {
      console.log('⚠️ 警告: 用户试图修改特殊参数:', 
        Object.entries(parameters).filter(([key]) => key.startsWith('@'))
      );
      // 移除所有特殊参数（以@开头的参数）
      const filteredParams = Object.fromEntries(
        Object.entries(parameters).filter(([key]) => !key.startsWith('@'))
      );
      parameters = filteredParams;
    }
    
    console.log('Parameter Update:', { 
      cardId, 
      parameters,
      groupType, // 记录当前的策略组类型
      mode: 'update' 
    });
    
    setSelectedCards(cards => {
      const updatedCards = cards.map(card => {
        if (card.id === cardId) {
          // 获取模板
          const template = templates.find(t => t.id === card.template.id);
          if (!template) return card;

          // 合并新参数和默认值
          const mergedParameters = { ...parameters };
          Object.entries(template.parameters).forEach(([key, param]) => {
            if (mergedParameters[key] === undefined && param.default !== undefined) {
              mergedParameters[key] = param.default;
            }
          });

          return {
            ...card,
            parameters: mergedParameters
          };
        }
        return card;
      });
      return updatedCards;
    });
  }, [templates, groupType]);

  // 处理添加到工作区
  const handleAddToWorkspace = useCallback((template: StrategyCardTemplate, currentMode: 'simple' | 'canvas') => {
    const newId = generateId();
    
    if (currentMode === 'canvas') {
      // 画布模式：添加新节点
      const canvasSize = {
        width: canvasRef.current?.clientWidth || 800,
        height: canvasRef.current?.clientHeight || 600
      };
      
      const position = calculateOptimalPosition(nodes, canvasSize);
      
      const newNode: IStrategyNode = {
        id: newId,
        type: 'strategy',
        position,
        data: {
          id: template.id,
          name: template.name,
          description: template.description,
          parameters: {},
          template
        }
      };
      
      setNodes(prevNodes => [...prevNodes, newNode]);
    } else {
      // 列表模式：添加新卡片
      const newCard: StrategyCardInstance = {
        id: newId,
        name: template.name,
        description: template.description,
        parameters: {},
        position: { x: 0, y: 0 },
        template: {
          ...template,
          parameters: template.parameters || {},
          parameterGroups: template.parameterGroups || {},
          ui: template.ui || {
            form: {
              layout: 'horizontal',
              compact: true,
              showDescription: false
            }
          }
        }
      };
      
      setSelectedCards(prevCards => [...prevCards, newCard]);
    }
  }, [nodes]);

  // 添加自动布局功能
  const handleAutoLayout = useCallback(() => {
    if (!canvasRef.current) return;
    
    const canvasSize = {
      width: canvasRef.current.clientWidth,
      height: canvasRef.current.clientHeight
    };
    
    const layoutedNodes = autoLayout(nodes, canvasSize);
    setNodes(layoutedNodes);
  }, [nodes]);

  // 使用 useMemo 优化传递给子组件的数据
  const marketProps = useMemo(() => ({
    templates,
    selectedCards: selectedCards.map(card => card.id),
    mode,
    groupType,
    onAddToWorkspace: (template: StrategyCardTemplate) => handleAddToWorkspace(template, mode),
    loading,
    hasMore: pagination.hasMore,
    onLoadMore: handleLoadMore
  }), [
    templates,
    selectedCards.map(card => card.id).join(','), // 仅当选择的卡片ID改变时更新
    mode,
    groupType,
    loading,
    pagination.hasMore,
    handleLoadMore,
    handleAddToWorkspace
  ]);

  const simpleListProps = useMemo(() => ({
    cards: templates,
    selectedCards,
    executionMode,
    onCardsChange: setSelectedCards,
    onExecutionModeChange: setExecutionMode,
    onParamUpdate: handleParamUpdate,
    debuggingCardId
  }), [templates, selectedCards, executionMode, handleParamUpdate, debuggingCardId]);

  // 处理模式切换
  const handleModeChange = useCallback((newMode: CreationMode) => {
    if (newMode === mode) return;
    
    if (selectedCards.length > 0 || nodes.length > 0) {
      setPendingMode(newMode);
      setModeChangeModalVisible(true);
    } else {
      setMode(newMode);
    }
  }, [mode, selectedCards, nodes]);

  // 确认模式切换
  const handleConfirmModeChange = () => {
    if (!pendingMode) return;
    
    const newMode = pendingMode;
    if (newMode === 'canvas') {
      const newNodes: IStrategyNode[] = selectedCards.map((item, index) => ({
        id: item.id,
        type: 'strategy' as const,
        position: { x: 100, y: 100 * (index + 1) },
        data: {
          id: item.template.id,
          name: item.name,
          description: item.description,
          parameters: item.parameters,
          template: item.template
        },
      }));
      const newEdges = executionMode === 'sequential'
        ? selectedCards.slice(0, -1).map((_, index) => ({
            id: `e${index}-${index + 1}`,
            source: selectedCards[index].id,
            target: selectedCards[index + 1].id,
          }))
        : [];
      setNodes(newNodes);
      setEdges(newEdges);
    } else {
      // 从Canvas切换到SimpleList
      const newSelectedCards = nodes.map(node => {
        // 直接使用节点中保存的完整模板数据
        return {
          id: node.id,
          name: node.data.name,
          description: node.data.description,
          parameters: node.data.parameters || {},
          position: node.position,
          template: node.data.template  // 使用完整的模板数据
        };
      }).filter(card => card.template) as StrategyCardInstance[];

      setSelectedCards(newSelectedCards);
      setExecutionMode(edges.length > 0 ? 'sequential' : 'parallel');
    }
    setMode(newMode);
    setModeChangeModalVisible(false);
    setPendingMode(null);
  };

  // 处理调试
  const handleDebug = async (debugResult: any) => {
    setLoading(true);
    
    try {
      console.log('原始调试结果:', debugResult);
      
      if (!debugResult) {
        message.warning('未接收到调试结果');
        setIsDebugging(false);
        return;
      }
      
      // 直接使用标准化的结果，无需适配转换
      setFinalResult(debugResult);
    } catch (error) {
      console.error('处理调试结果出错:', error);
      message.error('处理调试结果失败');
      
      setFinalResult({
        success: false,
        message: '处理调试结果失败',
        logs: [],
        data: null
      });
    } finally {
      setLoading(false);
    }
  };

  // 关闭调试
  const handleCloseDebug = () => {
    setFinalResult(null);
    setIsDebugging(false);
    setDebuggingCardId(null);
  };

  return (
    <Layout className="strategy-create">
      <PageTitle
        title={
          <Space>
            创建策略
            <Tooltip title="查看引导">
              <QuestionCircleOutlined 
                className="help-icon"
                onClick={() => setTourOpen(true)}
                style={{ 
                  color: 'rgba(0, 0, 0, 0.45)', 
                  fontSize: '16px',
                  cursor: 'pointer'
                }}
              />
            </Tooltip>
          </Space>
        }
        subtitle="通过组合策略卡片创建您的策略组合"
        breadcrumbs={[
          { label: '策略管理', path: '/strategies' },
          { label: '创建策略' },
        ]}
      />

      <Layout className="strategy-create-content">
        <Sider width={400} className="strategy-market-sider">
          <StrategyMarket {...marketProps} />
        </Sider>
        
        <Content className="strategy-workspace">
          <div className="workspace-header">
            <Space>
              <ModeToggle mode={mode} onChange={handleModeChange} />
              {mode === 'canvas' && (
                <Tooltip title="自动布局">
                  <QuantumButton
                    variant="secondary"
                    icon={<AppstoreOutlined />}
                    onClick={handleAutoLayout}
                  />
                </Tooltip>
              )}
              <Tooltip 
                title="调试策略组"
                open={isDebugging ? false : undefined}
                mouseLeaveDelay={0}
              >
                <QuantumButton
                  variant="secondary"
                  icon={<PlayCircleOutlined />}
                  onClick={async (e) => {
                    e.stopPropagation();
                    // 立即关闭tooltip
                    const target = e.currentTarget;
                    const mouseLeaveEvent = new MouseEvent('mouseleave', {
                      bubbles: true,
                      cancelable: true,
                    });
                    target.dispatchEvent(mouseLeaveEvent);
                    
                    // 开始调试
                    setIsDebugging(true);
                    
                    try {
                      // 确保有策略卡片
                      if (mode === 'simple' && selectedCards.length === 0) {
                        message.warning('请先添加策略卡片');
                        setIsDebugging(false);
                        return;
                      } else if (mode === 'canvas' && nodes.length === 0) {
                        message.warning('请先添加策略卡片');
                        setIsDebugging(false);
                        return;
                      }
                      
                      // 根据模式构建卡片数据
                      const cards = mode === 'simple' 
                        ? selectedCards.map(card => ({
                            id: card.template.id,
                            parameters: card.parameters || {}
                          }))
                        : nodes.map(node => ({
                            id: node.data.id,
                            parameters: node.data.parameters || {}
                          }));
                      
                      // 确保有卡片可调试
                      if (cards.length === 0) {
                        message.warning('没有可调试的策略卡片');
                        setIsDebugging(false);
                        return;
                      }
                      
                      // 第一个卡片作为主卡片，其余作为next_cards
                      const debugParams = {
                        id: cards[0].id,
                        parameters: {
                          ...cards[0].parameters,
                          // 当处于择时模式时，强制将kline_period参数添加到每个策略卡的参数中
                          ...(groupType === 'timing' ? { kline_period: klinePeriod } : {})
                        },
                        execution_mode: executionMode,
                        group_type: groupType, // 传递策略组类型给后端
                        timing_symbols: groupType === 'timing' ? timingSymbols : undefined,
                        kline_period: groupType === 'timing' ? klinePeriod : undefined, // 添加K线周期
                        next_cards: cards.slice(1).map(card => ({
                          id: card.id,
                          parameters: {
                            ...card.parameters,
                            // 当处于择时模式时，为每个策略卡添加相同的K线周期参数
                            ...(groupType === 'timing' ? { kline_period: klinePeriod } : {})
                          }
                        }))
                      };
                      
                      console.log('发送调试请求:', debugParams);
                      const result = await debugStrategy(debugParams);
                      
                      if (result && result.data) {
                        await handleDebug(result);
                      } else {
                        message.warning('调试未返回有效结果');
                        setIsDebugging(false);
                      }
                    } catch (error) {
                      console.error('调试请求失败:', error);
                      message.error('调试请求失败');
                      setIsDebugging(false);
                    }
                  }}
                  loading={isDebugging}
                />
              </Tooltip>
            </Space>
            <Tooltip title="保存策略">
              <QuantumButton
                variant="primary"
                icon={<SaveOutlined />}
                onClick={() => setSaveModalVisible(true)}
                className="save-strategy-btn"
              />
            </Tooltip>
          </div>
          
          <div className="workspace-content" ref={canvasRef}>
            {mode === 'simple' ? (
              <SimpleList 
                {...simpleListProps}
                groupType={groupType} 
              />
            ) : (
              <StrategyCanvas
                nodes={nodes}
                edges={edges}
                onNodesChange={setNodes}
                onEdgesChange={setEdges}
                onParamUpdate={handleParamUpdate}
                onNodeAdd={(node) => setNodes(nodes => [...nodes, node])}
                readOnly={false}
                debuggingCardId={debuggingCardId}
                groupType={groupType}
              />
            )}
          </div>

          {finalResult && (
            <div className="debug-console-wrapper">
              <StrategyDebugConsole
                result={finalResult}
                onClose={handleCloseDebug}
                strategyType={groupType}
              />
            </div>
          )}
        </Content>
      </Layout>

      {/* 使用新的SaveStrategyModal组件替代Drawer */}
      <SaveStrategyModal
        visible={saveModalVisible}
        onCancel={() => setSaveModalVisible(false)}
        selectedCards={selectedCards}
        nodes={nodes}
        mode={mode}
        executionMode={executionMode}
        groupType={groupType}
        timingSymbols={timingSymbols}
        klinePeriod={klinePeriod}
      />

      <TourGuide
        open={tourOpen}
        onClose={() => setTourOpen(false)}
        current={tourStep}
        onChange={setTourStep}
      />

      <Modal
        title="确认切换模式"
        open={modeChangeModalVisible}
        onOk={handleConfirmModeChange}
        onCancel={() => {
          setModeChangeModalVisible(false);
          setPendingMode(null);
        }}
        okText="确认"
        cancelText="取消"
      >
        <p>切换模式可能会丢失当前未保存的更改，是否继续？</p>
      </Modal>
    </Layout>
  );
};

export default StrategyCreate; 