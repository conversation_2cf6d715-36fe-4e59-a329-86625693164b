import React from 'react';
import { Card, Row, Col, Statistic, Progress, Tooltip } from 'antd';
import { 
  ArrowUpOutlined, 
  ArrowDownOutlined,
  ThunderboltOutlined,
  WarningOutlined,
  LineChartOutlined
} from '@ant-design/icons';
import './MarketSentiment.scss';

interface MarketSentimentProps {
  data: {
    bullishCount: number;
    bearishCount: number;
    limitUpCount: number;
    limitDownCount: number;
    divergenceCount: number;
    totalStocks: number;
    vix: number;
    marketTrend: 'bullish' | 'bearish' | 'neutral';
  };
}

const MarketSentiment: React.FC<MarketSentimentProps> = ({ data }) => {
  const {
    bullishCount,
    bearishCount,
    limitUpCount,
    limitDownCount,
    divergenceCount,
    totalStocks,
    vix,
    marketTrend
  } = data;

  const bullishPercentage = (bullishCount / totalStocks) * 100;
  const bearishPercentage = (bearishCount / totalStocks) * 100;

  const getTrendColor = () => {
    switch (marketTrend) {
      case 'bullish':
        return '#50fa7b';
      case 'bearish':
        return '#ff5555';
      default:
        return '#f8f8f2';
    }
  };

  const getVixStatus = () => {
    if (vix >= 30) return { color: '#ff5555', text: '恐慌' };
    if (vix >= 20) return { color: '#ffb86c', text: '警惕' };
    return { color: '#50fa7b', text: '贪婪' };
  };

  return (
    <Card className="market-sentiment-card">
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={12}>
          <div className="sentiment-section">
            <h4>市场多空比例</h4>
            <div className="progress-container">
              <Tooltip title={`多头：${bullishCount}只 (${bullishPercentage.toFixed(1)}%)`}>
                <Progress
                  percent={bullishPercentage}
                  strokeColor="#50fa7b"
                  trailColor="#6272a4"
                  showInfo={false}
                />
              </Tooltip>
              <Tooltip title={`空头：${bearishCount}只 (${bearishPercentage.toFixed(1)}%)`}>
                <Progress
                  percent={bearishPercentage}
                  strokeColor="#ff5555"
                  trailColor="#6272a4"
                  showInfo={false}
                />
              </Tooltip>
            </div>
          </div>
        </Col>
        
        <Col xs={24} sm={12}>
          <div className="sentiment-section">
            <h4>恐慌指数 (VIX)</h4>
            <Statistic
              value={vix}
              precision={2}
              valueStyle={{ color: getVixStatus().color }}
              suffix={getVixStatus().text}
              prefix={<WarningOutlined />}
            />
          </div>
        </Col>

        <Col xs={12} sm={8}>
          <Statistic
            title="涨停数量"
            value={limitUpCount}
            valueStyle={{ color: '#50fa7b' }}
            prefix={<ArrowUpOutlined />}
          />
        </Col>

        <Col xs={12} sm={8}>
          <Statistic
            title="跌停数量"
            value={limitDownCount}
            valueStyle={{ color: '#ff5555' }}
            prefix={<ArrowDownOutlined />}
          />
        </Col>

        <Col xs={24} sm={8}>
          <Statistic
            title="背离数量"
            value={divergenceCount}
            valueStyle={{ color: '#bd93f9' }}
            prefix={<LineChartOutlined />}
          />
        </Col>
      </Row>

      <div className="trend-indicator" style={{ color: getTrendColor() }}>
        <ThunderboltOutlined />
        <span>
          市场趋势：
          {marketTrend === 'bullish' ? '看多' : marketTrend === 'bearish' ? '看空' : '中性'}
        </span>
      </div>
    </Card>
  );
};

export default MarketSentiment; 