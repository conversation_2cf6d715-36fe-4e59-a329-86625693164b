import asyncio
from typing import Dict, Any, List, Callable, Coroutine, Optional
import logging
import time

logger = logging.getLogger(__name__)

class InMemoryQueue:
    """内存消息队列 - 优化版"""
    
    def __init__(self, batch_size: int = 10, batch_timeout_ms: int = 50):
        self._queues = {}  # 主题 -> 队列
        self._subscribers = {}  # 主题 -> 处理函数列表
        self._worker_tasks = {}  # 主题 -> 工作任务
        self._batch_size = batch_size  # 批处理大小
        self._batch_timeout_ms = batch_timeout_ms  # 批处理超时(毫秒)
    
    async def publish(self, topic: str, message: Dict[str, Any]):
        """发布消息到指定主题"""
        if topic not in self._queues:
            self._queues[topic] = asyncio.Queue()
            if topic in self._subscribers and topic not in self._worker_tasks:
                self._start_worker(topic)
        
        await self._queues[topic].put(message)
    
    def subscribe(self, topic: str, callback: Callable[[Dict[str, Any]], Coroutine]):
        """订阅主题"""
        if topic not in self._subscribers:
            self._subscribers[topic] = []
        
        self._subscribers[topic].append(callback)
        
        if topic in self._queues and topic not in self._worker_tasks:
            self._start_worker(topic)
    
    def _start_worker(self, topic: str):
        """启动工作任务处理队列消息"""
        if topic in self._worker_tasks:
            return
        
        self._worker_tasks[topic] = asyncio.create_task(self._worker(topic))
    
    async def _worker(self, topic: str):
        """工作任务函数 - 优化批处理"""
        try:
            while True:
                # 优化: 收集批量消息
                messages = []
                
                # 获取第一个消息
                first_message = await self._queues[topic].get()
                messages.append(first_message)
                start_time = time.time()
                
                # 在超时时间内尝试获取更多消息，直到达到批处理大小
                while len(messages) < self._batch_size:
                    try:
                        # 计算剩余超时时间
                        elapsed_ms = (time.time() - start_time) * 1000
                        timeout_sec = max(0, (self._batch_timeout_ms - elapsed_ms) / 1000)
                        
                        # 如果已经超时，跳出循环
                        if timeout_sec <= 0:
                            break
                            
                        # 尝试在超时内获取下一个消息
                        message = await asyncio.wait_for(
                            self._queues[topic].get(), 
                            timeout=timeout_sec
                        )
                        messages.append(message)
                    except asyncio.TimeoutError:
                        # 超时，不再等待新消息
                        break
                
                # 如果有消息并且主题有订阅者
                if messages and topic in self._subscribers:
                    batch_start = time.time()
                    
                    # 对每个订阅者处理批量消息
                    for callback in self._subscribers[topic]:
                        try:
                            # 逐个处理消息
                            for message in messages:
                                await callback(message)
                        except Exception as e:
                            logger.error(f"处理消息出错: {str(e)}", exc_info=True)
                    
                    batch_time = (time.time() - batch_start) * 1000
                    if len(messages) > 1:
                        logger.debug(f"批处理 {len(messages)} 消息完成，耗时 {batch_time:.2f}ms")
                
                # 标记所有消息为已处理
                for _ in messages:
                    self._queues[topic].task_done()
                    
        except asyncio.CancelledError:
            logger.info(f"主题 {topic} 的工作任务被取消")
        except Exception as e:
            logger.error(f"主题 {topic} 的工作任务出错: {str(e)}", exc_info=True)
            self._worker_tasks.pop(topic, None)
            self._start_worker(topic)
    
    async def stop(self):
        """停止所有队列工作"""
        for topic, task in self._worker_tasks.items():
            task.cancel()
            try:
                await task
            except asyncio.CancelledError:
                pass
        self._worker_tasks.clear()

# 全局单例
in_memory_queue = InMemoryQueue() 