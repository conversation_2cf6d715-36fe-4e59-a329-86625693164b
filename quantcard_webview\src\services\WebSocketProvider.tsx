/**
 * 🌐 全局 WebSocket Provider
 * 统一管理 WebSocket 连接，避免场景切换时的重复连接
 */

import React, { createContext, useContext, useEffect, useRef, useState } from 'react';
import { useAuth } from '../store/hooks/useAuth';
import { unifiedWebSocket } from './unifiedWebSocket';

interface WebSocketContextType {
  connected: boolean;
  connecting: boolean;
  error: string | null;
  reconnect: () => void;
}

const WebSocketContext = createContext<WebSocketContextType>({
  connected: false,
  connecting: false,
  error: null,
  reconnect: () => {}
});

export const useWebSocket = () => useContext(WebSocketContext);

interface WebSocketProviderProps {
  children: React.ReactNode;
}

export const WebSocketProvider: React.FC<WebSocketProviderProps> = ({ children }) => {
  const { isAuthenticated, isGuest, user, token } = useAuth();
  const [connected, setConnected] = useState(false);
  const [connecting, setConnecting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const connectionRef = useRef<boolean>(false);
  const cleanupRef = useRef<(() => void) | null>(null);
  
  // 连接状态缓存，避免重复连接
  const lastAuthStateRef = useRef<{
    isAuthenticated: boolean;
    isGuest: boolean;
    token: string | null;
    userId: string | undefined;
  } | null>(null);

  // WebSocket 连接函数
  const connectWebSocket = async () => {
    if (connectionRef.current || connecting) {
      console.log('🌐 WebSocket已连接或正在连接中，跳过重复连接');
      return;
    }

    setConnecting(true);
    setError(null);
    
    try {
      // 如果已有连接，先断开
      if (unifiedWebSocket.connected) {
        console.log('🌐 断开现有WebSocket连接');
        unifiedWebSocket.disconnect();
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      console.log('🌐 开始建立WebSocket连接...');
      
      if (isAuthenticated && token && !isGuest) {
        // 认证用户连接，传递用户ID
        const userId = user?.id || user?.username || 'authenticated_user';
        console.log(`🔐 使用认证用户模式连接，用户ID: ${userId}`);
        await unifiedWebSocket.connect(token, false, userId);
      } else {
        // 访客连接
        console.log('👤 使用访客模式连接');
        await unifiedWebSocket.connect(undefined, true);
      }

      // 订阅连接状态消息
      const unsubscribeConnection = unifiedWebSocket.subscribe('connection', (message) => {
        if (message.subtype === 'established') {
          console.log('✅ WebSocket连接已确认建立');
          // 防止重复设置状态
          if (!connected) {
            setConnected(true);
            setConnecting(false);
            connectionRef.current = true;
            setError(null); // 清除错误状态
          }
        }
      });

      // 订阅认证消息
      const unsubscribeAuth = unifiedWebSocket.subscribe('auth', (message) => {
        if (message.subtype === 'guest_mode') {
          console.log('👤 已进入访客模式');
        }
      });

      // 保存清理函数
      cleanupRef.current = () => {
        try {
          unsubscribeConnection();
          unsubscribeAuth();
        } catch (e) {
          console.warn('清理WebSocket订阅时出错:', e);
        }
      };

      console.log('🌐 WebSocket连接建立成功');
      
    } catch (err) {
      console.error('🌐 WebSocket连接失败:', err);
      setError(err instanceof Error ? err.message : 'WebSocket连接失败');
      setConnected(false);
      setConnecting(false);
      connectionRef.current = false;
    }
  };

  // 断开连接
  const disconnectWebSocket = () => {
    console.log('🌐 断开WebSocket连接');
    
    if (cleanupRef.current) {
      cleanupRef.current();
      cleanupRef.current = null;
    }
    
    unifiedWebSocket.disconnect();
    setConnected(false);
    setConnecting(false);
    connectionRef.current = false;
  };

  // 重连函数
  const reconnect = () => {
    console.log('🔄 手动重连WebSocket');
    disconnectWebSocket();
    setTimeout(connectWebSocket, 1000);
  };

  // 监听认证状态变化 - 优化版本
  useEffect(() => {
    const authState = {
      isAuthenticated,
      isGuest,
      hasUser: !!user,
      hasToken: !!token,
      userId: user?.id
    };
    
    console.log('🔍 认证状态变化，重新评估WebSocket连接');
    console.log(authState);
    
    // 如果状态不稳定（正在加载中），不进行连接操作
    if (isAuthenticated === undefined || isGuest === undefined) {
      console.log('⏳ 认证状态未稳定，等待状态确定');
      return;
    }
    
         // 检查是否需要重连
     const currentAuthState = {
       isAuthenticated,
       isGuest,
       token,
       userId: user?.id
     };
     
     const needReconnect = () => {
       // 如果没有连接且不在连接中，需要连接
       if (!connectionRef.current && !connecting) {
         return true;
       }
       
       // 检查状态是否真的发生了变化
       const lastState = lastAuthStateRef.current;
       if (!lastState) {
         return true; // 首次连接
       }
       
       // 比较关键状态是否变化
       const stateChanged = (
         lastState.isAuthenticated !== currentAuthState.isAuthenticated ||
         lastState.isGuest !== currentAuthState.isGuest ||
         lastState.token !== currentAuthState.token ||
         lastState.userId !== currentAuthState.userId
       );
       
       return stateChanged;
     };
    
         if (needReconnect()) {
       console.log('🔄 状态变化，需要重新建立连接');
       console.log('上次状态:', lastAuthStateRef.current);
       console.log('当前状态:', currentAuthState);
       
       // 更新状态缓存
       lastAuthStateRef.current = currentAuthState;
       
       // 清理现有连接
       disconnectWebSocket();
       
       // 延迟建立新连接，确保状态稳定
       const timer = setTimeout(connectWebSocket, 300);
       
       return () => {
         clearTimeout(timer);
       };
     } else {
       console.log('✅ 连接状态无需改变，跳过重连');
     }
  }, [isAuthenticated, isGuest, token, user?.id]); // 只监听关键状态变化

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      console.log('🧹 WebSocketProvider卸载，清理连接');
      // 延迟清理，避免快速重新挂载时的连接中断
      setTimeout(() => {
        if (!connectionRef.current) {
          disconnectWebSocket();
        }
      }, 100);
    };
  }, []);

  const contextValue: WebSocketContextType = {
    connected,
    connecting,
    error,
    reconnect
  };

  return (
    <WebSocketContext.Provider value={contextValue}>
      {children}
    </WebSocketContext.Provider>
  );
}; 