/**
 * 🎨 UI状态Hook - 简化版
 * 只导出最核心的UI导航功能
 */

import { useStore } from '../index'

// 🌐 核心UI状态Hook
export const useUIState = () => {
  const currentScene = useStore(state => state.currentScene)
  const previousScene = useStore(state => state.previousScene)
  const switchScene = useStore(state => state.switchScene)
  const goBack = useStore(state => state.goBack)
  const openModal = useStore(state => state.openModal)
  const closeModal = useStore(state => state.closeModal)

  return {
    currentScene,
    previousScene,
    switchScene,
    goBack,
    openModal,
    closeModal
  }
}
