import logging
from motor.motor_asyncio import AsyncIOMotorClient
from passlib.context import CryptContext
from app.core.config import settings
from pymongo import ASCENDING, IndexModel

logger = logging.getLogger(__name__)
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

async def init_db():
    """初始化数据库，创建管理员账号"""
    try:
        # 连接数据库
        client = AsyncIOMotorClient(settings.MONGODB_URL)
        db = client[settings.MONGODB_DB]
        
        # 检查管理员账号是否存在
        admin = await db.users.find_one({"username": "admin"})
        if not admin:
            # 创建管理员账号
            admin_user = {
                "username": "admin",
                "hashed_password": pwd_context.hash("admin123"),  # 默认密码
                "is_superuser": True,
                "is_active": True
            }
            await db.users.insert_one(admin_user)
            logger.info("管理员账号创建成功")
        else:
            logger.info("管理员账号已存在")

    except Exception as e:
        logger.error(f"初始化数据库失败: {e}")
        raise

async def init_database():
    """初始化数据库集合和索引"""
    try:
        # 连接数据库
        client = AsyncIOMotorClient(settings.MONGODB_URL)
        db = client[settings.MONGODB_DB]

        # 创建用户集合索引 - 只保留用户名唯一索引
        await db.users.create_index([("username", ASCENDING)], unique=True)

        # 创建策略模板集合
        if "strategy_templates" not in await db.list_collection_names():
            await db.create_collection("strategy_templates", validator={
                "$jsonSchema": {
                    "bsonType": "object",
                    "required": ["name", "type", "stars", "tags"],
                    "properties": {
                        "name": {"bsonType": "string"},
                        "description": {"bsonType": "string"},
                        "type": {"bsonType": "string"},
                        "category": {"bsonType": "string"},
                        "version": {"bsonType": "string"},
                        "author": {"bsonType": "string"},
                        "stars": {
                            "bsonType": "int",
                            "minimum": 1,
                            "maximum": 5,
                            "description": "策略星级 (1-5)"
                        },
                        "tags": {
                            "bsonType": "array",
                            "items": {"bsonType": "string"},
                            "description": "策略标签列表"
                        },
                        "parameters": {"bsonType": "object"},
                        "parameterGroups": {"bsonType": "object"},
                        "outputs": {"bsonType": "object"},
                        "ui": {"bsonType": "object"}
                    }
                }
            })

            # 创建索引
            await db.strategy_templates.create_indexes([
                IndexModel([("type", ASCENDING)], unique=True),
                IndexModel([("stars", ASCENDING)]),
                IndexModel([("tags", ASCENDING)])
            ])

        # 创建标签集合
        if "strategy_tags" not in await db.list_collection_names():
            await db.create_collection("strategy_tags", validator={
                "$jsonSchema": {
                    "bsonType": "object",
                    "required": ["name", "color"],
                    "properties": {
                        "name": {
                            "bsonType": "string",
                            "description": "标签名称"
                        },
                        "color": {
                            "bsonType": "string",
                            "description": "标签颜色（HEX格式）"
                        }
                    }
                }
            })

            # 创建唯一索引
            await db.strategy_tags.create_index([("name", ASCENDING)], unique=True)

        # 创建策略执行历史索引
        try:
            from app.models.strategy import StrategyExecutionHistory
            await StrategyExecutionHistory.create_indexes()
            logger.info("策略执行历史索引创建成功")
        except Exception as e:
            logger.error(f"创建策略执行历史索引失败: {e}")

        return db
        
    except Exception as e:
        logger.error(f"初始化数据库集合和索引失败: {e}")
        raise 