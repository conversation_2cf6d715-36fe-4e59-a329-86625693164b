#!/usr/bin/env python
"""
库存系统数据迁移脚本
从旧的 user_inventories + inventory_transactions 迁移到新的 user_cards 系统

用法:
    cd backend && python scripts/migrate_inventory_to_user_cards.py [--dry-run] [--backup]
"""

import asyncio
import argparse
import sys
import os
from pathlib import Path
from datetime import datetime
from collections import defaultdict

# 添加项目根目录到Python路径
current_dir = Path(__file__).parent.parent
sys.path.insert(0, str(current_dir))

from app.models.user_cards import UserCards, UserCardLog
from app.core.data.db.base import db_manager

async def backup_old_collections():
    """备份旧的集合数据"""
    try:
        db = await db_manager.get_mongodb_database("quantcard")
        
        # 备份 user_inventories
        old_inventories = []
        async for doc in db.user_inventories.find():
            old_inventories.append(doc)
        
        # 备份 inventory_transactions
        old_transactions = []
        async for doc in db.inventory_transactions.find():
            old_transactions.append(doc)
        
        # 写入备份文件
        import json
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        with open(f"backup_user_inventories_{timestamp}.json", "w", encoding='utf-8') as f:
            json.dump(old_inventories, f, default=str, ensure_ascii=False, indent=2)
        
        with open(f"backup_inventory_transactions_{timestamp}.json", "w", encoding='utf-8') as f:
            json.dump(old_transactions, f, default=str, ensure_ascii=False, indent=2)
        
        print(f"✅ 已备份 {len(old_inventories)} 条库存记录")
        print(f"✅ 已备份 {len(old_transactions)} 条交易记录")
        print(f"✅ 备份文件: backup_user_inventories_{timestamp}.json")
        print(f"✅ 备份文件: backup_inventory_transactions_{timestamp}.json")
        
        return True
        
    except Exception as e:
        print(f"❌ 备份失败: {e}")
        return False

async def migrate_user_inventories(dry_run: bool = False):
    """迁移用户库存数据"""
    try:
        db = await db_manager.get_mongodb_database("quantcard")
        
        # 聚合用户库存数据
        user_cards_data = defaultdict(lambda: {
            "cards": {},
            "total_drawn": 0,
            "total_used": 0,
            "first_acquired": None,
            "last_updated": None
        })
        
        # 处理库存记录
        inventory_count = 0
        async for inventory in db.user_inventories.find():
            user_id = inventory["user_id"]
            template_id = inventory["template_id"]
            quantity = inventory.get("quantity", 0)
            total_acquired = inventory.get("total_acquired", quantity)
            
            user_cards_data[user_id]["cards"][template_id] = quantity
            user_cards_data[user_id]["total_drawn"] += total_acquired
            
            # 跟踪时间
            acquired_at = inventory.get("acquired_at") or inventory.get("created_at")
            if acquired_at:
                if not user_cards_data[user_id]["first_acquired"] or acquired_at < user_cards_data[user_id]["first_acquired"]:
                    user_cards_data[user_id]["first_acquired"] = acquired_at
            
            updated_at = inventory.get("updated_at")
            if updated_at:
                if not user_cards_data[user_id]["last_updated"] or updated_at > user_cards_data[user_id]["last_updated"]:
                    user_cards_data[user_id]["last_updated"] = updated_at
            
            inventory_count += 1
        
        # 处理交易记录（计算使用数量）
        transaction_count = 0
        async for transaction in db.inventory_transactions.find({"type": "consume"}):
            user_id = transaction["user_id"]
            delta = abs(transaction.get("delta", 0))  # 消耗记录是负数，取绝对值
            user_cards_data[user_id]["total_used"] += delta
            transaction_count += 1
        
        print(f"📊 统计完成:")
        print(f"   - 处理了 {inventory_count} 条库存记录")
        print(f"   - 处理了 {transaction_count} 条消耗交易记录")
        print(f"   - 涉及 {len(user_cards_data)} 个用户")
        
        if dry_run:
            print("\n🔍 干运行模式 - 预览迁移数据:")
            for user_id, data in list(user_cards_data.items())[:3]:  # 只显示前3个用户
                total_cards = sum(data["cards"].values())
                print(f"   用户 {user_id[:8]}...")
                print(f"     - 卡牌种类: {len(data['cards'])}")
                print(f"     - 总卡牌数: {total_cards}")
                print(f"     - 累计抽取: {data['total_drawn']}")
                print(f"     - 累计使用: {data['total_used']}")
            return True
        
        # 实际迁移数据
        migrated_count = 0
        for user_id, data in user_cards_data.items():
            try:
                # 检查是否已存在
                existing = await UserCards.get_by_user_id(user_id)
                if existing:
                    print(f"⚠️  用户 {user_id} 已有新格式数据，跳过")
                    continue
                
                # 创建新的用户卡牌记录
                user_cards = UserCards(
                    user_id=user_id,
                    cards=data["cards"],
                    total_drawn=data["total_drawn"],
                    total_used=data["total_used"],
                    last_draw_time=data["last_updated"],
                    created_at=data["first_acquired"] or datetime.utcnow(),
                    updated_at=data["last_updated"] or datetime.utcnow()
                )
                
                await user_cards.save()
                migrated_count += 1
                
                if migrated_count % 10 == 0:
                    print(f"   已迁移 {migrated_count} 个用户...")
                
            except Exception as e:
                print(f"❌ 迁移用户 {user_id} 失败: {e}")
                continue
        
        print(f"✅ 成功迁移 {migrated_count} 个用户的库存数据")
        return True
        
    except Exception as e:
        print(f"❌ 迁移失败: {e}")
        return False

async def verify_migration():
    """验证迁移结果"""
    try:
        db = await db_manager.get_mongodb_database("quantcard")
        
        # 统计新旧数据
        old_users = set()
        async for doc in db.user_inventories.find({}, {"user_id": 1}):
            old_users.add(doc["user_id"])
        
        new_users = set()
        async for doc in db.user_cards.find({}, {"user_id": 1}):
            new_users.add(doc["user_id"])
        
        print(f"\n📋 迁移验证:")
        print(f"   - 旧系统用户数: {len(old_users)}")
        print(f"   - 新系统用户数: {len(new_users)}")
        print(f"   - 迁移覆盖率: {len(new_users)/len(old_users)*100:.1f}%" if old_users else "100%")
        
        # 抽样验证
        sample_user = next(iter(new_users)) if new_users else None
        if sample_user:
            # 检查旧数据
            old_total = 0
            async for doc in db.user_inventories.find({"user_id": sample_user}):
                old_total += doc.get("quantity", 0)
            
            # 检查新数据
            new_doc = await db.user_cards.find_one({"user_id": sample_user})
            new_total = sum(new_doc["cards"].values()) if new_doc else 0
            
            print(f"\n🔍 抽样验证 (用户 {sample_user[:8]}...):")
            print(f"   - 旧系统总卡牌: {old_total}")
            print(f"   - 新系统总卡牌: {new_total}")
            print(f"   - 数据一致性: {'✅' if old_total == new_total else '❌'}")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="迁移库存系统数据")
    parser.add_argument("--dry-run", action="store_true", help="干运行模式，仅预览不实际迁移")
    parser.add_argument("--backup", action="store_true", help="迁移前备份旧数据")
    parser.add_argument("--verify-only", action="store_true", help="仅验证迁移结果")
    
    return parser.parse_args()

async def main():
    """主函数"""
    args = parse_args()
    
    print("🚀 库存系统数据迁移工具")
    print("=" * 50)
    
    if args.verify_only:
        print("🔍 验证迁移结果...")
        await verify_migration()
        return
    
    if not args.dry_run:
        confirm = input("\n⚠️  这将修改数据库数据，确认继续? (y/n): ")
        if confirm.lower() != 'y':
            print("已取消迁移")
            return
    
    # 备份旧数据
    if args.backup:
        print("\n📦 备份旧数据...")
        backup_success = await backup_old_collections()
        if not backup_success:
            print("❌ 备份失败，迁移中止")
            return
    
    # 执行迁移
    print(f"\n🔄 开始迁移 {'(干运行)' if args.dry_run else ''}...")
    migration_success = await migrate_user_inventories(args.dry_run)
    
    if migration_success and not args.dry_run:
        print("\n🔍 验证迁移结果...")
        await verify_migration()
        print("\n✅ 迁移完成！")
        print("\n📌 下一步:")
        print("   1. 测试新的库存系统功能")
        print("   2. 确认无误后可考虑删除旧的 user_inventories 和 inventory_transactions 集合")
    elif args.dry_run:
        print("\n✅ 干运行完成！使用 --verify-only 可以验证已有的迁移数据")
    else:
        print("\n❌ 迁移失败")

if __name__ == "__main__":
    asyncio.run(main()) 