// 定义消息类型接口
interface WebSocketMessage {
  type: string;
  [key: string]: any;
}

interface ConnectionStatus {
  connected: boolean;
}

type MessageHandler = (data: any) => void;

// 应用退避算法的重连管理器
class ReconnectManager {
  private attempts = 0;
  private readonly maxAttempts: number;
  private readonly baseDelay: number;
  private readonly maxDelay: number;
  private timer: any = null;
  
  constructor(maxAttempts = 5, baseDelay = 1000, maxDelay = 30000) {
    this.maxAttempts = maxAttempts;
    this.baseDelay = baseDelay;
    this.maxDelay = maxDelay;
  }
  
  public schedule(callback: () => void): boolean {
    if (this.attempts >= this.maxAttempts) {
      console.log('已达到最大重连次数，停止尝试');
      return false;
    }
    
    this.attempts++;
    
    // 指数退避算法
    const delay = Math.min(
      this.maxDelay,
      this.baseDelay * Math.pow(1.5, this.attempts - 1)
    );
    
    console.log(`将在 ${delay}ms 后尝试重新连接 (尝试 ${this.attempts}/${this.maxAttempts})`);
    
    clearTimeout(this.timer);
    this.timer = setTimeout(() => {
      callback();
    }, delay);
    
    return true;
  }
  
  public reset(): void {
    this.attempts = 0;
    clearTimeout(this.timer);
  }
  
  public stop(): void {
    clearTimeout(this.timer);
  }
}

export class WebSocketClient {
  private socket: WebSocket | null = null;
  private readonly url: string;
  private messageHandlers: Map<string, Set<MessageHandler>> = new Map();
  private isConnecting = false;
  private reconnectManager: ReconnectManager;
  
  constructor(url: string) {
    this.url = url;
    this.reconnectManager = new ReconnectManager();
    this.connect();
  }
  
  private connect() {
    if (this.socket?.readyState === WebSocket.OPEN || this.isConnecting) {
      return;
    }
    
    this.isConnecting = true;
    
    try {
      this.socket = new WebSocket(this.url);
      
      this.socket.onopen = this.handleOpen.bind(this);
      this.socket.onmessage = this.handleMessage.bind(this);
      this.socket.onclose = this.handleClose.bind(this);
      this.socket.onerror = this.handleError.bind(this);
    } catch (error) {
      console.error('WebSocket连接错误:', error);
      this.scheduleReconnect();
    }
  }
  
  private handleOpen(event: Event) {
    console.log('WebSocket连接已建立');
    this.isConnecting = false;
    this.reconnectManager.reset();
    
    // 触发连接成功的事件处理
    this.notifyHandlers('connection', { connected: true });
  }
  
  private handleMessage(event: MessageEvent) {
    try {
      const data = JSON.parse(event.data) as WebSocketMessage;
      const messageType = data.type;
      
      // 确保在收到消息时更新连接状态
      if (this.socket?.readyState === WebSocket.OPEN) {
        this.notifyHandlers('connection', { connected: true });
      }
      
      if (messageType) {
        // 通知特定类型的处理器
        this.notifyHandlers(messageType, data);
        
        // 执行通用处理
        this.notifyHandlers('*', data);
      }
    } catch (error) {
      console.error('解析WebSocket消息时出错:', error);
    }
  }
  
  private handleClose(event: CloseEvent) {
    this.isConnecting = false;
    console.log(`WebSocket连接已关闭: 代码=${event.code}, 原因=${event.reason}`);
    
    // 触发连接关闭的事件处理
    this.notifyHandlers('connection', { connected: false });
    
    // 如果不是正常关闭，尝试重连
    if (event.code !== 1000) {
      this.scheduleReconnect();
    }
  }
  
  private handleError(event: Event) {
    this.isConnecting = false;
    console.error('WebSocket连接发生错误:', event);
    this.scheduleReconnect();
  }
  
  private scheduleReconnect() {
    this.reconnectManager.schedule(() => {
      this.connect();
    });
  }
  
  private notifyHandlers(type: string, data: any): void {
    const handlers = this.messageHandlers.get(type);
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(data);
        } catch (e) {
          console.error(`执行处理器出错 (类型: ${type}):`, e);
        }
      });
    }
  }
  
  public on(messageType: string, handler: MessageHandler): () => void {
    if (!this.messageHandlers.has(messageType)) {
      this.messageHandlers.set(messageType, new Set());
    }
    
    this.messageHandlers.get(messageType)!.add(handler);
    
    // 返回取消订阅函数
    return () => this.off(messageType, handler);
  }
  
  public off(messageType: string, handler?: MessageHandler): void {
    if (!handler) {
      // 如果没有提供处理函数，移除该消息类型的所有处理函数
      this.messageHandlers.delete(messageType);
    } else {
      // 移除特定的处理函数
      const handlers = this.messageHandlers.get(messageType);
      if (handlers) {
        handlers.delete(handler);
        if (handlers.size === 0) {
          this.messageHandlers.delete(messageType);
        }
      }
    }
  }
  
  public send(data: any): void {
    if (this.socket?.readyState === WebSocket.OPEN) {
      this.socket.send(JSON.stringify(data));
    } else {
      console.error('无法发送消息：WebSocket未连接');
    }
  }
  
  public close(): void {
    if (this.socket) {
      this.socket.close(1000, '客户端主动关闭');
    }
    
    this.reconnectManager.stop();
    this.messageHandlers.clear();
  }
  
  public isConnected(): boolean {
    return this.socket?.readyState === WebSocket.OPEN;
  }
  
  // 强制检查连接状态并通知订阅者
  public checkConnection(): boolean {
    const connected = this.isConnected();
    this.notifyHandlers('connection', { connected });
    return connected;
  }
}

// 全局单例管理器
class WebSocketManager {
  private websockets: Map<string, WebSocketClient> = new Map();
  
  // 获取策略WebSocket实例 - 单一用户连接模式
  public getStrategyWebSocket(strategyId?: string): WebSocketClient {
    const token = localStorage.getItem('token') || '';
    const wsProtocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    
    // 修改：自动获取API主机地址，支持跨设备访问
    let apiHost;
    const hostName = window.location.hostname;
    if (process.env.NODE_ENV === 'development') {
      // 开发环境：优先使用当前主机名+端口
      apiHost = `${hostName}:8000`;
    } else {
      // 生产环境：使用当前主机
      apiHost = window.location.host;
    }
    
    // 使用用户级别的WebSocket连接
    const url = `${wsProtocol}//${apiHost}/ws/strategy?token=${token}`;
    const key = 'strategy:global';
    
    if (!this.websockets.has(key)) {
      this.websockets.set(key, new WebSocketClient(url));
    }
    
    return this.websockets.get(key)!;
  }
  
  // 获取公共WebSocket实例
  public getPublicWebSocket(): WebSocketClient {
    const key = 'public';
    
    if (!this.websockets.has(key)) {
      const wsProtocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
      // 修改：自动获取API主机地址，支持跨设备访问
      let apiHost;
      const hostName = window.location.hostname;
      if (process.env.NODE_ENV === 'development') {
        // 开发环境：优先使用当前主机名+端口
        apiHost = `${hostName}:8000`;
      } else {
        // 生产环境：使用当前主机
        apiHost = window.location.host;
      }
      const url = `${wsProtocol}//${apiHost}/ws/public`;
      this.websockets.set(key, new WebSocketClient(url));
    }
    
    return this.websockets.get(key)!;
  }
  
  // 关闭所有连接
  public closeAll(): void {
    this.websockets.forEach(ws => ws.close());
    this.websockets.clear();
  }
}

// 导出单例
const webSocketManager = new WebSocketManager();

// 兼容旧API
export function getStrategyWebSocket(strategyId?: string): WebSocketClient {
  return webSocketManager.getStrategyWebSocket(strategyId);
}

export function getPublicWebSocket(): WebSocketClient {
  return webSocketManager.getPublicWebSocket();
}

export default webSocketManager; 