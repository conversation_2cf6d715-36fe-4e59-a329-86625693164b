@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600;700&display=swap');

/* 重置样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* 全局样式 */
body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  line-height: 1.5;
  color: #1e293b;
}

code {
  font-family: 'JetBrains Mono', monospace;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-thumb {
  background: #d9d9d9;
  border-radius: 3px;
}

::-webkit-scrollbar-track {
  background: #f0f2f5;
  border-radius: 3px;
}

/* 链接样式 */
a {
  color: #6366f1;
  text-decoration: none;
  transition: color 0.2s ease-in-out;
}

a:hover {
  color: #4f46e5;
}

/* 动画类 */
.fade-enter {
  opacity: 0;
}

.fade-enter-active {
  opacity: 1;
  transition: opacity 200ms ease-in;
}

.fade-exit {
  opacity: 1;
}

.fade-exit-active {
  opacity: 0;
  transition: opacity 200ms ease-in;
}

/* 响应式布局 */
@media screen and (max-width: 768px) {
  html {
    font-size: 14px;
  }
}

/* 暗色模式 */
@media (prefers-color-scheme: dark) {
  body {
    background-color: #0f172a;
    color: #e2e8f0;
  }

  ::-webkit-scrollbar-thumb {
    background: #475569;
  }

  ::-webkit-scrollbar-track {
    background: #1e293b;
  }
}
