import React, { useState, useCallback, useEffect, useRef } from 'react';
import {
  create<PERSON><PERSON>,
  IChartApi,
  ColorType,
  CrosshairMode,
  LineStyle,
  Time,
  UTCTimestamp,
  DeepPartial,
  PriceScaleOptions,
} from 'lightweight-charts';
import { Typography, Spin, Button, Space, Tooltip, Radio, Select, Divider } from 'antd';
import { 
  CloseOutlined, 
  FullscreenOutlined,
  FullscreenExitOutlined,
  LineChartOutlined,
  ReloadOutlined,
  ZoomInOutlined,
  ZoomOutOutlined,
  EditOutlined,
  PushpinOutlined,
  HighlightOutlined,
  ScissorOutlined,
} from '@ant-design/icons';
import './CyberpunkCandlestick.scss';

const { Text } = Typography;
const { Option } = Select;

export interface CandlestickData {
  time: string;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
  ma5?: number;
  ma10?: number;
  ma20?: number;
  macd?: number;
  signal?: number;
  histogram?: number;
}

interface CyberpunkCandlestickProps {
  data: CandlestickData[];
  height?: number;
  showVolume?: boolean;
  showMACD?: boolean;
  showMA?: boolean;
  loading?: boolean;
  title?: string;
  showTitle?: boolean;
  onCandleClick?: (data: CandlestickData) => void;
  onClose?: () => void;
  onRefresh?: () => void;
}

type TimeFrame = '1m' | '5m' | '15m' | '30m' | '1h' | '4h' | '1d' | '1w' | '1M';

const CyberpunkCandlestick: React.FC<CyberpunkCandlestickProps> = ({
  data,
  height = 400,
  showVolume = true,
  showMACD = true,
  showMA = true,
  loading = false,
  title,
  showTitle = true,
  onCandleClick,
  onClose,
  onRefresh,
}) => {
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isClosing, setIsClosing] = useState(false);
  const [timeFrame, setTimeFrame] = useState<TimeFrame>('1d');
  const [zoomLevel, setZoomLevel] = useState(1);
  const [showIndicators, setShowIndicators] = useState({
    ma: true,
    volume: true,
    macd: false
  });
  const [activeToolType, setActiveToolType] = useState<'none' | 'trendline' | 'annotation' | 'measure'>('none');
  const [tooltipData, setTooltipData] = useState<{
    visible: boolean;
    x: number;
    y: number;
    data: any;
    time: string;
  } | null>(null);

  const chartContainerRef = useRef<HTMLDivElement>(null);
  const chartRef = useRef<IChartApi | null>(null);
  const resizeObserverRef = useRef<ResizeObserver | null>(null);

  const handleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  const handleClose = () => {
    setIsClosing(true);
    setTimeout(() => {
      onClose?.();
    }, 300);
  };

  const handleZoomIn = () => {
    if (chartRef.current) {
      const timeScale = chartRef.current.timeScale();
      timeScale.applyOptions({
        rightOffset: Math.max(0, timeScale.scrollPosition() - 5),
      });
    }
  };

  const handleZoomOut = () => {
    if (chartRef.current) {
      const timeScale = chartRef.current.timeScale();
      timeScale.applyOptions({
        rightOffset: timeScale.scrollPosition() + 5,
      });
    }
  };

  // 处理工具选择
  const handleToolSelect = (toolType: 'none' | 'trendline' | 'annotation' | 'measure') => {
    setActiveToolType(toolType === activeToolType ? 'none' : toolType);
  };

  useEffect(() => {
    if (!chartContainerRef.current) return;

    let chart: IChartApi | null = null;

    // 确保容器已经挂载并且有有效的尺寸
    const containerRect = chartContainerRef.current.getBoundingClientRect();
    if (containerRect.width === 0 || containerRect.height === 0) return;

    try {
      // 创建图表实例
      chart = createChart(chartContainerRef.current, {
        width: containerRect.width,
        height: isFullscreen ? window.innerHeight - 200 : height,
        layout: {
          background: { 
            color: '#13141b' as ColorType,
          },
          textColor: '#8be9fd' as ColorType,
          fontSize: 12,
          fontFamily: 'JetBrains Mono',
        },
        grid: {
          vertLines: {
            color: 'rgba(98, 114, 164, 0.08)' as ColorType,
            style: LineStyle.Dotted,
          },
          horzLines: {
            color: 'rgba(98, 114, 164, 0.08)' as ColorType,
            style: LineStyle.Dotted,
          },
        },
        crosshair: {
          mode: CrosshairMode.Normal,
          vertLine: {
            color: '#ff79c6' as ColorType,
            width: 1,
            style: LineStyle.Dashed,
            labelBackgroundColor: '#13141b' as ColorType,
            visible: true,
            labelVisible: true,
          },
          horzLine: {
            color: '#ff79c6' as ColorType,
            width: 1,
            style: LineStyle.Dashed,
            labelBackgroundColor: '#13141b' as ColorType,
            visible: true,
            labelVisible: true,
          },
        },
        timeScale: {
          borderColor: '#2b2d3a' as ColorType,
          timeVisible: true,
          secondsVisible: false,
          borderVisible: true,
          tickMarkFormatter: (time: number) => {
            const date = new Date(time * 1000);
            return date.toLocaleTimeString('zh-CN', { 
              hour: '2-digit', 
              minute: '2-digit',
              hour12: false 
            });
          },
        },
        rightPriceScale: {
          borderColor: '#2b2d3a' as ColorType,
          borderVisible: true,
          scaleMargins: {
            top: 0.1,
            bottom: 0.2,
          },
        },
        handleScale: {
          mouseWheel: true,
          pinch: true,
          axisPressedMouseMove: {
            time: true,
            price: true,
          },
        },
        handleScroll: {
          mouseWheel: true,
          pressedMouseMove: true,
          horzTouchDrag: true,
          vertTouchDrag: true,
        },
      });

      // 格式化时间戳为 UTCTimestamp
      const formatTimeToUTC = (timeStr: string): UTCTimestamp => {
        return (new Date(timeStr).getTime() / 1000) as UTCTimestamp;
      };

      // 添加蜡烛图系列
      const candlestickSeries = chart.addCandlestickSeries({
        upColor: '#00ffa3' as ColorType,
        downColor: '#ff3358' as ColorType,
        borderVisible: true,
        borderColor: '#2b2d3a' as ColorType,
        borderUpColor: '#00ffa3' as ColorType,
        borderDownColor: '#ff3358' as ColorType,
        wickUpColor: '#00ffa3' as ColorType,
        wickDownColor: '#ff3358' as ColorType,
        priceFormat: {
          type: 'price',
          precision: 2,
          minMove: 0.01,
        },
        lastValueVisible: true,
        priceLineVisible: true,
        priceLineWidth: 1,
        priceLineColor: '#8be9fd',
        priceLineStyle: LineStyle.Dotted,
      });

      // 格式化数据
      const formattedData = data.map(item => ({
        time: formatTimeToUTC(item.time),
        open: item.open,
        high: item.high,
        low: item.low,
        close: item.close,
      }));

      candlestickSeries.setData(formattedData);

      // 添加成交量
      if (showVolume) {
        const volumeSeries = chart.addHistogramSeries({
          color: '#bd93f9' as ColorType,
          priceFormat: {
            type: 'volume',
            precision: 0,
          },
          priceScaleId: 'volume',
          scaleMargins: {
            top: 0.8,
            bottom: 0,
          },
        });

        const volumeScaleOptions: DeepPartial<PriceScaleOptions> = {
          scaleMargins: {
            top: 0.8,
            bottom: 0,
          },
          borderVisible: true,
          borderColor: '#44475a' as ColorType,
        };

        chart.priceScale('volume').applyOptions(volumeScaleOptions);

        const volumeData = data.map(item => ({
          time: formatTimeToUTC(item.time),
          value: item.volume,
          color: item.close >= item.open ? 'rgba(0, 255, 163, 0.15)' : 'rgba(255, 51, 88, 0.15)',
        }));

        volumeSeries.setData(volumeData);
      }

      // 添加移动平均线
      if (showMA) {
        if (data[0]?.ma5) {
          const ma5Series = chart.addLineSeries({
            color: '#8be9fd' as ColorType,
            lineWidth: 2,
            priceLineVisible: false,
            crosshairMarkerVisible: true,
            crosshairMarkerRadius: 4,
            lineStyle: LineStyle.Solid,
            title: 'MA5',
          });

          const ma5Data = data.map(item => ({
            time: formatTimeToUTC(item.time),
            value: item.ma5 || 0,
          }));

          ma5Series.setData(ma5Data);
        }

        if (data[0]?.ma10) {
          const ma10Series = chart.addLineSeries({
            color: '#ff79c6' as ColorType,
            lineWidth: 2,
            priceLineVisible: false,
            crosshairMarkerVisible: true,
            crosshairMarkerRadius: 4,
            lineStyle: LineStyle.Solid,
            title: 'MA10',
          });

          const ma10Data = data.map(item => ({
            time: formatTimeToUTC(item.time),
            value: item.ma10 || 0,
          }));

          ma10Series.setData(ma10Data);
        }

        if (data[0]?.ma20) {
          const ma20Series = chart.addLineSeries({
            color: '#f1fa8c' as ColorType,
            lineWidth: 2,
            priceLineVisible: false,
            crosshairMarkerVisible: true,
            crosshairMarkerRadius: 4,
            lineStyle: LineStyle.Solid,
            title: 'MA20',
          });

          const ma20Data = data.map(item => ({
            time: formatTimeToUTC(item.time),
            value: item.ma20 || 0,
          }));

          ma20Series.setData(ma20Data);
        }
      }

      // 添加点击事件
      chart.subscribeClick((param: any) => {
        if (param.time && onCandleClick) {
          const clickedData = data.find(
            item => formatTimeToUTC(item.time) === param.time
          );
          if (clickedData) {
            onCandleClick(clickedData);
          }
        }
      });

      // 订阅十字光标移动事件
      const crosshairHandler = (param: any) => {
        console.log('Crosshair param:', param); // 添加调试日志
        
        if (param.point && param.time) {
          // 从原始数据中查找对应的数据点
          const currentData = data.find(
            item => formatTimeToUTC(item.time) === param.time
          );

          console.log('Current data:', currentData); // 添加调试日志
          
          if (currentData) {
            // 计算tooltip位置 - 修改这里使tooltip显示在鼠标附近
            const toolTipWidth = 160;
            const toolTipHeight = showMA ? 220 : 140;
            const toolTipMargin = 10;
            
            // 获取当前鼠标位置
            const mouseX = param.point.x;
            const mouseY = param.point.y;
            
            // 获取图表容器的位置和尺寸信息
            const containerRect = chartContainerRef.current?.getBoundingClientRect();
            if (!containerRect) return;
            
            // 计算tooltip位置，使它显示在鼠标右侧，如果右侧空间不足则显示在左侧
            // 由于我们现在使用fixed定位，考虑相对于视口而非容器
            let left = mouseX;
            let top = mouseY - toolTipHeight / 2; // 垂直居中对齐鼠标位置
            
            // 确保tooltip不超出视口边界
            if (left + toolTipWidth > containerRect.width) {
              left = mouseX - toolTipWidth - toolTipMargin;
            } else {
              left = mouseX + toolTipMargin;
            }
            
            if (top < 0) {
              top = toolTipMargin;
            } else if (top + toolTipHeight > containerRect.height) {
              top = containerRect.height - toolTipHeight - toolTipMargin;
            }

            const time = new Date(param.time * 1000);
            const formattedTime = time.toLocaleTimeString('zh-CN', {
              hour: '2-digit',
              minute: '2-digit',
              hour12: false
            });
            const formattedDate = time.toLocaleDateString('zh-CN', {
              year: 'numeric',
              month: '2-digit',
              day: '2-digit'
            });

            setTooltipData({
              visible: true,
              x: left,
              y: top,
              data: currentData,
              time: `${formattedDate} ${formattedTime}`
            });
          }
        } else {
          setTooltipData(null);
        }
      };

      chart.subscribeCrosshairMove(crosshairHandler);

      // 设置图表引用
      chartRef.current = chart;

      // 创建 ResizeObserver
      const resizeObserver = new ResizeObserver(entries => {
        if (entries[0] && chartRef.current) {
          const { width, height } = entries[0].contentRect;
          if (width > 0 && height > 0) {  // 确保尺寸有效
            chartRef.current.applyOptions({ width, height });
            chartRef.current.timeScale().fitContent();
          }
        }
      });

      // 观察容器大小变化
      resizeObserver.observe(chartContainerRef.current);
      resizeObserverRef.current = resizeObserver;

      // 清理函数
      return () => {
        if (resizeObserver) {
          resizeObserver.disconnect();
        }
        if (chart) {
          chart.remove();
        }
        chartRef.current = null;
      };
    } catch (error) {
      console.error('图表创建错误:', error);
    }
  }, [data, height, isFullscreen, showVolume, showMA]);

  // 处理键盘事件
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!chartRef.current) return;

      switch (e.key) {
        case 'ArrowLeft':
          // 向左移动
          chartRef.current.timeScale().scrollToPosition(
            chartRef.current.timeScale().scrollPosition() + 1,
            false
          );
          break;
        case 'ArrowRight':
          // 向右移动
          chartRef.current.timeScale().scrollToPosition(
            chartRef.current.timeScale().scrollPosition() - 1,
            false
          );
          break;
        case 'ArrowUp':
          // 放大
          handleZoomIn();
          break;
        case 'ArrowDown':
          // 缩小
          handleZoomOut();
          break;
        case 'Escape':
          // 退出全屏
          if (isFullscreen) {
            setIsFullscreen(false);
          }
          break;
        case ' ':
          // 切换指标显示
          e.preventDefault();
          setShowIndicators(prev => ({
            ma: !prev.ma,
            volume: prev.volume,
            macd: prev.macd
          }));
          break;
      }
    };

    if (isFullscreen) {
      window.addEventListener('keydown', handleKeyDown);
    }

    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [isFullscreen, handleZoomIn, handleZoomOut]);

  // 处理手势事件
  useEffect(() => {
    if (!chartContainerRef.current) return;

    let touchStartTime = 0;
    let touchStartPosition = { x: 0, y: 0 };
    let isLongPress = false;
    const longPressDelay = 500;

    const handleTouchStart = (e: TouchEvent) => {
      if (e.touches.length === 1) {
        touchStartTime = Date.now();
        touchStartPosition = {
          x: e.touches[0].clientX,
          y: e.touches[0].clientY
        };

        // 设置长按定时器
        setTimeout(() => {
          const touchDuration = Date.now() - touchStartTime;
          if (touchDuration >= longPressDelay) {
            isLongPress = true;
            // 显示详细信息
            // TODO: 实现长按显示详细信息的逻辑
          }
        }, longPressDelay);
      }
    };

    const handleTouchEnd = (e: TouchEvent) => {
      const touchDuration = Date.now() - touchStartTime;
      
      if (touchDuration < longPressDelay && !isLongPress) {
        // 检测双击
        if (touchDuration < 300 && e.touches.length === 0) {
          // 双击放大特定区域
          if (chartRef.current) {
            const timeScale = chartRef.current.timeScale();
            timeScale.fitContent();
          }
        }
      }
      
      isLongPress = false;
    };

    const handleDoubleTap = (e: TouchEvent) => {
      e.preventDefault();
      if (chartRef.current) {
        const timeScale = chartRef.current.timeScale();
        timeScale.fitContent();
      }
    };

    chartContainerRef.current.addEventListener('touchstart', handleTouchStart, true);
    chartContainerRef.current.addEventListener('touchend', handleTouchEnd, true);
    chartContainerRef.current.addEventListener('dblclick', handleDoubleTap as any);

    return () => {
      if (chartContainerRef.current) {
        chartContainerRef.current.removeEventListener('touchstart', handleTouchStart, true);
        chartContainerRef.current.removeEventListener('touchend', handleTouchEnd, true);
        chartContainerRef.current.removeEventListener('dblclick', handleDoubleTap as any);
      }
    };
  }, []);

  return (
    <div 
      className={`cyberpunk-candlestick-chart ${isFullscreen ? 'fullscreen' : ''} ${isClosing ? 'closing' : ''}`}
      style={{ height: isFullscreen ? '100%' : height }}
      ref={chartContainerRef}
    >
      <div className="chart-header">
        {showTitle && title ? (
          <Text className="chart-title" strong>{title}</Text>
        ) : <div className="chart-title-placeholder"></div>}
        
        <Space className="chart-tools">
          <Radio.Group 
            value={timeFrame} 
            onChange={(e) => setTimeFrame(e.target.value)}
            size="small"
            className="time-select"
          >
            <Radio.Button value="1d">日K</Radio.Button>
            <Radio.Button value="1h">小时</Radio.Button>
            <Radio.Button value="15m">15分</Radio.Button>
          </Radio.Group>
          
          <Tooltip title="缩小">
            <Button 
              type="text" 
              icon={<ZoomOutOutlined />} 
              size="small"
              onClick={handleZoomOut}
            />
          </Tooltip>
          
          <Tooltip title="放大">
            <Button 
              type="text" 
              icon={<ZoomInOutlined />} 
              size="small"
              onClick={handleZoomIn}
            />
          </Tooltip>
          
          <Tooltip title="趋势线">
            <Button 
              type={activeToolType === 'trendline' ? 'primary' : 'text'} 
              icon={<LineChartOutlined />} 
              size="small"
              onClick={() => handleToolSelect('trendline')}
            />
          </Tooltip>
          
          <Tooltip title="标注">
            <Button 
              type={activeToolType === 'annotation' ? 'primary' : 'text'} 
              icon={<HighlightOutlined />} 
              size="small"
              onClick={() => handleToolSelect('annotation')}
            />
          </Tooltip>
          
          <Tooltip title="测量">
            <Button 
              type={activeToolType === 'measure' ? 'primary' : 'text'} 
              icon={<ScissorOutlined />} 
              size="small"
              onClick={() => handleToolSelect('measure')}
            />
          </Tooltip>
          
          {onRefresh && (
            <Tooltip title="刷新">
              <Button 
                type="text" 
                icon={<ReloadOutlined />} 
                size="small"
                onClick={onRefresh}
              />
            </Tooltip>
          )}
          
          <Tooltip title={isFullscreen ? "退出全屏" : "全屏"}>
            <Button 
              type="text" 
              icon={isFullscreen ? <FullscreenExitOutlined /> : <FullscreenOutlined />} 
              size="small"
              onClick={handleFullscreen}
            />
          </Tooltip>
          
          {onClose && (
            <Tooltip title="关闭">
              <Button 
                type="text" 
                icon={<CloseOutlined />} 
                size="small"
                onClick={handleClose}
              />
            </Tooltip>
          )}
        </Space>
      </div>
      
      <div 
        className="chart-content" 
        style={{ height: `calc(100% - ${showTitle ? '34px' : '32px'})` }}
      >
        {loading ? (
          <div className="loading-container">
            <Spin size="large" />
          </div>
        ) : null}
      </div>
      
      {tooltipData && tooltipData.visible && (
        <div 
          className="chart-tooltip"
          style={{
            position: 'fixed',
            left: (chartContainerRef.current?.getBoundingClientRect().left || 0) + tooltipData.x + 'px',
            top: (chartContainerRef.current?.getBoundingClientRect().top || 0) + tooltipData.y + 'px',
            zIndex: 9999,
            pointerEvents: 'none'
          }}
        >
          <div className="tooltip-time">{tooltipData.time}</div>
          <div className="tooltip-price">
            <div><span>开:</span> {tooltipData.data.open.toFixed(2)}</div>
            <div><span>高:</span> {tooltipData.data.high.toFixed(2)}</div>
            <div><span>低:</span> {tooltipData.data.low.toFixed(2)}</div>
            <div><span>收:</span> {tooltipData.data.close.toFixed(2)}</div>
          </div>
          <div className="tooltip-volume">
            <div><span>量:</span> {tooltipData.data.volume.toLocaleString()}</div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CyberpunkCandlestick; 
