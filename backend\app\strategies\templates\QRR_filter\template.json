{"template_id": "QRR_filter", "name": "量比", "description": "根据设定的量比条件筛选股票，支持区间范围筛选。例：量比小于1。", "version": "2.0.0", "author": "quantcard", "stars": 1, "tags": ["选股", "基础筛选"], "parameters": {"operator": {"type": "select", "label": "条件", "description": "量比比较条件", "default": "大于", "options": [{"label": "大于", "value": "大于"}, {"label": "小于", "value": "小于"}, {"label": "区间", "value": "区间"}]}, "qrrMin": {"type": "number", "label": "下限", "description": "量比下限", "default": 10, "validation": {"required": true, "min": 0, "max": 10000}}, "qrrMax": {"type": "number", "label": "上限", "description": "量比上限，仅在'区间'模式下使用", "default": null, "validation": {"min": 0, "max": 10000}, "visibleWhen": {"param": "operator", "in": ["区间"]}}}, "parameterGroups": {"QRR_filter": {"parameters": ["operator", "qrrMin", "qrrMax"], "displayMode": "inline", "prefix": "量比", "separator": "-", "layout": "horizontal"}}, "ui": {"icon": "filter", "color": "#1890ff", "category": "筛选", "order": 1, "form": {"layout": "horizontal", "compact": true, "showDescription": false}}}