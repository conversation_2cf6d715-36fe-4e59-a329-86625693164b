"""
简化版缓存服务

专为市场数据优化的内存缓存实现，移除多余抽象层
"""
import logging
import time
import asyncio
from typing import Dict, Any, Callable, Awaitable, Optional

logger = logging.getLogger(__name__)

class SimpleCacheService:
    """简化版缓存服务，使用内存缓存"""
    
    def __init__(self):
        """初始化缓存服务"""
        self._cache = {
            "quotes": {},  # 行情数据缓存
            "general": {}  # 通用数据缓存
        }
        self._timestamps = {
            "quotes": {},  # 行情数据时间戳
            "general": {}  # 通用数据时间戳
        }
        self._lock = asyncio.Lock()
        logger.info("缓存服务已初始化")
    
    async def close(self):
        """关闭缓存服务"""
        self._cache.clear()
        self._timestamps.clear()
        logger.info("缓存服务已关闭")
        
    async def get(self, key: str, cache_type: str = "general") -> Optional[Any]:
        """
        获取缓存数据
        
        Args:
            key: 缓存键
            cache_type: 缓存类型，默认为general
            
        Returns:
            缓存数据，如果不存在则返回None
        """
        if cache_type not in self._cache:
            return None
            
        return self._cache[cache_type].get(key)
        
    async def set(self, key: str, value: Any, ttl: int = 300, cache_type: str = "general") -> None:
        """
        设置缓存数据
        
        Args:
            key: 缓存键
            value: 缓存值
            ttl: 过期时间(秒)，默认5分钟
            cache_type: 缓存类型，默认为general
        """
        if cache_type not in self._cache:
            self._cache[cache_type] = {}
            self._timestamps[cache_type] = {}
            
        self._cache[cache_type][key] = value
        self._timestamps[cache_type][key] = time.time() + ttl
        
    async def delete(self, key: str, cache_type: str = "general") -> None:
        """
        删除缓存数据
        
        Args:
            key: 缓存键
            cache_type: 缓存类型，默认为general
        """
        if cache_type in self._cache and key in self._cache[cache_type]:
            del self._cache[cache_type][key]
            
        if cache_type in self._timestamps and key in self._timestamps[cache_type]:
            del self._timestamps[cache_type][key]
            
    async def clear(self, cache_type: Optional[str] = None) -> None:
        """
        清空缓存
        
        Args:
            cache_type: 要清空的缓存类型，为None则清空所有缓存
        """
        if cache_type:
            if cache_type in self._cache:
                self._cache[cache_type].clear()
                self._timestamps[cache_type].clear()
        else:
            for cache_type in self._cache:
                self._cache[cache_type].clear()
                self._timestamps[cache_type].clear()
    
    async def get_or_compute(
        self,
        key: str,
        computer: Callable[[], Awaitable[Any]], 
        ttl: int = 300, 
        cache_type: str = "general"
    ) -> Any:
        """
        获取缓存数据，如果不存在则计算并缓存
        
        Args:
            key: 缓存键
            computer: 计算函数，用于获取数据
            ttl: 过期时间(秒)，默认5分钟
            cache_type: 缓存类型，默认为general
            
        Returns:
            缓存数据或计算结果
        """
        # 检查是否有缓存且未过期
        cached_value = await self.get(key, cache_type)
        if cached_value is not None and cache_type in self._timestamps:
            timestamp = self._timestamps[cache_type].get(key, 0)
            if timestamp > time.time():
                logger.debug(f"缓存命中: {key}")
                return cached_value
                
        # 缓存不存在或已过期，需要重新计算
        # 使用锁防止并发计算导致的重复请求
        async with self._lock:
            # 双重检查，避免等待锁期间已有其他线程更新了缓存
            cached_value = await self.get(key, cache_type)
            if cached_value is not None and cache_type in self._timestamps:
                timestamp = self._timestamps[cache_type].get(key, 0)
                if timestamp > time.time():
                    return cached_value
        
        # 计算新值
        try:
            value = await computer()
            # 缓存计算结果
            if value is not None:
                await self.set(key, value, ttl, cache_type)
            return value
        except Exception as e:
            logger.error(f"计算缓存值失败: {str(e)}")
            # 如果有过期的旧值，仍然返回
            if cached_value is not None:
                logger.warning(f"返回过期的缓存值: {key}")
                return cached_value
            raise
                
    async def store_all_quotes(self, quotes_dict: Dict[str, Any], ttl: int = 300) -> None:
        """
        存储全量行情数据
        
        Args:
            quotes_dict: 股票代码到行情数据的映射
            ttl: 过期时间(秒)，默认5分钟
        """
        async with self._lock:
            # 更新全部行情数据
            self._cache["quotes"] = quotes_dict.copy()
            
            # 更新所有时间戳
            expiry_time = time.time() + ttl
            self._timestamps["quotes"] = {
                code: expiry_time for code in quotes_dict
            }
            
            # 增加一个特殊键记录上次更新时间
            await self.set("_last_update", time.time(), ttl, "quotes")
            logger.info(f"已缓存全量行情数据: {len(quotes_dict)}只股票")
            
    async def get_quotes(self, symbols: Optional[list] = None) -> Dict[str, Any]:
        """
        获取行情数据，可以指定股票代码
        
        Args:
            symbols: 要获取的股票代码列表，None表示获取全部
            
        Returns:
            股票代码到行情数据的映射
        """
        if symbols:
            # 获取指定股票的行情
            result = {}
            for symbol in symbols:
                if symbol in self._cache["quotes"]:
                    result[symbol] = self._cache["quotes"][symbol]
            return result
        else:
            # 返回全部行情
            return self._cache["quotes"].copy()
            
    async def is_quotes_fresh(self, max_age_seconds: int = 180) -> bool:
        """
        检查行情数据是否新鲜
        
        Args:
            max_age_seconds: 最大允许年龄(秒)，默认3分钟
            
        Returns:
            如果行情数据在允许的时间范围内，则返回True
        """
        last_update = await self.get("_last_update", "quotes")
        if last_update is None:
            return False
            
        return (time.time() - last_update) < max_age_seconds

# 创建单例实例
cache_service = SimpleCacheService() 