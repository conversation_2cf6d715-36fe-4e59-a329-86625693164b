import React, { useState, useEffect, CSSProperties } from 'react';
import { useNavigate } from 'react-router-dom';
import { Layout, Row, Col, Empty, Spin, Modal } from 'antd';
import { PlusOutlined, FilterOutlined } from '@ant-design/icons';
import QuantumButton from '../components/common/QuantumButton';
import PageTitle from '../components/common/PageTitle';
import { strategyGroupApi } from '../services/strategyService';
import { StrategyGroup } from '../types/api';
import StrategyTypeModal from '../components/strategy/StrategyTypeModal';
import StrategySignalNotification from '../components/strategy/StrategySignalNotification';
import StrategyGroupItem from '../components/strategy/StrategyGroupItem';
import { App } from 'antd';
import SignalTable from '../components/SignalTable';
import TimingSignalTable from '../components/TimingSignalTable';

const { Content } = Layout;

// 样式定义
const styles = {
  page: {
    minHeight: '100vh',
    background: 'linear-gradient(135deg, #f0f2f5, #f5f7fa)',
    padding: '24px',
  } as CSSProperties,
  content: {
    marginTop: '24px',
  } as CSSProperties,
  section: {
    marginBottom: '32px',
  } as CSSProperties,
};

const Strategies: React.FC = () => {
  const navigate = useNavigate();
  const { message: antdMessage, notification } = App.useApp(); // 使用App.useApp获取message和notification
  const [loading, setLoading] = useState(false);
  const [strategies, setStrategies] = useState<StrategyGroup[]>([]);
  const [typeModalVisible, setTypeModalVisible] = useState(false);
  const [executingStrategyId, setExecutingStrategyId] = useState<string | null>(null);
  const [activeTimingStrategies, setActiveTimingStrategies] = useState<string[]>([]);
  
  // 新增状态用于管理结果模态框
  const [signalModalVisible, setSignalModalVisible] = useState(false);
  const [lastExecutedStrategy, setLastExecutedStrategy] = useState<{
    id: string;
    name: string;
    signals: any[];
    type: 'filter' | 'timing';
  } | null>(null);

  // 执行策略组
  const handleRunStrategy = async (strategyId: string) => {
    try {
      setExecutingStrategyId(strategyId);
      
      // 获取当前策略信息
      const strategy = strategies.find(s => s.id === strategyId);
      
      // 根据策略类型决定调用哪个接口
      if (strategy?.group_type === 'timing') {
        // 择时策略：根据当前状态决定启动或停止
        if (strategy.status === 'active') {
          // 如果已经是运行状态，则停止
          const response = await strategyGroupApi.stopGroup(strategyId);
          
          if (response && response.success) {
            antdMessage.success('择时策略组已停止执行');
            // 刷新策略列表
            loadStrategies();
          } else {
            antdMessage.error(`停止失败: ${response?.message || '未知错误'}`);
          }
        } else {
          // 如果是停止状态，则启动
          const response = await strategyGroupApi.startGroup(strategyId);
          
          if (response && response.success) {
            antdMessage.success('择时策略组已启动持续执行');
            // 刷新策略列表
            loadStrategies();
          } else {
            antdMessage.error(`启动失败: ${response?.message || '未知错误'}`);
          }
        }
      } else {
        // 执行选股策略
        const response = await strategyGroupApi.executeGroup(strategyId);
        
        if (response && response.success) {
          antdMessage.success('策略组执行成功');
          
          // 如果有信号数据，保存它并显示一个可点击的通知（这个通知包含查看结果按钮，所以需要保留）
          const signalCount = response.data?.signals?.length || 0;
          if (signalCount > 0) {
            // 保存最后执行的策略结果
            setLastExecutedStrategy({
              id: strategyId,
              name: strategy?.name || '未命名策略',
              signals: response.data.signals,
              type: strategy?.group_type as 'filter' | 'timing' || 'filter'
            });
            
            // 显示可点击的通知，这个是唯一保留的通知，包含"查看结果"按钮
            notification.open({
              message: `选股策略执行完成`,
              description: (
                <div>
                  <p>筛选出{signalCount}个符合条件的股票</p>
                  <QuantumButton 
                    variant="primary" 
                    buttonSize="small" 
                    onClick={() => {
                      setSignalModalVisible(true);
                      notification.destroy(); // 关闭通知
                    }}
                    style={{ marginTop: 8 }}
                  >
                    查看结果
                  </QuantumButton>
                </div>
              ),
              icon: <FilterOutlined style={{ color: '#1890ff' }} />,
              duration: 10,
              placement: 'topRight',
              key: `strategy-result-${strategyId}-${Date.now()}` // 添加唯一key避免重复
            });
          }
          
          // 刷新策略列表
          loadStrategies();
        } else {
          antdMessage.error(`执行失败: ${response?.message || '未知错误'}`);
        }
      }
    } catch (error) {
      console.error('执行策略组失败:', error);
      antdMessage.error('策略组执行失败');
    } finally {
      setExecutingStrategyId(null);
    }
  };

  const loadStrategies = async () => {
    try {
      setLoading(true);
      const response = await strategyGroupApi.getGroups({
        page: 1,
        pageSize: 10
      });
      console.log('策略组API响应:', response);
      const strategyList = response?.data?.items || [];
      setStrategies(strategyList);
      
      // 找出所有状态为active的择时策略
      const activeStrategies = strategyList
        .filter(s => s.status === 'active' && s.group_type === 'timing')
        .map(s => s.id);
      setActiveTimingStrategies(activeStrategies);
    } catch (error) {
      console.error('加载策略组失败:', error);
      setStrategies([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadStrategies();
  }, []);

  // 渲染信号表格
  const renderSignalTable = () => {
    if (!lastExecutedStrategy || !lastExecutedStrategy.signals) {
      return <Empty description="无信号数据" />;
    }
    
    return lastExecutedStrategy.type === 'timing' ? (
      <TimingSignalTable signals={lastExecutedStrategy.signals} />
    ) : (
      <SignalTable signals={lastExecutedStrategy.signals} />
    );
  };

  return (
    <div style={styles.page}>
      {/* 信号通知监听器 - 始终启用以接收所有类型策略的信号 */}
      <StrategySignalNotification visible={true} />
      
      <Content style={styles.content}>
        <div style={styles.section}>
          <PageTitle
            title="我的策略组合"
            subtitle="在这里管理您创建的所有策略组合，监控它们的表现并进行优化"
            breadcrumbs={[{ label: '策略管理' }]}
            extra={[
              <QuantumButton
                key="create"
                variant="primary"
                buttonSize="large"
                icon={<PlusOutlined />}
                onClick={() => setTypeModalVisible(true)}
              >
                创建策略
              </QuantumButton>
            ]}
          />

          <Spin spinning={loading}>
            {strategies.length === 0 ? (
              <Empty
                image={Empty.PRESENTED_IMAGE_SIMPLE}
                description="暂无策略组合"
              />
            ) : (
              <Row gutter={[24, 24]}>
                {strategies.map((strategy) => (
                  <Col key={strategy.id} xs={24} sm={12} lg={8} xl={8}>
                    <StrategyGroupItem 
                      strategy={strategy}
                      onRun={handleRunStrategy}
                      executingStrategyId={executingStrategyId}
                    />
                  </Col>
                ))}
              </Row>
            )}
          </Spin>
        </div>
      </Content>

      {/* 策略类型选择对话框 */}
      <StrategyTypeModal
        visible={typeModalVisible}
        onCancel={() => setTypeModalVisible(false)}
      />
      
      {/* 信号结果模态框 */}
      <Modal
        title={`${lastExecutedStrategy?.name || '策略'} 执行结果`}
        open={signalModalVisible}
        onCancel={() => setSignalModalVisible(false)}
        footer={null}
        width={1000}
        styles={{ body: { padding: '20px' } }}
        destroyOnClose
      >
        {renderSignalTable()}
      </Modal>
    </div>
  );
};

export default Strategies; 