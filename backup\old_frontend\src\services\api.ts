import axios, { AxiosRequestConfig, AxiosResponse } from 'axios';
import { App } from 'antd';

// 创建axios实例
const instance = axios.create({
  baseURL: '/api/v1',  // 使用相对路径，让vite的代理处理
  timeout: 30000,      // 增加超时时间到30秒
  headers: {
    'Content-Type': 'application/json',
  },
});

// 创建一个工具函数来获取message实例
const showMessage = (type: 'success' | 'error' | 'warning' | 'info', content: string) => {
  // 在App组件内部才能使用message，这里简单用console做备份显示
  console[type === 'error' ? 'error' : 'log'](content);
  
  // 如果是在组件内部调用，组件自行处理消息展示
  // 这里不再使用全局message
};

// 请求拦截器
instance.interceptors.request.use(
  (config) => {
    // 确保 headers 存在
    if (!config.headers) {
      config.headers = {};
    }

    // 从localStorage获取token
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    
    // 如果是登录请求，确保使用正确的Content-Type
    if (config.url === 'auth/login' && config.method === 'post') {
      config.headers['Content-Type'] = 'application/x-www-form-urlencoded';
    }
    
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
instance.interceptors.response.use(
  (response) => {
    return response;  // 返回完整响应对象
  },
  (error) => {
    if (error.response) {
      const { status, data } = error.response;
      
      // 处理常见错误
      switch (status) {
        case 401:
          // 保存当前路径
          const currentPath = window.location.pathname;
          if (currentPath !== '/login') {
            localStorage.setItem('redirectPath', currentPath);
          }
          // 使用 history 进行导航而不是直接修改 location
          if (!window.location.pathname.includes('/login')) {
            window.location.href = `/login?redirect=${encodeURIComponent(currentPath)}`;
          }
          break;
        case 403:
          showMessage('error', '没有权限执行此操作');
          break;
        case 404:
          showMessage('error', data.detail || '请求的资源不存在');
          break;
        case 500:
          showMessage('error', '服务器错误，请稍后重试');
          break;
        default:
          showMessage('error', data.detail || '请求失败');
      }
    } else if (error.request) {
      showMessage('error', '网络错误，请检查网络连接');
    } else {
      showMessage('error', '请求配置错误');
    }
    return Promise.reject(error);
  }
);

// 通用请求函数
export const request = async <T = any>(config: AxiosRequestConfig): Promise<T> => {
  try {
    const response: AxiosResponse<T> = await instance(config);
    return response.data;
  } catch (error: any) {
    if (error.response) {
      // 服务器返回错误响应
      const errorMessage = error.response.data?.message || '请求失败';
      showMessage('error', errorMessage);
    } else if (error.request) {
      // 请求发送成功但没有收到响应
      showMessage('error', '服务器无响应');
    } else {
      // 请求配置出错
      showMessage('error', '请求配置错误');
    }
    throw error;
  }
};

// 用户布局相关API
export const userLayoutApi = {
  // 获取用户布局
  getUserLayout: async () => {
    return request<{ layout: any[] }>({
      url: '/users/current/layout',
      method: 'get',
    });
  },
  
  // 保存用户布局
  saveUserLayout: async (layout: any[]) => {
    return request<{ status: string; message: string }>({
      url: '/users/layout',
      method: 'post',
      data: { layout },
    });
  }
};

// 重试机制的包装函数
export const withRetry = <T extends (...args: any[]) => Promise<any>>(
  fn: T,
  retries = 3,
  delay = 1000
): T => {
  return (async (...args: Parameters<T>) => {
    let lastError: any;
    
    for (let i = 0; i < retries; i++) {
      try {
        return await fn(...args);
      } catch (error) {
        lastError = error;
        if (i < retries - 1) {
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }
    
    throw lastError;
  }) as T;
};

export default instance;