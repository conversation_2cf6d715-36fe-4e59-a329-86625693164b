/**
 * 👤 个人资料场景 - 移动端优先设�? * 浅色主题，展示用户统计、成就、设�? */

import React, { useState, useMemo } from 'react'
import styled from 'styled-components'
import { motion, AnimatePresence } from 'framer-motion'
import { useGameState, useCardsState, useStrategyState } from '../../store/hooks'
import { useTheme } from '../../styles/themes/ThemeProvider'
import UnifiedMobileNav from '../components/ui/Nav/UnifiedMobileNav'

// 🎯 顶部用户信息
const ProfileHeader = styled.div<{ theme: any }>`
  background: ${props => props.theme.colors.background.card};
  backdrop-filter: blur(15px);
  border-bottom: 1px solid ${props => props.theme.colors.border.primary};
  padding: 2rem 1rem 1rem;
  flex-shrink: 0;
  box-shadow: ${props => props.theme.shadows.medium};
`

const UserAvatar = styled.div<{ theme: any }>`
  width: 80px;
  height: 80px;
  background: ${props => props.theme.colors.gradients.accent};
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2.5rem;
  margin: 0 auto 1rem;
  border: 3px solid ${props => props.theme.colors.primaryColors.cyan};
  box-shadow: ${props => props.theme.shadows.glow};
`

const UserInfo = styled.div`
  text-align: center;
`

const UserName = styled.h1<{ theme: any }>`
  color: ${props => props.theme.colors.text.primary};
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  font-family: ${props => props.theme.typography.fonts.primary};
`

const UserLevel = styled.div<{ theme: any }>`
  background: ${props => props.theme.colors.primaryColors.cyan}20;
  border: 1px solid ${props => props.theme.colors.primaryColors.cyan};
  color: ${props => props.theme.colors.primaryColors.cyan};
  padding: 0.25rem 1rem;
  border-radius: 20px;
  font-weight: 600;
  font-size: 0.9rem;
  display: inline-block;
  margin-bottom: 1rem;
`

const StatsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  margin-top: 1rem;
`

const StatItem = styled.div`
  text-align: center;
`

const StatValue = styled.div<{ theme: any }>`
  color: ${props => props.theme.colors.text.accent};
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
`

const StatLabel = styled.div<{ theme: any }>`
  color: ${props => props.theme.colors.text.secondary};
  font-size: 0.8rem;
`

// 📋 主要内容区域
const ProfileContent = styled.div<{ theme: any }>`
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  
  &::-webkit-scrollbar {
    width: 4px;
  }
  
  &::-webkit-scrollbar-track {
    background: ${props => props.theme.colors.background.secondary};
  }
  
  &::-webkit-scrollbar-thumb {
    background: ${props => props.theme.colors.primaryColors.cyan};
    border-radius: 2px;
  }
`

// 🔖 分类标签
const TabNavigation = styled.div`
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
  overflow-x: auto;
  padding-bottom: 0.5rem;
  
  &::-webkit-scrollbar {
    display: none;
  }
`

const TabButton = styled(motion.button)<{ $active: boolean, theme: any }>`
  background: ${props => props.$active ? props.theme.colors.primaryColors.cyan : props.theme.colors.background.surface};
  color: ${props => props.$active ? 'white' : props.theme.colors.text.secondary};
  border: 1px solid ${props => props.$active ? props.theme.colors.primaryColors.cyan : props.theme.colors.border.secondary};
  border-radius: 20px;
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  white-space: nowrap;
  transition: all 0.2s ease;
  font-family: ${props => props.theme.typography.fonts.primary};
  
  &:hover {
    background: ${props => props.$active ? props.theme.colors.primaryColors.cyan : `${props.theme.colors.primaryColors.cyan}20`};
    border-color: ${props => props.theme.colors.primaryColors.cyan};
  }
`

// 🏆 成就区域
const AchievementsSection = styled.div`
  margin-bottom: 2rem;
`

const SectionTitle = styled.h2<{ theme: any }>`
  color: ${props => props.theme.colors.text.primary};
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
  padding-left: 0.5rem;
  font-family: ${props => props.theme.typography.fonts.primary};
`

const AchievementGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1rem;
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
`

const AchievementCard = styled(motion.div)<{ $completed?: boolean }>`
  background: ${props => props.theme.colors.background.card};
  border: 1px solid ${props => props.$completed 
    ? props.theme.colors.semantic.success 
    : props.theme.colors.border.primary
  };
  border-radius: 12px;
  padding: 1.5rem;
  position: relative;
  opacity: ${props => props.$completed ? 1 : 0.7};
  
  ${props => props.$completed && `
    box-shadow: 0 0 15px ${props.theme.colors.semantic.success}30;
  `}
`

const AchievementIcon = styled.div<{ $completed?: boolean }>`
  font-size: 2.5rem;
  text-align: center;
  margin-bottom: 1rem;
  filter: ${props => props.$completed ? 'none' : 'grayscale(1)'};
`

const AchievementName = styled.h3`
  color: ${props => props.theme.colors.text.primary};
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  text-align: center;
`

const AchievementProgress = styled.div`
  color: ${props => props.theme.colors.text.secondary};
  font-size: 0.9rem;
  text-align: center;
  margin-bottom: 1rem;
`

const ProgressBar = styled.div`
  background: ${props => props.theme.colors.background.secondary};
  border-radius: 10px;
  height: 8px;
  overflow: hidden;
`

const ProgressFill = styled.div<{ $progress: number, theme: any }>`
  background: ${props => props.theme.colors.primaryColors.cyan};
  height: 100%;
  width: ${props => props.$progress}%;
  transition: width 0.3s ease;
`

// 📊 活动记录区域
const ActivitySection = styled.div`
  margin-bottom: 2rem;
`

const ActivityList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
`

const ActivityItem = styled(motion.div)`
  background: ${props => props.theme.colors.background.card};
  border: 1px solid ${props => props.theme.colors.border.primary};
  border-radius: 12px;
  padding: 1rem;
  display: flex;
  align-items: center;
  gap: 1rem;
`

const ActivityIcon = styled.div<{ $type: 'trade' | 'achievement' | 'level' }>`
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  background: ${props => {
    switch(props.$type) {
      case 'trade': return props.theme.colors.semantic.info + '20'
      case 'achievement': return props.theme.colors.semantic.success + '20'
      case 'level': return props.theme.colors.semantic.warning + '20'
      default: return props.theme.colors.border.primary
    }
  }};
  color: ${props => {
    switch(props.$type) {
      case 'trade': return props.theme.colors.semantic.info
      case 'achievement': return props.theme.colors.semantic.success
      case 'level': return props.theme.colors.semantic.warning
      default: return props.theme.colors.text.primary
    }
  }};
`

const ActivityContent = styled.div`
  flex: 1;
`

const ActivityTitle = styled.div`
  color: ${props => props.theme.colors.text.primary};
  font-weight: 600;
  margin-bottom: 0.25rem;
`

const ActivityTime = styled.div`
  color: ${props => props.theme.colors.text.secondary};
  font-size: 0.8rem;
`

// ⚙️ 设置区域
const SettingsSection = styled.div`
  margin-bottom: 2rem;
`

const SettingsList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
`

const SettingItem = styled(motion.div)<{ theme: any }>`
  background: ${props => props.theme.colors.background.card};
  border: 1px solid ${props => props.theme.colors.border.primary};
  border-radius: 12px;
  padding: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  
  &:hover {
    border-color: ${props => props.theme.colors.primaryColors.cyan};
  }
`

const SettingLabel = styled.div`
  color: ${props => props.theme.colors.text.primary};
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  
  .icon {
    font-size: 1.2rem;
  }
`

const SettingValue = styled.div`
  color: ${props => props.theme.colors.text.secondary};
  font-size: 0.9rem;
`

// 📱 底部导航
const BottomNavigation = styled.div<{ theme: any }>`
  background: ${props => props.theme.colors.background.card};
  backdrop-filter: blur(15px);
  border-top: 1px solid ${props => props.theme.colors.border.primary};
  padding: 1rem;
  display: flex;
  justify-content: space-around;
  flex-shrink: 0;
`

const NavButton = styled(motion.button)<{ $active?: boolean, theme: any }>`
  background: ${props => props.$active 
    ? props.theme.colors.primaryColors.cyan 
    : 'transparent'
  };
  border: 1px solid ${props => props.$active 
    ? props.theme.colors.primaryColors.cyan 
    : props.theme.colors.border.primary
  };
  color: ${props => props.$active 
    ? '#ffffff' 
    : props.theme.colors.text.primary
  };
  padding: 0.75rem 1rem;
  border-radius: 12px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
  min-width: 80px;
  
  .icon {
    font-size: 1.2rem;
  }
  
  .label {
    font-size: 0.8rem;
  }
`

// 📊 成就数据
const achievements = [
  {
    id: 'first_trade',
    name: '初次交易',
    description: '完成第一笔交易',
    icon: '🎯',
    progress: 100,
    maxProgress: 100,
    completed: true,
    reward: '100 金币'
  },
  {
    id: 'card_collector',
    name: '卡牌收集家',
    description: '收集10张不同的策略卡',
    icon: '🃏',
    progress: 7,
    maxProgress: 10,
    completed: false,
    reward: '稀有卡包'
  },
  {
    id: 'strategy_master',
    name: '策略大师',
    description: '创建5个策略组',
    icon: '🧠',
    progress: 2,
    maxProgress: 5,
    completed: false,
    reward: '传说卡牌'
  }
]

// 📜 活动记录数据
const activities = [
  {
    id: 1,
    type: 'achievement' as const,
    title: '获得成就：初次交易',
    time: '2小时前',
    icon: '🏆'
  },
  {
    id: 2,
    type: 'trade' as const,
    title: '执行策略组：多因子选股',
    time: '4小时前',
    icon: '📈'
  },
  {
    id: 3,
    type: 'level' as const,
    title: '等级提升：Lv.5',
    time: '1天前',
    icon: '⬆️'
  }
]

// 👤 个人资料场景组件
interface ProfileSceneProps {
  sceneData?: any
}

export default function ProfileScene({ sceneData }: ProfileSceneProps) {
  const { switchTheme } = useTheme()
  const { session, playerLevel, totalExperience, currency } = useGameState()
  const { inventory } = useCardsState()
  const { groups } = useStrategyState()
  
  const [activeTab, setActiveTab] = useState<'achievements' | 'activity' | 'settings'>('achievements')

  // 🎨 切换到浅色主题
  React.useEffect(() => {
    switchTheme('Profile')
  }, [switchTheme])

  // 📊 计算统计数据
  const stats = useMemo(() => {
    const totalCards = inventory.reduce((sum, item) => sum + item.quantity, 0)
    const activeGroups = groups.filter(group => group.status === 'active').length
    const completedAchievements = achievements.filter(a => a.completed).length
    
    return {
      totalCards,
      activeGroups,
      completedAchievements
    }
  }, [inventory, groups])

  return (
    <UnifiedMobileNav title="个人资料" bottomNavActive="Profile" showUserInfo={false}>
      <ProfileHeader>
        <UserAvatar>
          {session?.user?.user_metadata?.avatar_url ? (
            <img 
              src={session.user.user_metadata.avatar_url} 
              alt="头像" 
              style={{ width: '100%', height: '100%', borderRadius: '50%', objectFit: 'cover' }}
            />
          ) : (
            '👤'
          )}
        </UserAvatar>
        
        <UserInfo>
          <UserName>
            {session?.user?.user_metadata?.full_name || session?.user?.email?.split('@')[0] || '量化交易者'}
          </UserName>
          <UserLevel>等级 {playerLevel} · 盟主</UserLevel>
          
          <StatsGrid>
            <StatItem>
              <StatValue>{stats.totalCards}</StatValue>
              <StatLabel>策略数</StatLabel>
            </StatItem>
            <StatItem>
              <StatValue>{stats.activeGroups}</StatValue>
              <StatLabel>活跃策略</StatLabel>
            </StatItem>
            <StatItem>
              <StatValue>{currency?.coins || 0}</StatValue>
              <StatLabel>金币</StatLabel>
            </StatItem>
          </StatsGrid>
        </UserInfo>
      </ProfileHeader>

      <ProfileContent>
        <TabNavigation>
          <TabButton 
            $active={activeTab === 'achievements'} 
            onClick={() => setActiveTab('achievements')}
            whileTap={{ scale: 0.95 }}
          >
            🏆 成就
          </TabButton>
          <TabButton 
            $active={activeTab === 'activity'} 
            onClick={() => setActiveTab('activity')}
            whileTap={{ scale: 0.95 }}
          >
            📊 活动
          </TabButton>
          <TabButton 
            $active={activeTab === 'settings'} 
            onClick={() => setActiveTab('settings')}
            whileTap={{ scale: 0.95 }}
          >
            ⚙️ 设置
          </TabButton>
        </TabNavigation>

        <AnimatePresence mode="wait">
          {activeTab === 'achievements' && (
            <motion.div
              key="achievements"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.2 }}
            >
              <AchievementsSection>
                <SectionTitle>🏆 成就系统</SectionTitle>
                <AchievementGrid>
                  {achievements.map((achievement) => (
                    <AchievementCard
                      key={achievement.id}
                      $completed={achievement.completed}
                      whileHover={{ y: -2 }}
                    >
                      <AchievementIcon $completed={achievement.completed}>
                        {achievement.icon}
                      </AchievementIcon>
                      
                      <AchievementName>{achievement.name}</AchievementName>
                      
                      <AchievementProgress>
                        {achievement.progress}/{achievement.maxProgress}
                      </AchievementProgress>
                      
                      <ProgressBar>
                        <ProgressFill 
                          $progress={(achievement.progress / achievement.maxProgress) * 100}
                        />
                      </ProgressBar>
                      
                      {achievement.completed && (
                        <div style={{
                          position: 'absolute',
                          top: 10,
                          right: 10,
                          background: '#10b981',
                          color: 'white',
                          borderRadius: '50%',
                          width: 24,
                          height: 24,
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          fontSize: '0.8rem'
                        }}>
                          ✓
                        </div>
                      )}
                    </AchievementCard>
                  ))}
                </AchievementGrid>
              </AchievementsSection>
            </motion.div>
          )}

          {activeTab === 'activity' && (
            <motion.div
              key="activity"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.2 }}
            >
              <ActivitySection>
                <SectionTitle>📜 最近活动</SectionTitle>
                <ActivityList>
                  {activities.map((activity) => (
                    <ActivityItem
                      key={activity.id}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: activity.id * 0.1 }}
                    >
                      <ActivityIcon $type={activity.type}>
                        {activity.icon}
                      </ActivityIcon>
                      
                      <ActivityContent>
                        <ActivityTitle>{activity.title}</ActivityTitle>
                        <ActivityTime>{activity.time}</ActivityTime>
                      </ActivityContent>
                    </ActivityItem>
                  ))}
                </ActivityList>
              </ActivitySection>
            </motion.div>
          )}

          {activeTab === 'settings' && (
            <motion.div
              key="settings"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.2 }}
            >
              <SettingsSection>
                <SectionTitle>⚙️ 账户设置</SectionTitle>
                <SettingsList>
                  <SettingItem whileTap={{ scale: 0.98 }}>
                    <SettingLabel>
                      <span className="icon">🔔</span>
                      通知设置
                    </SettingLabel>
                    <SettingValue>开启</SettingValue>
                  </SettingItem>
                  
                  <SettingItem whileTap={{ scale: 0.98 }}>
                    <SettingLabel>
                      <span className="icon">🎨</span>
                      主题设置
                    </SettingLabel>
                    <SettingValue>自动</SettingValue>
                  </SettingItem>
                  
                  <SettingItem whileTap={{ scale: 0.98 }}>
                    <SettingLabel>
                      <span className="icon">📊</span>
                      数据统计
                    </SettingLabel>
                    <SettingValue>查看</SettingValue>
                  </SettingItem>
                  
                  <SettingItem whileTap={{ scale: 0.98 }}>
                    <SettingLabel>
                      <span className="icon">🆘</span>
                      帮助支持
                    </SettingLabel>
                    <SettingValue>联系</SettingValue>
                  </SettingItem>
                  
                  <SettingItem whileTap={{ scale: 0.98 }}>
                    <SettingLabel>
                      <span className="icon">🚪</span>
                      退出登录
                    </SettingLabel>
                    <SettingValue>→</SettingValue>
                  </SettingItem>
                </SettingsList>
              </SettingsSection>
            </motion.div>
          )}
        </AnimatePresence>
      </ProfileContent>
    </UnifiedMobileNav>
  )
}
