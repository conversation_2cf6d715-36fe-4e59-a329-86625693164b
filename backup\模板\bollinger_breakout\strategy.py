"""
布林带突破择时策略
基于布林带的高级突破策略，包含挤压形态识别、假突破过滤和多时间框架确认
"""
import logging
import pandas as pd
import numpy as np
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import yaml
import json
from pathlib import Path
import talib as ta

from app.core.strategy.base import UnifiedStrategyCard
from app.core.runtime.types import RuntimeContext, Signal, SignalType, StrategyMode
from app.services.minute_kline_service import MinuteKlineService

logger = logging.getLogger(__name__)

class BollingerBreakoutStrategy(UnifiedStrategyCard):
    """布林带突破策略
    
    该策略特点：
    1. 动态布林带：支持自适应标准差调整
    2. 挤压识别：检测布林带收窄形态（低波动率）
    3. 突破确认：多重条件确认真实突破
    4. 假突破过滤：通过成交量、持续性等过滤假信号
    5. 回调入场：突破后的回调确认入场点
    6. 动态止损：基于布林带的动态止损策略
    """
    
    def __init__(self, context: Optional[RuntimeContext] = None):
        """初始化策略"""
        super().__init__(context)
        
        # 加载配置文件
        config_path = Path(__file__).parent / "config.yaml"
        with open(config_path, "r", encoding="utf-8") as f:
            self.config = yaml.safe_load(f)
        
        # 初始化分钟K线服务
        self.kline_service = MinuteKlineService.get_instance()

    async def prepare_data(self, context: RuntimeContext) -> Dict[str, pd.DataFrame]:
        """准备数据"""
        try:
            # 检查串行执行模式
            if not self._check_sequential_mode(context):
                return {}
            
            # 从context.data_cache获取K线数据
            kline_data = context.data_cache.get("kline_data", {})
            if not kline_data:
                self._log("未能从上下文中获取K线数据", "warning")
                return {}
            
            # 处理每只股票的数据
            processed_data = {}
            for symbol, df in kline_data.items():
                try:
                    processed_df = await self._process_kline_data(df, context.parameters)
                    if not processed_df.empty:
                        processed_data[symbol] = processed_df
                        self._log(f"成功处理股票 {symbol} 的布林带数据，共{len(processed_df)}条记录")
                    else:
                        self._log(f"股票 {symbol} 数据处理后为空", "warning")
                except Exception as e:
                    self._log(f"处理股票 {symbol} 数据失败: {str(e)}", "error")
            
            return processed_data
            
        except Exception as e:
            self._log(f"准备数据失败: {str(e)}", "error")
            return {}
    
    async def _process_kline_data(self, df: pd.DataFrame, params: Dict[str, Any]) -> pd.DataFrame:
        """处理K线数据，计算布林带相关指标"""
        try:
            if df.empty:
                return pd.DataFrame()
            
            # 确保数据按时间排序
            df = df.sort_values('time').copy()
            
            # 获取参数
            bb_period = int(params.get("bb_period", 20))
            bb_std = float(params.get("bb_std", 2.0))
            squeeze_threshold = float(params.get("squeeze_threshold", 0.1))
            adaptive_std = params.get("adaptive_std", False)
            
            # 计算基础布林带
            df = self._calculate_bollinger_bands(df, bb_period, bb_std, adaptive_std)
            
            # 计算布林带相关指标
            df = self._calculate_bb_indicators(df, squeeze_threshold)
            
            # 计算成交量指标
            df = self._calculate_volume_indicators(df)
            
            # 计算RSI（用于背离分析）
            df['rsi'] = ta.RSI(df['close'].values, timeperiod=14)
            
            # 识别布林带挤压
            df = self._identify_squeeze_patterns(df, params)
            
            # 生成突破信号
            df = self._generate_breakout_signals(df, params)
            
            return df
            
        except Exception as e:
            self._log(f"处理K线数据失败: {str(e)}", "error")
            return pd.DataFrame()
    
    def _calculate_bollinger_bands(self, df: pd.DataFrame, period: int, 
                                 std_dev: float, adaptive_std: bool) -> pd.DataFrame:
        """计算布林带"""
        try:
            close_values = df['close'].values
            
            # 计算中轨（移动平均）
            df['bb_mid'] = ta.SMA(close_values, timeperiod=period)
            
            # 计算标准差
            if adaptive_std:
                # 自适应标准差：根据市场波动调整
                returns = df['close'].pct_change()
                volatility = returns.rolling(window=period).std()
                vol_mean = volatility.mean()
                vol_std = volatility.std()
                
                # 动态调整标准差倍数
                adaptive_multiplier = std_dev * (1 + (volatility - vol_mean) / vol_std * 0.2)
                adaptive_multiplier = np.clip(adaptive_multiplier, std_dev * 0.5, std_dev * 2)
                
                std_values = df['close'].rolling(window=period).std() * adaptive_multiplier
            else:
                std_values = df['close'].rolling(window=period).std() * std_dev
            
            # 计算上下轨
            df['bb_upper'] = df['bb_mid'] + std_values
            df['bb_lower'] = df['bb_mid'] - std_values
            
            return df
            
        except Exception as e:
            self._log(f"计算布林带失败: {str(e)}", "error")
            return df
    
    def _calculate_bb_indicators(self, df: pd.DataFrame, squeeze_threshold: float) -> pd.DataFrame:
        """计算布林带相关指标"""
        try:
            # 布林带宽度（标准化）
            df['bb_width'] = (df['bb_upper'] - df['bb_lower']) / df['bb_mid']
            
            # 布林带位置（%B）
            df['bb_position'] = (df['close'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])
            
            # 价格相对中轨位置
            df['price_to_mid'] = (df['close'] - df['bb_mid']) / df['bb_mid']
            
            # 布林带斜率
            df['bb_upper_slope'] = df['bb_upper'].diff(3) / 3
            df['bb_lower_slope'] = df['bb_lower'].diff(3) / 3
            df['bb_mid_slope'] = df['bb_mid'].diff(3) / 3
            
            # 挤压状态（布林带收窄）
            bb_width_ma = df['bb_width'].rolling(window=20).mean()
            bb_width_std = df['bb_width'].rolling(window=20).std()
            df['is_squeeze'] = df['bb_width'] < (bb_width_ma - bb_width_std * squeeze_threshold)
            
            # 挤压强度
            df['squeeze_strength'] = (bb_width_ma - df['bb_width']) / bb_width_std
            df['squeeze_strength'] = np.clip(df['squeeze_strength'], 0, 5)
            
            return df
            
        except Exception as e:
            self._log(f"计算布林带指标失败: {str(e)}", "error")
            return df
    
    def _calculate_volume_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算成交量指标"""
        try:
            # 成交量均线
            df['volume_ma'] = df['volume'].rolling(window=20).mean()
            df['volume_ratio'] = df['volume'] / df['volume_ma']
            
            # 价量关系
            price_change = df['close'].pct_change()
            volume_change = df['volume'].pct_change()
            df['price_volume_corr'] = price_change.rolling(window=10).corr(volume_change)
            
            return df
            
        except Exception as e:
            self._log(f"计算成交量指标失败: {str(e)}", "error")
            return df
    
    def _identify_squeeze_patterns(self, df: pd.DataFrame, params: Dict[str, Any]) -> pd.DataFrame:
        """识别挤压形态"""
        try:
            min_squeeze_periods = int(params.get("min_squeeze_periods", 5))
            
            # 计算连续挤压周期数
            df['squeeze_count'] = 0
            current_count = 0
            
            for i in range(len(df)):
                if df.iloc[i]['is_squeeze']:
                    current_count += 1
                else:
                    current_count = 0
                df.iloc[i, df.columns.get_loc('squeeze_count')] = current_count
            
            # 挤压突破准备状态
            df['squeeze_ready'] = (df['squeeze_count'] >= min_squeeze_periods) & (~df['is_squeeze'])
            
            # 挤压后首次突破
            df['post_squeeze_breakout'] = False
            
            for i in range(min_squeeze_periods, len(df)):
                # 检查前面是否有足够的挤压周期
                if (df.iloc[i-min_squeeze_periods:i]['is_squeeze'].sum() >= min_squeeze_periods and
                    not df.iloc[i]['is_squeeze']):
                    
                    # 检查是否突破上轨或下轨
                    if (df.iloc[i]['close'] > df.iloc[i]['bb_upper'] or
                        df.iloc[i]['close'] < df.iloc[i]['bb_lower']):
                        df.iloc[i, df.columns.get_loc('post_squeeze_breakout')] = True
            
            return df
            
        except Exception as e:
            self._log(f"识别挤压形态失败: {str(e)}", "error")
            return df
    
    def _generate_breakout_signals(self, df: pd.DataFrame, params: Dict[str, Any]) -> pd.DataFrame:
        """生成突破信号"""
        try:
            # 获取参数
            min_volume_ratio = float(params.get("min_volume_ratio", 1.5))
            breakout_confirmation = int(params.get("breakout_confirmation", 2))
            use_squeeze_filter = params.get("use_squeeze_filter", True)
            rsi_oversold = float(params.get("rsi_oversold", 30))
            rsi_overbought = float(params.get("rsi_overbought", 70))
            
            # 初始化信号列
            df['signal'] = 'HOLD'
            df['signal_strength'] = 0.0
            df['breakout_type'] = 'none'
            
            for i in range(breakout_confirmation, len(df)):
                current = df.iloc[i]
                previous = df.iloc[i-1]
                
                if pd.isna(current['bb_upper']) or pd.isna(current['bb_lower']):
                    continue
                
                # 上轨突破检测
                upper_breakout = (previous['close'] <= previous['bb_upper'] and 
                                current['close'] > current['bb_upper'])
                
                # 下轨突破检测  
                lower_breakout = (previous['close'] >= previous['bb_lower'] and
                                current['close'] < current['bb_lower'])
                
                # 成交量确认
                volume_confirmed = current['volume_ratio'] >= min_volume_ratio
                
                # 挤压后突破加分
                squeeze_bonus = 0.2 if (use_squeeze_filter and current['post_squeeze_breakout']) else 0
                
                # RSI背离确认
                rsi_bullish = current['rsi'] < rsi_oversold  # RSI超卖时突破更可靠
                rsi_bearish = current['rsi'] > rsi_overbought  # RSI超买时突破更可靠
                
                # 生成买入信号（上轨突破）
                if upper_breakout and volume_confirmed:
                    # 计算信号强度
                    base_strength = 0.6
                    volume_bonus = min(0.2, (current['volume_ratio'] - min_volume_ratio) * 0.1)
                    rsi_bonus = 0.1 if not rsi_bearish else -0.1
                    
                    signal_strength = min(0.95, base_strength + volume_bonus + squeeze_bonus + rsi_bonus)
                    
                    df.iloc[i, df.columns.get_loc('signal')] = 'BUY'
                    df.iloc[i, df.columns.get_loc('signal_strength')] = signal_strength
                    df.iloc[i, df.columns.get_loc('breakout_type')] = 'upper_breakout'
                
                # 生成卖出信号（下轨突破）
                elif lower_breakout and volume_confirmed:
                    # 计算信号强度
                    base_strength = 0.6
                    volume_bonus = min(0.2, (current['volume_ratio'] - min_volume_ratio) * 0.1)
                    rsi_bonus = 0.1 if not rsi_bullish else -0.1
                    
                    signal_strength = min(0.95, base_strength + volume_bonus + squeeze_bonus + rsi_bonus)
                    
                    df.iloc[i, df.columns.get_loc('signal')] = 'SELL'
                    df.iloc[i, df.columns.get_loc('signal_strength')] = signal_strength
                    df.iloc[i, df.columns.get_loc('breakout_type')] = 'lower_breakout'
                
                # 回到中轨信号（趋势回归）
                elif (current['bb_position'] > 0.8 and previous['bb_position'] <= 0.8):
                    # 从上轨回到中轨，可能的卖出信号
                    df.iloc[i, df.columns.get_loc('signal')] = 'SELL'
                    df.iloc[i, df.columns.get_loc('signal_strength')] = 0.4
                    df.iloc[i, df.columns.get_loc('breakout_type')] = 'mean_reversion'
                    
                elif (current['bb_position'] < 0.2 and previous['bb_position'] >= 0.2):
                    # 从下轨回到中轨，可能的买入信号
                    df.iloc[i, df.columns.get_loc('signal')] = 'BUY'
                    df.iloc[i, df.columns.get_loc('signal_strength')] = 0.4
                    df.iloc[i, df.columns.get_loc('breakout_type')] = 'mean_reversion'
            
            # 计算止损位
            df['stop_loss_long'] = df['bb_lower']  # 多头止损在下轨
            df['stop_loss_short'] = df['bb_upper']  # 空头止损在上轨
            
            return df
            
        except Exception as e:
            self._log(f"生成突破信号失败: {str(e)}", "error")
            return df

    async def generate_signal(self, data: Dict[str, pd.DataFrame], params: Dict[str, Any]) -> List[Signal]:
        """生成信号"""
        try:
            if not data:
                return []
            
            signals = []
            
            for symbol, df in data.items():
                if df.empty:
                    continue
                
                # 获取最新的交易信号
                recent_signals = df[df['signal'] != 'HOLD'].tail(3)  # 最近3个信号
                
                if recent_signals.empty:
                    # 生成市场状态信号
                    latest = df.iloc[-1]
                    
                    # 判断当前市场状态
                    if latest['is_squeeze']:
                        market_state = "布林带挤压中"
                        direction = "HOLD"
                        confidence = 0.3
                    elif latest['bb_position'] > 0.8:
                        market_state = "接近上轨压力"
                        direction = "HOLD"
                        confidence = 0.4
                    elif latest['bb_position'] < 0.2:
                        market_state = "接近下轨支撑"
                        direction = "HOLD"
                        confidence = 0.4
                    else:
                        market_state = "在布林带中轨附近"
                        direction = "HOLD"
                        confidence = 0.5
                    
                    signals.append(
                        self.create_signal(
                            symbol=symbol,
                            name=symbol,
                            direction=direction,
                            signal_type="technical",
                            confidence=confidence,
                            trigger_condition=market_state,
                            current_price=float(latest['close']),
                            bb_upper=float(latest['bb_upper']) if pd.notna(latest['bb_upper']) else 0,
                            bb_mid=float(latest['bb_mid']) if pd.notna(latest['bb_mid']) else 0,
                            bb_lower=float(latest['bb_lower']) if pd.notna(latest['bb_lower']) else 0,
                            bb_position=float(latest['bb_position']) if pd.notna(latest['bb_position']) else 0,
                            bb_width=float(latest['bb_width']) if pd.notna(latest['bb_width']) else 0,
                            is_squeeze=bool(latest['is_squeeze']),
                            squeeze_strength=float(latest['squeeze_strength']) if pd.notna(latest['squeeze_strength']) else 0
                        )
                    )
                else:
                    # 处理交易信号
                    for _, row in recent_signals.iterrows():
                        direction = row['signal']
                        confidence = row['signal_strength']
                        breakout_type = row['breakout_type']
                        
                        # 构建触发条件描述
                        if breakout_type == 'upper_breakout':
                            condition = "布林带上轨突破"
                        elif breakout_type == 'lower_breakout':
                            condition = "布林带下轨突破"
                        elif breakout_type == 'mean_reversion':
                            condition = "布林带均值回归"
                        else:
                            condition = "布林带信号"
                        
                        # 添加挤压信息
                        if row['post_squeeze_breakout']:
                            condition += "(挤压后突破)"
                        
                        # 计算止损位
                        if direction == 'BUY':
                            stop_loss = float(row['stop_loss_long'])
                        else:
                            stop_loss = float(row['stop_loss_short'])
                        
                        signal = self.create_signal(
                            symbol=symbol,
                            name=symbol,
                            direction=direction,
                            signal_type="technical",
                            confidence=confidence,
                            trigger_condition=condition,
                            current_price=float(row['close']),
                            bb_upper=float(row['bb_upper']) if pd.notna(row['bb_upper']) else 0,
                            bb_mid=float(row['bb_mid']) if pd.notna(row['bb_mid']) else 0,
                            bb_lower=float(row['bb_lower']) if pd.notna(row['bb_lower']) else 0,
                            bb_position=float(row['bb_position']) if pd.notna(row['bb_position']) else 0,
                            bb_width=float(row['bb_width']) if pd.notna(row['bb_width']) else 0,
                            breakout_type=breakout_type,
                            stop_loss=stop_loss,
                            volume_ratio=float(row['volume_ratio']) if pd.notna(row['volume_ratio']) else 0,
                            rsi=float(row['rsi']) if pd.notna(row['rsi']) else 0,
                            is_squeeze=bool(row['is_squeeze']),
                            post_squeeze_breakout=bool(row['post_squeeze_breakout'])
                        )
                        signals.append(signal)
            
            self._log(f"布林带突破策略生成{len(signals)}个信号")
            return signals
            
        except Exception as e:
            self._log(f"生成信号失败: {str(e)}", "error")
            return []

    async def execute(self, context: RuntimeContext) -> List[Signal]:
        """执行策略"""
        try:
            self._log("开始执行布林带突破策略")
            
            # 获取参数信息
            bb_period = context.parameters.get("bb_period", 20)
            bb_std = context.parameters.get("bb_std", 2.0)
            use_squeeze_filter = context.parameters.get("use_squeeze_filter", True)
            adaptive_std = context.parameters.get("adaptive_std", False)
            
            squeeze_text = "启用挤压过滤" if use_squeeze_filter else "关闭挤压过滤"
            adaptive_text = "启用自适应标准差" if adaptive_std else "固定标准差"
            
            self._log(f"策略参数: 周期={bb_period}, 标准差={bb_std}, {squeeze_text}, {adaptive_text}")
            
            # 准备数据
            data = await self.prepare_data(context)
            
            if not data:
                self._log("未获取到有效数据", "warning")
                return []
            
            # 生成信号
            signals = await self.generate_signal(data, context.parameters)
            
            # 统计信号类型
            breakout_signals = len([s for s in signals if s.direction in ["BUY", "SELL"] and s.confidence > 0.6])
            mean_reversion_signals = len([s for s in signals if s.confidence <= 0.5])
            hold_signals = len([s for s in signals if s.direction == "HOLD"])
            
            self._log(f"策略执行完成: 突破信号{breakout_signals}个, 均值回归信号{mean_reversion_signals}个, "
                     f"观望信号{hold_signals}个")
            
            return signals
            
        except Exception as e:
            self._log(f"策略执行失败: {str(e)}", "error")
            return []