/**
 * 实时行情数据服务 - WebSocket版本
 * 负责获取股票实时行情数据
 */
import { request } from './api';
import { message } from 'antd';
import { getPublicWebSocket } from './websocketService';

// 股票行情数据接口
export interface StockQuote {
  symbol: string;        // 股票代码
  name: string;          // 股票名称
  price: number;         // 当前价格
  open?: number;         // 开盘价
  high?: number;         // 最高价
  low?: number;          // 最低价
  close?: number;        // 昨收价
  volume?: number;       // 成交量
  amount?: number;       // 成交额
  change?: number;       // 涨跌额
  change_percent?: number; // 涨跌幅(%)
  update_time?: string;  // 更新时间
  [key: string]: any;    // 其他字段
}

// 全局股票数据存储
const stockQuotes: Record<string, StockQuote> = {};

// 注册全局WebSocket监听器（在应用启动时调用一次）
let isInitialized = false;

export function initRealTimeQuotes(): void {
  if (isInitialized) return;
  
  const ws = getPublicWebSocket();
  
  ws.on('quote_update', (data) => {
    if (data.symbol && data.data) {
      stockQuotes[data.symbol] = data.data;
      
      // 触发自定义事件，通知组件更新
      const event = new CustomEvent('quote_updated', { 
        detail: { symbol: data.symbol, data: data.data } 
      });
      window.dispatchEvent(event);
    }
  });
  
  ws.on('market_update', (data) => {
    if (data.data) {
      // 处理市场整体数据更新
      // 触发自定义事件
      const event = new CustomEvent('market_updated', { detail: data.data });
      window.dispatchEvent(event);
    }
  });
  
  isInitialized = true;
  console.log('实时行情WebSocket监听器已初始化');
}

/**
 * 获取已缓存的股票行情数据 - 同步方法，不进行API调用
 * 
 * @param symbols 股票代码列表，为空时获取所有已缓存股票
 * @param returnType 返回类型：对象(symbol->quote)或数组
 * @returns 股票行情数据
 */
export function getQuotes(
  symbols?: string[],
  returnType: 'object' | 'array' = 'object'
): Record<string, StockQuote> | StockQuote[] {
  if (!symbols || symbols.length === 0) {
    return returnType === 'array' ? Object.values(stockQuotes) : { ...stockQuotes };
  }
  
  const result: Record<string, StockQuote> = {};
  for (const symbol of symbols) {
    if (stockQuotes[symbol]) {
      result[symbol] = stockQuotes[symbol];
    }
  }
  
  return returnType === 'array' ? Object.values(result) : result;
}

/**
 * 请求初始股票行情数据 - 获取初始数据
 * 这个方法仅用于初始加载，后续更新通过WebSocket自动推送
 * 
 * @param symbols 股票代码列表，为空或不传时获取热门股票数据
 * @returns 根据参数类型返回对应格式的行情数据
 */
export async function getInitialMarketData(
  symbols?: string[]
): Promise<void> {
  try {
    const response = await request({
      url: 'market-data/batch',
      method: 'POST',
      data: {
        symbols: symbols || [],
        from_db: false
      }
    });
    
    if (response && response.code === 0 && response.data) {
      const quotes = response.data;
      
      // 添加成功日志
      console.log(`成功获取初始行情数据: ${quotes.length}条记录`);
      
      // 更新存储
      for (const quote of quotes) {
        if (quote.symbol) {
          stockQuotes[quote.symbol] = quote;
        }
      }
    } else {
      const errorMsg = '获取初始行情数据失败: ' + (response?.message || '未知错误');
      console.error(errorMsg);
      message.error(errorMsg);
    }
  } catch (error: any) {
    console.error('获取初始行情数据出错:', error);
    message.error('获取初始行情数据失败，请稍后重试');
  }
}

/**
 * 注册行情更新回调函数
 * 
 * @param symbol 股票代码，不传则监听所有股票
 * @param callback 回调函数
 */
export function subscribeQuote(
  symbol: string | null,
  callback: (quote: StockQuote) => void
): () => void {
  const handler = (e: CustomEvent) => {
    if (!symbol || e.detail.symbol === symbol) {
      callback(e.detail.data);
    }
  };
  
  // 添加事件监听
  window.addEventListener('quote_updated', handler as EventListener);
  
  // 返回取消订阅函数
  return () => {
    window.removeEventListener('quote_updated', handler as EventListener);
  };
}

/**
 * 注册市场数据更新回调函数
 * 
 * @param callback 回调函数
 */
export function subscribeMarket(
  callback: (data: any) => void
): () => void {
  const handler = (e: CustomEvent) => {
    callback(e.detail);
  };
  
  // 添加事件监听
  window.addEventListener('market_updated', handler as EventListener);
  
  // 返回取消订阅函数
  return () => {
    window.removeEventListener('market_updated', handler as EventListener);
  };
} 