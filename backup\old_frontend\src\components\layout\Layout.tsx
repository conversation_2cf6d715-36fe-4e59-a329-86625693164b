import React, { useState, useEffect, useCallback } from 'react';
import { Layout as AntLayout, Menu, Button, Input, Avatar, Dropdown, MenuProps, message } from 'antd';
import { Outlet, useNavigate, useLocation } from 'react-router-dom';
import {
  DashboardOutlined,
  ExperimentOutlined,
  LineChartOutlined,
  UserOutlined,
  SearchOutlined,
  LogoutOutlined,
  SettingOutlined,
} from '@ant-design/icons';
import { motion, AnimatePresence } from 'framer-motion';
import { authService } from '../../services/authService';
import './Layout.scss';

const { Header, Content } = AntLayout;
const { Search } = Input;

// 菜单项配置
const menuItems = [
  {
    key: '/',
    icon: <DashboardOutlined />,
    label: '仪表盘',
  },
  {
    key: '/strategies',
    icon: <ExperimentOutlined />,
    label: '策略管理',
  },
  {
    key: '/backtest',
    icon: <LineChartOutlined />,
    label: '回测分析',
  },
];

const Layout: React.FC = () => {
  const [currentUser, setCurrentUser] = useState<any>(null);
  const navigate = useNavigate();
  const location = useLocation();
  const [isMobile, setIsMobile] = useState(window.innerWidth <= 768);

  // 获取用户信息
  useEffect(() => {
    const fetchUserInfo = async () => {
      const token = localStorage.getItem('token');
      if (!token) {
        console.error('未找到有效的 token，无法获取用户信息。');
        return;
      }
      try {
        const user = await authService.getCurrentUser();
        setCurrentUser(user);
      } catch (error) {
        console.error('获取用户信息失败:', error);
      }
    };

    fetchUserInfo();
  }, []);

  // 监听窗口大小变化
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // 处理菜单点击
  const handleMenuClick = ({ key }: { key: string }) => {
    navigate(key);
  };

  // 处理搜索
  const handleSearch = (value: string) => {
    // 搜索逻辑
  };

  // 处理用户菜单点击
  const handleUserMenuClick = async ({ key }: { key: string }) => {
    switch (key) {
      case 'settings':
        navigate('/settings');
        break;
      case 'logout':
        try {
          await authService.logout();
          localStorage.removeItem('token');
          message.success('已成功退出登录');
          navigate('/login');
        } catch (error) {
          message.error('退出登录失败，请重试');
        }
        break;
    }
  };

  // 用户下拉菜单项
  const userMenuItems: MenuProps['items'] = [
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '系统设置',
    },
    {
      type: 'divider'
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
    }
  ];

  return (
    <AntLayout className="layout-container">
      <Header className="header">
        <div className="header-left">
          <div className="logo-container">
            <div className="logo-image">
              <img 
                src="/logo.png" 
                alt="Logo" 
                onError={(e) => {
                  const target = e.currentTarget;
                  target.outerHTML = `
                    <svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg" style="width: 100%; height: 100%;">
                      <defs>
                        <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
                          <stop offset="0%" style="stop-color:#722ED1;stop-opacity:1" />
                          <stop offset="100%" style="stop-color:#9254DE;stop-opacity:0.8" />
                        </linearGradient>
                        <linearGradient id="grad2" x1="100%" y1="0%" x2="0%" y2="100%">
                          <stop offset="0%" style="stop-color:#722ED1;stop-opacity:0.8" />
                          <stop offset="100%" style="stop-color:#9254DE;stop-opacity:0.6" />
                        </linearGradient>
                      </defs>
                      <g transform="translate(10,10) scale(0.8)">
                        <!-- 主要折线图形 -->
                        <path d="M10,50 L30,30 L50,60 L70,20 L90,45"
                              stroke="url(#grad1)"
                              fill="none"
                              stroke-width="4"
                              stroke-linecap="round"
                              stroke-linejoin="round"/>
                        <!-- 辅助曲线 -->
                        <path d="M10,70 C30,60 50,80 90,65"
                              stroke="url(#grad2)"
                              fill="none"
                              stroke-width="3"
                              stroke-linecap="round"
                              opacity="0.8"/>
                        <!-- 点状装饰 -->
                        <circle cx="30" cy="30" r="3" fill="#722ED1"/>
                        <circle cx="50" cy="60" r="3" fill="#722ED1"/>
                        <circle cx="70" cy="20" r="3" fill="#722ED1"/>
                      </g>
                    </svg>
                  `;
                }}
              />
            </div>
            <div className="logo-text">QuantCard</div>
          </div>
          
          <Menu
            theme="light"
            mode="horizontal"
            defaultSelectedKeys={['/']}
            selectedKeys={[location.pathname]}
            items={menuItems}
            onClick={handleMenuClick}
            className="top-menu"
          />
        </div>
        
        <div className="header-center">
          <Search
            placeholder="搜索..."
            allowClear
            onSearch={handleSearch}
            className="search-input"
            prefix={<SearchOutlined />}
          />
        </div>
        
        <div className="header-right">
          <Dropdown menu={{ items: userMenuItems, onClick: handleUserMenuClick }} placement="bottomRight">
            <div className="user-info">
              <Avatar size={32} icon={<UserOutlined />} />
              <span className="username">{currentUser?.username || 'Loading...'}</span>
            </div>
          </Dropdown>
        </div>
      </Header>
      
      <Content className="content">
        <AnimatePresence mode="wait">
          <motion.div
            key={location.pathname}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.2 }}
          >
            <Outlet />
          </motion.div>
        </AnimatePresence>
      </Content>
    </AntLayout>
  );
};

export default Layout; 