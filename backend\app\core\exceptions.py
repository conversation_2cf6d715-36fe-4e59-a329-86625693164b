class QuantCardException(Exception):
    """基础异常类"""
    def __init__(self, message: str):
        self.message = message
        super().__init__(self.message)

class StrategyError(QuantCardException):
    """策略相关异常"""
    pass

class BacktestError(QuantCardException):
    """回测相关异常"""
    pass

class DataError(QuantCardException):
    """数据相关异常"""
    pass

class ValidationError(QuantCardException):
    """验证相关异常"""
    pass 