from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel, Field, EmailStr
from app.core.data.db.base import MongoModel, db_manager
from app.schemas.user import UserBase, UserResponse
try:
    from app.core.security import verify_password
except ImportError:
    # 临时修复：如果passlib未安装，使用简单的密码验证
    def verify_password(plain_password: str, hashed_password: str) -> bool:
        return plain_password == hashed_password  # 临时方案
from bson import ObjectId

class User(BaseModel, MongoModel):
    """数据库中的用户模型"""
    id: Optional[str] = None  # 添加id字段
    username: str
    email: Optional[EmailStr] = None
    hashed_password: str
    is_active: bool = True
    is_superuser: bool = False
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    last_login: Optional[datetime] = None
    avatar: Optional[str] = None
    favorites: List[str] = []

    class Config:
        arbitrary_types_allowed = True
        json_encoders = {
            ObjectId: str
        }
        
    @classmethod
    async def get_by_username(cls, username: str):
        """通过用户名获取用户"""
        collection = await db_manager.get_mongodb_database(cls.get_db_name())
        user_data = await collection.users.find_one({"username": username})
        if user_data:
            # 将_id转换为id
            if "_id" in user_data:
                user_data["id"] = str(user_data.pop("_id"))
            return cls(**user_data)
        return None

    @classmethod
    async def get_by_email(cls, email: str):
        """通过邮箱获取用户"""
        collection = await db_manager.get_mongodb_database(cls.get_db_name())
        user_data = await collection.users.find_one({"email": email})
        if user_data:
            # 将_id转换为id
            if "_id" in user_data:
                user_data["id"] = str(user_data.pop("_id"))
            return cls(**user_data)
        return None
        
    @classmethod
    async def get_by_id(cls, user_id: str):
        """通过ID获取用户"""
        collection = await db_manager.get_mongodb_database(cls.get_db_name())
        user_data = await collection.users.find_one({"_id": ObjectId(user_id)})
        if user_data:
            # 将_id转换为id
            if "_id" in user_data:
                user_data["id"] = str(user_data.pop("_id"))
            return cls(**user_data)
        return None

    @classmethod
    def get_db_name(cls) -> str:
        """获取数据库名称"""
        return "quantcard"
        
    def check_password(self, plain_password: str) -> bool:
        """检查密码是否正确"""
        return verify_password(plain_password, self.hashed_password)

    def to_response(self) -> UserResponse:
        """转换为响应模型"""
        return UserResponse(
            id=self.id,
            username=self.username,
            email=self.email,
            is_active=self.is_active,
            is_superuser=self.is_superuser,
            created_at=self.created_at,
            last_login=self.last_login,
            avatar=self.avatar
        )

