/**
 * 🎮 策略组类型卡片组件
 * 游戏化的类型选择卡片
 */

import React from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import type { GroupTypeOption } from './types';

const CardContainer = styled(motion.div)<{ $selected: boolean; $color: string }>`
  position: relative;
  padding: 1.5rem;
  border-radius: 16px;
  border: 3px solid ${props => props.$selected ? props.$color : '#e5e7eb'};
  background: ${props => props.$selected 
    ? `linear-gradient(135deg, ${props.$color}15, ${props.$color}08)`
    : 'white'};
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: ${props => props.$selected 
      ? `linear-gradient(135deg, ${props.$color}20, transparent)`
      : 'transparent'};
    pointer-events: none;
  }
  
  &:hover {
    border-color: ${props => props.$color};
    background: ${props => `linear-gradient(135deg, ${props.$color}10, ${props.$color}05)`};
    transform: translateY(-2px);
    box-shadow: 0 8px 25px ${props => `${props.$color}40`};
  }
`;

const CardIcon = styled.div<{ $color: string }>`
  font-size: 2.5rem;
  margin-bottom: 1rem;
  text-align: center;
  filter: ${props => `drop-shadow(0 2px 4px ${props.$color}40)`};
`;

const CardTitle = styled.h4<{ $selected: boolean; $color: string }>`
  font-size: 1.2rem;
  font-weight: 700;
  color: ${props => props.$selected ? props.$color : '#374151'};
  margin: 0 0 0.5rem;
  text-align: center;
  font-family: "Inter", sans-serif;
`;

const CardDescription = styled.p`
  font-size: 0.9rem;
  color: #6b7280;
  margin: 0;
  text-align: center;
  line-height: 1.4;
`;

const SelectionIndicator = styled(motion.div)<{ $color: string }>`
  position: absolute;
  top: 0.75rem;
  right: 0.75rem;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: ${props => props.$color};
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 0.75rem;
  font-weight: bold;
`;

interface GroupTypeCardProps {
  option: GroupTypeOption;
  selected: boolean;
  onClick: () => void;
}

const GroupTypeCard: React.FC<GroupTypeCardProps> = ({
  option,
  selected,
  onClick,
}) => {
  return (
    <CardContainer
      $selected={selected}
      $color={option.color}
      onClick={onClick}
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      {selected && (
        <SelectionIndicator
          $color={option.color}
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ duration: 0.2 }}
        >
          ✓
        </SelectionIndicator>
      )}
      
      <CardIcon $color={option.color}>
        {option.icon}
      </CardIcon>
      
      <CardTitle $selected={selected} $color={option.color}>
        {option.label}
      </CardTitle>
      
      <CardDescription>
        {option.description}
      </CardDescription>
    </CardContainer>
  );
};

export default GroupTypeCard; 