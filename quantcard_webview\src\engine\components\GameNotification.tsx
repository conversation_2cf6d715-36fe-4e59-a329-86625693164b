import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import './GameNotification.scss';

export interface NotificationData {
  id: string;
  type: 'success' | 'warning' | 'error' | 'info';
  title: string;
  message: string;
  duration?: number;
  icon?: string;
}

interface GameNotificationProps {
  notifications: NotificationData[];
  onDismiss: (id: string) => void;
}

/**
 * 游戏化深色半透明通知系统
 */
export default function GameNotification({ notifications, onDismiss }: GameNotificationProps) {
  const getNotificationIcon = (type: string, customIcon?: string) => {
    if (customIcon) return customIcon;
    
    const icons = {
      success: '✅',
      warning: '⚠️',
      error: '❌',
      info: 'ℹ️'
    };
    return icons[type as keyof typeof icons] || 'ℹ️';
  };

  const getNotificationColor = (type: string) => {
    const colors = {
      success: '#10b981',
      warning: '#f59e0b', 
      error: '#ef4444',
      info: '#3b82f6'
    };
    return colors[type as keyof typeof colors] || '#3b82f6';
  };

  return (
    <div className="game-notifications">
      <AnimatePresence>
        {notifications.map((notification, index) => (
          <motion.div
            key={notification.id}
            className={`game-notification ${notification.type}`}
            initial={{ opacity: 0, x: 300, scale: 0.8 }}
            animate={{ opacity: 1, x: 0, scale: 1 }}
            exit={{ opacity: 0, x: 300, scale: 0.8 }}
            transition={{ 
              duration: 0.4, 
              ease: [0.4, 0, 0.2, 1],
              delay: index * 0.1 
            }}
            style={{ 
              '--notification-color': getNotificationColor(notification.type),
              top: `${20 + index * 90}px`
            } as React.CSSProperties}
            onClick={() => onDismiss(notification.id)}
          >
            {/* 背景光效 */}
            <div className="notification-glow"></div>
            
            {/* 内容区域 */}
            <div className="notification-content">
              <div className="notification-icon">
                {getNotificationIcon(notification.type, notification.icon)}
              </div>
              
              <div className="notification-text">
                <div className="notification-title">{notification.title}</div>
                <div className="notification-message">{notification.message}</div>
              </div>
              
              <button className="notification-close">×</button>
            </div>
            
            {/* 进度�?*/}
            <motion.div 
              className="notification-progress"
              initial={{ width: '100%' }}
              animate={{ width: '0%' }}
              transition={{ 
                duration: notification.duration || 4,
                ease: 'linear'
              }}
            />
          </motion.div>
        ))}
      </AnimatePresence>
    </div>
  );
}
