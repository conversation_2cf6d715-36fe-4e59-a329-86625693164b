/**
 * 🎭 ThemeProvider - 主题切换系统
 * 支持深色加载页面到浅色主界面的渐变切换
 */

import React, { createContext, useContext, useRef, useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import styled, { ThemeProvider as StyledThemeProvider } from 'styled-components'
import { buildTheme, themeTransition } from './dual-theme'
import type { ThemeMode } from '../../types/game'

// 🎯 场景主题映射
export type SceneName = 
  | 'WorldMap' | 'Battle' | 'Adventure' | 'Arena'
  | 'Codex' | 'Shop' | 'Profile' | 'Settings'
  | 'Strategy' | 'StrategyWorkshop' | 'StrategyCreation'
  | 'Monitor' | 'StrategyOptimization'

const sceneThemeMapping: Record<SceneName, ThemeMode> = {
  // 深色赛博朋克主题场景
  'Battle': 'dark',
  'Adventure': 'dark',
  'Arena': 'dark',
  'WorldMap': 'light',
  
  // 浅色现代主题场景
  'Codex': 'light',
  'Shop': 'light',
  'Profile': 'light', 
  'Settings': 'light',
  'Strategy': 'light',
  'StrategyWorkshop': 'light',
  'StrategyCreation': 'light',
  'Monitor': 'light',
  'StrategyOptimization': 'light'
}

// 🎨 主题上下文
interface ThemeContextType {
  theme: ReturnType<typeof buildTheme>
  mode: ThemeMode
  setMode: (mode: ThemeMode) => void
  switchTheme: (sceneName: SceneName) => void
  isTransitioning: boolean
  startTransition: () => void
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined)

// 🪝 主题钩子
export const useTheme = () => {
  const context = useContext(ThemeContext)
  if (context === undefined) {
    throw new Error('useTheme must be used within a DynamicThemeProvider')
  }
  return context
}

// 🎬 赛博朋克主题切换覆盖层
const CyberOverlay = styled(motion.div)`
  position: fixed;
  inset: 0;
  z-index: 9999;
  pointer-events: none;
  background: #000011;
  will-change: opacity, transform;
  backface-visibility: hidden;
  overflow: hidden;
`

const CyberScan = styled(motion.div)`
  position: absolute;
  inset: 0;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(0, 255, 255, 0.3) 25%,
    rgba(255, 0, 255, 0.5) 50%,
    rgba(0, 255, 255, 0.3) 75%,
    transparent 100%
  );
  
  &::before {
    content: '';
    position: absolute;
    inset: 0;
    background: repeating-linear-gradient(
      0deg,
      transparent 0px,
      transparent 2px,
      rgba(0, 255, 255, 0.1) 2px,
      rgba(0, 255, 255, 0.1) 4px
    );
  }
  
  &::after {
    content: 'QUANTCARD';
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    font-family: 'Orbitron', monospace;
    font-size: 16px;
    font-weight: 700;
    color: #00ffff;
    text-shadow: 0 0 20px #00ffff;
    letter-spacing: 4px;
  }
`



// 🎯 主题提供者组件
export const DynamicThemeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [mode, setMode] = useState<ThemeMode>('light') // 默认浅色主题（世界地图）
  const [isTransitioning, setIsTransitioning] = useState(false)
  const hasMountedRef = useRef(false)
  
  const theme = buildTheme(mode)
  
  const startTransition = () => {
    setIsTransitioning(true)
    setTimeout(() => {
      setIsTransitioning(false)
    }, themeTransition.duration * 1000)
  }
  
  // 🔄 模式切换处理器
  const handleModeChange = (newMode: ThemeMode) => {
    if (newMode === mode) return
    // 首次装载不触发过渡动画，直接设置
    if (!hasMountedRef.current) {
      hasMountedRef.current = true
      setMode(newMode)
      return
    }
    if (isTransitioning) return
    
    // 开始过渡动画
    setIsTransitioning(true)
    
    // 等待扫描动画完全覆盖屏幕后再切换主题
    // 扫描到中心位置需要 50% 的动画时长
    setTimeout(() => {
      setMode(newMode)
    }, (themeTransition.duration * 0.5) * 1000) // 0.8 * 0.5 * 1000 = 400ms
    
    // 动画结束后隐藏遮罩
    setTimeout(() => {
      setIsTransitioning(false)
    }, themeTransition.duration * 1000)
  }

  // 🎯 场景主题切换
  const switchTheme = (sceneName: SceneName) => {
    const targetMode = sceneThemeMapping[sceneName] || 'light'
    handleModeChange(targetMode)
  }
  
  return (
    <ThemeContext.Provider
      value={{
        theme,
        mode,
        setMode: handleModeChange,
        switchTheme,
        isTransitioning,
        startTransition,
      }}
    >
      <StyledThemeProvider theme={theme}>
        {children}
        
        {/* 🔄 赛博朋克主题切换动画：立即覆盖 → 扫描效果 → 切换主题 → 淡出揭示 */}
        <AnimatePresence>
          {isTransitioning && (
            <CyberOverlay
              initial={{ opacity: 1 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ 
                duration: themeTransition.duration * 0.2, 
                ease: 'easeOut' 
              }}
            >
              <CyberScan
                initial={{ x: '-100%', opacity: 1 }}
                animate={{ x: '100%', opacity: 1 }}
                transition={{ 
                  duration: themeTransition.duration * 0.9, 
                  ease: [0.25, 0.46, 0.45, 0.94]
                }}
              />
            </CyberOverlay>
          )}
        </AnimatePresence>
      </StyledThemeProvider>
    </ThemeContext.Provider>
  )
}

// 🎮 主题切换按钮组件
export const ThemeToggleButton: React.FC<{ className?: string }> = ({ className }) => {
  const { mode, setMode, isTransitioning } = useTheme()
  
  return (
    <motion.button
      className={className}
      onClick={() => setMode(mode === 'dark' ? 'light' : 'dark')}
      disabled={isTransitioning}
      whileHover={{ scale: 1.1 }}
      whileTap={{ scale: 0.9 }}
      style={{
        background: 'transparent',
        border: 'none',
        cursor: isTransitioning ? 'not-allowed' : 'pointer',
        fontSize: '1.5rem',
        padding: '0.5rem',
        borderRadius: '50%',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
      }}
    >
      <motion.span
        animate={{ 
          rotate: mode === 'dark' ? 0 : 180,
          scale: isTransitioning ? 0.8 : 1
        }}
        transition={{ duration: 0.3 }}
      >
        {mode === 'dark' ? '🌙' : '☀️'}
      </motion.span>
    </motion.button>
  )
}

export default DynamicThemeProvider