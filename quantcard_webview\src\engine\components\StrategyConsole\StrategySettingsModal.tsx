/**
 * ⚙️ 策略设置模态框组件
 * 编辑策略基本信息、执行配置和风险设置
 */

import React, { useState, useEffect } from 'react'
import styled from 'styled-components'
import { motion, AnimatePresence } from 'framer-motion'
import { useStrategyState } from '../../../store/hooks'
import type { StrategyGroup } from '../../../types/game'

// 🎨 样式组件
const ModalOverlay = styled(motion.div)`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
`

const ModalContent = styled(motion.div)`
  background: #ffffff;
  border-radius: 20px;
  width: 100%;
  max-width: 500px;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
`

const ModalHeader = styled.div`
  padding: 1.5rem;
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
`

const ModalTitle = styled.h2`
  margin: 0;
  font-size: 1.25rem;
  font-weight: 700;
  color: #1a202c;
`

const CloseButton = styled(motion.button)`
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 50%;
  background: #f8fafc;
  color: #64748b;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  
  &:hover {
    background: #e2e8f0;
  }
`

const ModalBody = styled.div`
  padding: 1.5rem;
  max-height: 60vh;
  overflow-y: auto;
`

const FormGroup = styled.div`
  margin-bottom: 1.5rem;
`

const Label = styled.label`
  display: block;
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.5rem;
`

const Input = styled.input`
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  font-size: 0.875rem;
  transition: all 0.2s ease;
  
  &:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  }
`

const TextArea = styled.textarea`
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  font-size: 0.875rem;
  resize: vertical;
  min-height: 80px;
  transition: all 0.2s ease;
  
  &:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  }
`

const Select = styled.select`
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  font-size: 0.875rem;
  background: white;
  transition: all 0.2s ease;
  
  &:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  }
`

const ModalFooter = styled.div`
  padding: 1.5rem;
  border-top: 1px solid #e2e8f0;
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
`

const Button = styled(motion.button)<{ $variant: 'primary' | 'secondary' | 'danger' }>`
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 12px;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  
  ${props => {
    switch (props.$variant) {
      case 'primary':
        return `
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
        `
      case 'secondary':
        return `
          background: #f8fafc;
          color: #475569;
          border: 1px solid #e2e8f0;
        `
      case 'danger':
        return `
          background: #dc2626;
          color: white;
          box-shadow: 0 2px 8px rgba(220, 38, 38, 0.3);
        `
      default:
        return ''
    }
  }}
  
  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }
`

const ErrorMessage = styled.div`
  color: #dc2626;
  font-size: 0.75rem;
  margin-top: 0.25rem;
`

// 🎮 组件属性
interface StrategySettingsModalProps {
  isOpen: boolean
  onClose: () => void
  strategyId: string | null
}

// 🎮 策略设置模态框组件
function StrategySettingsModal({ isOpen, onClose, strategyId }: StrategySettingsModalProps) {
  const { groups, updateGroup, deleteGroup } = useStrategyState()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string>('')
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    execution_mode: 'sequential' as 'sequential' | 'parallel'
  })
  
  const strategy = strategyId ? groups.find(g => g.id === strategyId) : null
  
  // 🔄 初始化表单数据
  useEffect(() => {
    if (strategy) {
      setFormData({
        name: strategy.name,
        description: strategy.description || '',
        execution_mode: strategy.execution_mode || 'sequential'
      })
    }
  }, [strategy])
  
  // 📝 处理表单提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!strategyId) return
    
    setLoading(true)
    setError('')
    
    try {
      await updateGroup(strategyId, formData)
      onClose()
    } catch (err) {
      setError(err instanceof Error ? err.message : '更新失败')
    } finally {
      setLoading(false)
    }
  }
  
  // 🗑️ 处理删除策略
  const handleDelete = async () => {
    if (!strategyId || !confirm('确定要删除这个策略吗？此操作不可恢复。')) return
    
    setLoading(true)
    setError('')
    
    try {
      await deleteGroup(strategyId)
      onClose()
    } catch (err) {
      setError(err instanceof Error ? err.message : '删除失败')
    } finally {
      setLoading(false)
    }
  }
  
  // 📝 处理输入变化
  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }
  
  if (!isOpen || !strategy) return null

  return (
    <AnimatePresence>
      <ModalOverlay
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        onClick={onClose}
      >
        <ModalContent
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.9, opacity: 0 }}
          onClick={(e) => e.stopPropagation()}
        >
          <ModalHeader>
            <ModalTitle>⚙️ 策略设置</ModalTitle>
            <CloseButton
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={onClose}
            >
              ✕
            </CloseButton>
          </ModalHeader>
          
          <form onSubmit={handleSubmit}>
            <ModalBody>
              <FormGroup>
                <Label>策略名称</Label>
                <Input
                  type="text"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  placeholder="请输入策略名称"
                  required
                />
              </FormGroup>
              
              <FormGroup>
                <Label>策略描述</Label>
                <TextArea
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  placeholder="请输入策略描述（可选）"
                />
              </FormGroup>
              
              <FormGroup>
                <Label>执行模式</Label>
                <Select
                  value={formData.execution_mode}
                  onChange={(e) => handleInputChange('execution_mode', e.target.value)}
                >
                  <option value="sequential">🔄 串行执行</option>
                  <option value="parallel">⚡ 并行执行</option>
                </Select>
              </FormGroup>
              
              {error && <ErrorMessage>{error}</ErrorMessage>}
            </ModalBody>
            
            <ModalFooter>
              <Button
                type="button"
                $variant="danger"
                onClick={handleDelete}
                disabled={loading}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                🗑️ 删除策略
              </Button>
              
              <Button
                type="button"
                $variant="secondary"
                onClick={onClose}
                disabled={loading}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                取消
              </Button>
              
              <Button
                type="submit"
                $variant="primary"
                disabled={loading}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                {loading ? '⏳ 保存中...' : '💾 保存'}
              </Button>
            </ModalFooter>
          </form>
        </ModalContent>
      </ModalOverlay>
    </AnimatePresence>
  )
}

export default StrategySettingsModal
