# 策略组保存流程测试指南

## 测试目标
验证用户从卡包选择1张卡牌到构建区并保存策略组的完整流程正常工作，确保修复后的卡牌消费逻辑正确运行。

## 测试前准备

### 1. 确保用户有卡牌库存
```bash
# 启动后端服务
cd backend
python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# 启动前端服务
cd quantcard_webview
npm run dev
```

### 2. 登录并检查库存
- 登录系统（非访客用户）
- 打开库存面板，确认有至少1张可用卡牌
- 记录卡牌的template_id和数量

## 核心测试流程

### 测试用例1：正常保存流程
**步骤：**
1. 进入策略创建场景
2. 从库存中选择1张卡牌添加到构建区
3. 验证卡牌的临时使用状态（temp_used）正确更新
4. 填写策略组名称和描述
5. 点击保存按钮
6. 验证保存成功

**预期结果：**
- 卡牌成功添加到构建区
- 库存显示可用数量减1（临时使用）
- 保存成功，显示策略组ID
- 库存中该卡牌的实际数量减1
- 临时使用状态清零

### 测试用例2：库存不足场景
**步骤：**
1. 选择库存数量为1的卡牌
2. 添加到构建区（此时可用数量变为0）
3. 尝试再次添加同一张卡牌
4. 验证错误提示

**预期结果：**
- 第二次添加失败
- 显示"库存不足"错误提示
- 不影响已添加的卡牌

### 测试用例3：保存失败回滚
**步骤：**
1. 添加卡牌到构建区
2. 断开网络连接
3. 尝试保存策略组
4. 验证错误处理和状态恢复

**预期结果：**
- 保存失败，显示网络错误提示
- 卡牌仍在构建区
- 库存状态正确（临时使用状态保持）

## 关键验证点

### 前端状态验证
```javascript
// 在浏览器控制台检查状态
// 1. 检查库存状态
window.store.getState().inventory

// 2. 检查可用数量计算
window.store.getState().getAvailableQuantity('template_id')

// 3. 检查总数量
window.store.getState().getTotalQuantity('template_id')
```

### 后端日志验证
```bash
# 查看后端日志，确认：
# 1. 库存检查日志
# 2. 卡牌消费日志
# 3. 策略组创建日志
tail -f backend/logs/app.log
```

## 性能测试

### 并发保存测试
1. 同时打开多个浏览器标签页
2. 使用相同的卡牌创建策略组
3. 同时点击保存
4. 验证只有一个成功，其他显示库存不足

### 大量卡牌测试
1. 创建包含10+张卡牌的策略组
2. 验证批量消费的性能和正确性
3. 检查内存使用情况

## 错误场景测试

### 1. 网络异常
- 保存过程中断网
- 服务器响应超时
- 验证错误提示和状态恢复

### 2. 权限问题
- 访客用户尝试保存
- 登录过期后保存
- 验证权限检查和错误提示

### 3. 数据异常
- 无效的卡牌ID
- 空的策略组
- 验证数据验证和错误处理

## 修复验证清单

- [ ] 前端getAvailableQuantity方法正确计算可用数量
- [ ] 前端consumeCard方法使用总数量而非可用数量检查
- [ ] 后端use_cards方法提供详细的错误信息
- [ ] 策略保存流程简化，减少不必要的数据转换
- [ ] 错误处理机制完善，提供用户友好的提示
- [ ] 临时使用状态和真实消费状态正确协调
- [ ] 并发场景下的原子性操作正确
- [ ] 日志记录完整，便于问题排查

## 回归测试

确保修复没有影响其他功能：
- [ ] 卡牌抽取功能正常
- [ ] 库存加载和显示正常
- [ ] 策略组执行功能正常
- [ ] 其他卡牌相关功能正常

## 测试报告模板

```markdown
## 测试结果

### 测试环境
- 前端版本：
- 后端版本：
- 浏览器：
- 测试时间：

### 测试结果
- 正常保存流程：✅/❌
- 库存不足场景：✅/❌
- 保存失败回滚：✅/❌
- 并发保存测试：✅/❌
- 错误处理验证：✅/❌

### 发现的问题
1. 问题描述
2. 复现步骤
3. 预期结果 vs 实际结果

### 建议
- 改进建议
- 后续优化方向
```
