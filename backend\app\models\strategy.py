from datetime import datetime
from typing import List, Dict, Any, Optional, Type
import logging
import importlib
from pydantic import BaseModel, Field
from bson import ObjectId

from ..core.data.db.base import MongoModel, db_manager
from ..core.strategy.base import UnifiedStrategyCard

logger = logging.getLogger(__name__)

class ParameterOption(BaseModel):
    """参数选项"""
    label: str = Field(..., description="选项标签")
    value: str = Field(..., description="选项值")

class ParameterValidation(BaseModel):
    """参数验证"""
    min: Optional[float] = Field(None, description="最小值")
    max: Optional[float] = Field(None, description="最大值")

class ParameterVisibility(BaseModel):
    """参数可见性条件"""
    # 支持两种格式：新格式(param/in)和旧格式(parameter/value)
    parameter: Optional[str] = Field(None, description="依赖的参数名(旧格式)")
    param: Optional[str] = Field(None, description="依赖的参数名(新格式)")
    value: Optional[Any] = Field(None, description="依赖参数的值(旧格式)")
    eq: Optional[Any] = Field(None, description="等于条件(新格式)")
    in_: Optional[List[Any]] = Field(None, alias="in", description="包含条件(新格式)")

    def get_param_name(self) -> str:
        """获取参数名，兼容新旧格式"""
        return self.param or self.parameter or ""

    def get_param_value(self) -> Any:
        """获取参数值，兼容新旧格式"""
        if self.in_ is not None:
            return self.in_
        if self.eq is not None:
            return self.eq
        return self.value

class Parameter(BaseModel):
    """策略参数定义"""
    type: str = Field(..., description="参数类型")
    label: str = Field(..., description="参数标签")
    description: str = Field(..., description="参数描述")
    required: bool = Field(default=False, description="是否必填")
    default: Any = Field(None, description="默认值")
    options: Optional[List[ParameterOption]] = Field(None, description="选项列表")
    validation: Optional[ParameterValidation] = Field(None, description="验证规则")
    unit: Optional[str] = Field(None, description="单位")
    group: Optional[str] = Field(None, description="参数组")
    visibleWhen: Optional[ParameterVisibility] = Field(None, description="可见性条件")

class ParameterGroup(BaseModel):
    """参数组定义"""
    parameters: List[str] = Field(..., description="参数列表")
    displayMode: str = Field(default="inline", description="显示模式")
    prefix: Optional[str] = Field(None, description="前缀")
    separator: Optional[str] = Field(None, description="分隔符")
    layout: Optional[str] = Field(None, description="布局")

class UIConfig(BaseModel):
    """UI配置"""
    icon: str = Field(..., description="图标")
    color: str = Field(..., description="颜色")
    # 支持两种字段名：group 和 category
    group: Optional[str] = Field(None, description="分组")
    category: Optional[str] = Field(None, description="分类")
    order: int = Field(default=0, description="排序")
    form: Dict[str, Any] = Field(default_factory=dict, description="表单配置")

    def get_group(self) -> str:
        """获取分组，兼容新旧格式"""
        return self.group or self.category or "默认"

class StrategyCard(MongoModel, BaseModel):
    """策略卡片模型"""
    id: Optional[str] = Field(None, description="策略的MongoDB ID")
    template_id: Optional[str] = Field(None, description="策略模板ID，唯一标识符")
    name: str = Field(..., description="策略名称")
    description: str = Field(..., description="策略描述")
    version: str = Field(..., description="策略版本")
    author: str = Field(..., description="作者")
    stars: int = Field(default=0, description="星级")
    tags: List[str] = Field(default_factory=list, description="标签列表，用于分类和筛选，包括功能标记（选股/择时/回测）")
    parameters: Dict[str, Parameter] = Field(..., description="策略参数定义")
    parameterGroups: Dict[str, ParameterGroup] = Field(..., description="参数组定义")
    ui: UIConfig = Field(..., description="UI配置")
    is_active: bool = Field(default=True, description="是否启用")
    template_code: str = Field(..., description="策略代码文件路径")
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)

    class Config:
        arbitrary_types_allowed = True
        json_encoders = {
            ObjectId: str,
            datetime: lambda v: v.isoformat()
        }

    @classmethod
    def get_collection_name(cls) -> str:
        """获取集合名称"""
        return "strategy_cards"
        
    @classmethod
    def get_db_name(cls) -> str:
        """获取数据库名称"""
        return "quantcard"
        
    @classmethod
    async def get(cls, strategy_id: str) -> Optional["StrategyCard"]:
        """根据ID获取策略卡片
        
        Args:
            strategy_id: 策略ID
            
        Returns:
            Optional[StrategyCard]: 策略卡片，如果不存在则返回None
        """
        try:
            db = await db_manager.get_mongodb_database(cls.get_db_name())
            result = await db[cls.get_collection_name()].find_one({"_id": ObjectId(strategy_id)})
            if result:
                return cls(**result)
            return None
        except Exception as e:
            logger.error(f"获取策略卡片失败: {str(e)}", exc_info=True)
            return None
            
    @classmethod
    async def find_by_template_id(cls, template_id: str) -> List["StrategyCard"]:
        """按模板ID查找策略卡片"""
        db = await db_manager.get_mongodb_database(cls.get_db_name())
        cursor = db[cls.get_collection_name()].find({"template_id": template_id})
        return [cls(**doc) async for doc in cursor]

    @classmethod
    async def find_for_function(cls, function_type: str) -> List["StrategyCard"]:
        """按功能类型查找卡片（选股/择时/回测）通过tags字段"""
        db = await db_manager.get_mongodb_database(cls.get_db_name())
        
        # 根据传入的功能类型设置要查找的标签
        tag_to_find = None
        if function_type == "filter":
            tag_to_find = "选股"
        elif function_type == "timing":
            tag_to_find = "择时"
        elif function_type == "backtest":
            tag_to_find = "回测"
        
        if not tag_to_find:
            return []
            
        cursor = db[cls.get_collection_name()].find({"tags": tag_to_find})
        return [cls(**doc) async for doc in cursor]

    @classmethod
    async def find_by_tags(cls, tags: List[str]) -> List["StrategyCard"]:
        """按标签查找策略卡片"""
        if not tags:
            return []
        db = await db_manager.get_mongodb_database(cls.get_db_name())
        cursor = db[cls.get_collection_name()].find({"tags": {"$in": tags}})
        return [cls(**doc) async for doc in cursor]

    @classmethod
    async def find_by_author(cls, author: str) -> List["StrategyCard"]:
        """按作者查找策略卡片"""
        db = await db_manager.get_mongodb_database(cls.get_db_name())
        cursor = db[cls.get_collection_name()].find({"author": author})
        return [cls(**doc) async for doc in cursor]

    def load_strategy_class(self):
        """加载策略类
        
        Returns:
            策略类
            
        Raises:
            ValueError: 当策略类加载失败时抛出
        """
        try:
            # 使用策略注册表加载
            from ..core.strategy.registry import strategy_registry
            return strategy_registry.load_strategy_from_path(self.template_code)
        except Exception as e:
            logger.error(f"加载策略类失败: {str(e)}", exc_info=True)
            raise ValueError(f"加载策略类失败: {str(e)}")

# StrategyGroup 已移至 strategy_group.py，避免重复定义

class StrategyExecutionHistory(MongoModel, BaseModel):
    """策略执行历史"""
    id: Optional[str] = Field(None, description="记录ID")
    strategy_id: str = Field(..., description="策略ID")
    user_id: str = Field(..., description="用户ID")
    status: str = Field(..., description="执行状态")
    result: Dict[str, Any] = Field(default_factory=dict, description="执行结果")
    error: Optional[str] = Field(None, description="错误信息")
    execution_time: float = Field(..., description="执行时间(秒)")
    execution_duration: float = Field(default=0, description="执行耗时(毫秒)")
    created_at: datetime = Field(default_factory=datetime.now)  # 使用本地时间而非UTC时间
    group_type: str = Field(default="filter", description="策略组类型: filter-选股, timing-择时")
        
    class Config:
        arbitrary_types_allowed = True
        json_encoders = {
            ObjectId: str,
            datetime: lambda v: v.isoformat()
        }

    @classmethod
    def get_collection_name(cls) -> str:
        """获取集合名称"""
        return "strategy_execution_history"

    @classmethod
    def get_db_name(cls) -> str:
        """获取数据库名称"""
        return "quantcard"
        
    async def insert(self) -> None:
        """插入新的执行历史记录"""
        await self.save()
        
    @classmethod
    async def create_indexes(cls) -> None:
        """创建索引以提高查询效率"""
        try:
            # 获取数据库连接和集合
            from ..core.data.db.base import db_manager
            db = await db_manager.get_mongodb_database(cls.get_db_name())
            collection = db[cls.get_collection_name()]
            
            # 创建复合索引 - 按strategy_id和user_id查询，并按created_at排序
            await collection.create_index(
                [
                    ("strategy_id", 1),
                    ("user_id", 1),
                    ("created_at", -1)
                ],
                background=True,
                name="idx_strategy_user_time"
            )
            
            # 创建时间索引 - 用于数据清理和时间范围查询
            await collection.create_index(
                [("created_at", -1)],
                background=True,
                name="idx_created_at"
            )
            
            # 创建状态索引 - 用于按状态筛选
            await collection.create_index(
                [("status", 1)],
                background=True,
                name="idx_status"
            )
            
            # 创建类型索引 - 用于区分选股和择时策略
            await collection.create_index(
                [("group_type", 1)],
                background=True,
                name="idx_group_type"
            )
            
            return True
        except Exception as e:
            import logging
            logging.error(f"创建索引失败: {e}")
            return False