import React, { lazy, Suspense } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import AuthGuard from '../components/auth/AuthGuard';
import Loading from '../components/common/Loading';

// 懒加载组件
const Layout = lazy(() => import('../components/layout/Layout'));
const Dashboard = lazy(() => import('../pages/Dashboard'));
const Strategies = lazy(() => import('../pages/Strategies'));
const StrategyCreate = lazy(() => import('../pages/StrategyCreate'));
const StrategyDetail = lazy(() => import('../pages/StrategyDetail'));
const Backtest = lazy(() => import('../pages/Backtest'));
const Login = lazy(() => import('../pages/Login'));
const Register = lazy(() => import('../pages/Register'));

// 路由守卫配置
export const PUBLIC_ROUTES = ['/login', '/register'];
export const DEFAULT_REDIRECT = '/';

const AppRoutes: React.FC = () => {
  return (
    <Suspense fallback={<Loading fullscreen />}>
      <Routes>
        {/* 公开路由 */}
        <Route path="/login" element={<Login />} />
        <Route path="/register" element={<Register />} />

        {/* 受保护路由 */}
        <Route
          path="/"
          element={
            <AuthGuard>
              <Layout />
            </AuthGuard>
          }
        >
          <Route index element={<Dashboard />} />
          <Route path="strategies" element={<Strategies />} />
          <Route path="strategies/create" element={<StrategyCreate />} />
          <Route path="strategies/:id" element={<StrategyDetail />} />
          <Route path="backtest" element={<Backtest />} />
        </Route>

        {/* 404重定向 */}
        <Route path="*" element={<Navigate to="/" replace />} />
      </Routes>
    </Suspense>
  );
};

export default AppRoutes; 