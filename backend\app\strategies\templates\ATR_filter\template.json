{"template_id": "ATR_filter", "name": "振幅", "description": "根据设定的振幅条件筛选股票，支持区间范围筛选。例：振幅小于1。", "version": "2.0.0", "author": "quantcard", "stars": 1, "tags": ["选股", "基础筛选"], "parameters": {"operator": {"type": "select", "label": "条件", "description": "振幅比较条件", "default": "大于", "options": [{"label": "大于", "value": "大于"}, {"label": "小于", "value": "小于"}, {"label": "区间", "value": "区间"}]}, "atrMin": {"type": "number", "label": "下限", "description": "振幅下限（%）", "default": 10, "unit": "%", "validation": {"required": true, "min": 0, "max": 10000}}, "atrMax": {"type": "number", "label": "上限", "description": "振幅上限（%），仅在'区间'模式下使用", "default": null, "unit": "%", "validation": {"min": 0, "max": 10000}, "visibleWhen": {"param": "operator", "in": ["区间"]}}}, "parameterGroups": {"ATR_filter": {"parameters": ["operator", "atrMin", "atrMax"], "displayMode": "inline", "prefix": "振幅", "separator": "-", "layout": "horizontal"}}, "ui": {"icon": "filter", "color": "#1890ff", "category": "筛选", "order": 1, "form": {"layout": "horizontal", "compact": true, "showDescription": false}}}