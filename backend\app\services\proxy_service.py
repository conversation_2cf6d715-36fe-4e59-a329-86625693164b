"""
代理服务模块

集成 jhao104/proxy_pool 项目的功能，提供代理IP获取和管理
"""
import logging
import requests
import random
from typing import Optional, Dict, List, Union
import time
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class ProxyService:
    """代理服务，提供代理IP的获取和管理"""
    
    _instance = None
    
    @classmethod
    def get_instance(cls, *args, **kwargs):
        """获取单例实例"""
        if cls._instance is None:
            cls._instance = cls(*args, **kwargs)
        return cls._instance
    
    def __init__(self, proxy_pool_url: str = "http://127.0.0.1:5010"):
        """
        初始化代理服务
        
        Args:
            proxy_pool_url: proxy_pool API服务地址，默认为本地5010端口
        """
        self.proxy_pool_url = proxy_pool_url
        self.proxy_cache = []  # 缓存可用代理列表
        self.last_update_time = datetime.min  # 上次更新时间
        self.update_interval = 60  # 更新间隔，单位为秒
        self.max_cache_size = 20  # 最大缓存代理数量
        self.current_proxy = None  # 当前使用的代理
        self.failed_proxies = set()  # 记录失败的代理
        self.proxy_use_count = {}  # 记录每个代理的使用次数
        self.max_use_count = 30  # 每个代理最大使用次数
    
    def _check_proxy_pool_available(self) -> bool:
        """
        检查代理池服务是否可用
        
        Returns:
            bool: 代理池服务是否可用
        """
        try:
            response = requests.get(f"{self.proxy_pool_url}/count", timeout=2)
            if response.status_code == 200:
                proxy_count = response.json().get("count", 0)
                logger.info(f"代理池可用，当前代理数量: {proxy_count}")
                return proxy_count > 0
            return False
        except Exception as e:
            logger.error(f"代理池服务不可用: {str(e)}")
            return False
    
    def _get_proxy_from_pool(self, https: bool = False) -> Optional[str]:
        """
        从代理池获取单个代理
        
        Args:
            https: 是否获取支持https的代理
            
        Returns:
            Optional[str]: 代理地址，格式为 'ip:port'
        """
        try:
            url = f"{self.proxy_pool_url}/get"
            if https:
                url += "?type=https"
                
            response = requests.get(url, timeout=2)
            if response.status_code == 200:
                proxy_data = response.json()
                if proxy_data.get("proxy"):
                    return proxy_data["proxy"]
            return None
        except Exception as e:
            logger.error(f"从代理池获取代理失败: {str(e)}")
            return None
    
    def _get_all_proxies(self, https: bool = False) -> List[str]:
        """
        从代理池获取所有代理
        
        Args:
            https: 是否获取支持https的代理
            
        Returns:
            List[str]: 代理地址列表
        """
        try:
            url = f"{self.proxy_pool_url}/all"
            if https:
                url += "?type=https"
                
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                proxies = response.json()
                return [p.get("proxy") for p in proxies if p.get("proxy")]
            return []
        except Exception as e:
            logger.error(f"从代理池获取所有代理失败: {str(e)}")
            return []
    
    def _delete_proxy(self, proxy: str) -> bool:
        """
        从代理池中删除指定代理
        
        Args:
            proxy: 要删除的代理，格式为 'ip:port'
            
        Returns:
            bool: 是否删除成功
        """
        try:
            url = f"{self.proxy_pool_url}/delete/?proxy={proxy}"
            response = requests.get(url, timeout=2)
            return response.status_code == 200
        except Exception as e:
            logger.error(f"删除代理失败: {str(e)}")
            return False
    
    def _update_proxy_cache(self, force: bool = False) -> None:
        """
        更新代理缓存
        
        Args:
            force: 是否强制更新
        """
        now = datetime.now()
        
        # 检查是否需要更新缓存
        if (not force and 
            self.proxy_cache and 
            (now - self.last_update_time).total_seconds() < self.update_interval):
            return
            
        # 检查代理池服务是否可用
        if not self._check_proxy_pool_available():
            logger.warning("代理池服务不可用，跳过更新缓存")
            return
            
        # 获取所有代理
        proxies = self._get_all_proxies()
        
        # 过滤掉已失败的代理
        valid_proxies = [p for p in proxies if p not in self.failed_proxies]
        
        if valid_proxies:
            # 只保留最大缓存数量的代理
            self.proxy_cache = valid_proxies[:self.max_cache_size]
            self.last_update_time = now
            logger.info(f"更新代理缓存成功，当前缓存数量: {len(self.proxy_cache)}")
        else:
            # 如果所有代理都失败了，清空失败记录，重新使用
            if not self.proxy_cache and proxies:
                self.failed_proxies.clear()
                self.proxy_cache = proxies[:self.max_cache_size]
                self.last_update_time = now
                logger.info(f"重置失败代理记录，重新使用所有代理，当前缓存数量: {len(self.proxy_cache)}")
    
    def get_proxy(self, https: bool = False) -> Optional[str]:
        """
        获取一个代理
        
        Args:
            https: 是否获取支持https的代理
            
        Returns:
            Optional[str]: 代理地址，格式为 'ip:port'
        """
        # 更新代理缓存
        self._update_proxy_cache()
        
        # 如果没有缓存的代理，直接从代理池获取
        if not self.proxy_cache:
            proxy = self._get_proxy_from_pool(https)
            if proxy:
                self.current_proxy = proxy
                self.proxy_use_count[proxy] = 1
                return proxy
            return None
            
        # 从缓存中选择代理
        for _ in range(min(3, len(self.proxy_cache))):  # 最多尝试3次
            if not self.proxy_cache:
                break
                
            # 随机选择一个代理
            proxy = random.choice(self.proxy_cache)
            
            # 检查代理使用次数
            if self.proxy_use_count.get(proxy, 0) >= self.max_use_count:
                # 使用次数达到上限，移除此代理
                self.proxy_cache.remove(proxy)
                continue
                
            # 更新代理使用计数
            self.proxy_use_count[proxy] = self.proxy_use_count.get(proxy, 0) + 1
            self.current_proxy = proxy
            return proxy
            
        # 如果缓存中没有可用代理，直接从代理池获取
        proxy = self._get_proxy_from_pool(https)
        if proxy:
            self.current_proxy = proxy
            self.proxy_use_count[proxy] = 1
            return proxy
            
        return None
    
    def report_proxy_failure(self, proxy: str) -> None:
        """
        报告代理失败
        
        Args:
            proxy: 失败的代理地址
        """
        if not proxy:
            return
            
        logger.warning(f"代理 {proxy} 失败")
        
        # 添加到失败代理集合
        self.failed_proxies.add(proxy)
        
        # 从缓存中移除
        if proxy in self.proxy_cache:
            self.proxy_cache.remove(proxy)
            
        # 从代理池中删除
        self._delete_proxy(proxy)
        
        # 如果是当前代理，清除当前代理
        if self.current_proxy == proxy:
            self.current_proxy = None
    
    def get_request_proxies(self, https: bool = False) -> Optional[Dict[str, str]]:
        """
        获取用于requests的代理字典
        
        Args:
            https: 是否获取支持https的代理
            
        Returns:
            Optional[Dict[str, str]]: 代理字典，格式为 {'http': 'http://ip:port'}
        """
        proxy = self.get_proxy(https)
        if not proxy:
            return None
            
        if https:
            return {
                'http': f'http://{proxy}',
                'https': f'http://{proxy}'  # 注意这里仍然是http://
            }
        return {
            'http': f'http://{proxy}'
        } 