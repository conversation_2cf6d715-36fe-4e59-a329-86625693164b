from datetime import datetime
from typing import Dict, Any, Optional, List, Union
from pydantic import Field, BaseModel
from bson import ObjectId

from ..core.data.db.base import MongoModel, db_manager

class ParameterValidation(BaseModel):
    """参数验证规则 - 升级版，统一所有验证字段"""
    required: Optional[bool] = None
    min: Optional[float] = None
    max: Optional[float] = None
    step: Optional[float] = None
    pattern: Optional[str] = None

class ParameterConfig(BaseModel):
    """参数配置 - 升级版，统一字段结构"""
    type: str = Field(..., description="参数类型: number, text, select, boolean, multi-select")
    label: str = Field(..., description="参数标签")
    default: Optional[Any] = None
    options: Optional[List[Dict[str, Any]]] = None  # 仅 select/multi-select 使用
    unit: Optional[str] = None
    validation: Optional[ParameterValidation] = None  # 统一验证规则
    visibleWhen: Optional[Dict[str, Any]] = None  # 支持 eq 和 in 条件
    order: Optional[int] = None
    help: Optional[str] = None
    description: Optional[str] = None  # 保持向后兼容

class ParameterGroup(BaseModel):
    """参数组配置 - 升级版，支持sentence模式"""
    parameters: List[str] = Field(..., description="参数列表，顺序即渲染顺序")
    style: Optional[str] = Field(default="inline", description="样式: sentence, block, inline")
    layout: Optional[str] = Field(default="horizontal", description="布局: horizontal, vertical")
    title: Optional[str] = None
    sentenceTemplate: Optional[str] = None  # 句式模板，如"{operator} {value} {unit}"
    displayMode: Optional[str] = Field(default="inline", description="兼容现有displayMode字段")
    prefix: Optional[str] = None
    separator: Optional[str] = None

class UiConfig(BaseModel):
    """UI配置 - 升级版，支持移动端优化"""
    icon: Optional[str] = None
    color: Optional[str] = None
    category: Optional[str] = None  # 替代原来的group字段
    order: Optional[int] = None
    form: Optional[Dict[str, Any]] = None  # 支持layout、mobile等配置

class StrategyTemplate(MongoModel, BaseModel):
    """策略模板模型"""
    template_id: str = Field(...)  # 策略模板ID，唯一标识符
    name: str = Field(...)  # 策略名称
    description: str = Field(default="")  # 策略描述
    # type 字段已移除，使用 template_id 作为唯一标识符
    version: str = Field(default="1.0.0")  # 策略版本
    author: str = Field(default="system")  # 作者
    stars: Optional[int] = Field(default=3)  # 星级评分
    tags: List[str] = Field(default_factory=list)  # 标签，用于分类和筛选，包括功能标记（选股/择时/回测）
    parameters: Dict[str, ParameterConfig] = Field(default_factory=dict)  # 参数配置
    parameterGroups: Optional[Dict[str, ParameterGroup]] = Field(default_factory=dict)  # 参数组配置
    ui: Optional[UiConfig] = None  # UI配置
    is_active: bool = Field(default=True)  # 是否激活
    template_code: Optional[str] = Field(default=None)  # 策略代码文件路径
    created_at: datetime = Field(default_factory=datetime.utcnow)  # 创建时间
    updated_at: datetime = Field(default_factory=datetime.utcnow)  # 更新时间

    @classmethod
    def get_collection_name(cls) -> str:
        """获取集合名称"""
        return "strategy_templates"

    @classmethod
    def get_db_name(cls) -> str:
        """获取数据库名称"""
        return "quantcard"

    class Config:
        allow_population_by_field_name = True
        arbitrary_types_allowed = True
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            ObjectId: str
        }

    @classmethod
    async def find_by_template_id(cls, template_id: str) -> Optional["StrategyTemplate"]:
        """按模板ID查找策略模板 (推荐方法)"""
        db = await db_manager.get_mongodb_database(cls.get_db_name())
        result = await db[cls.get_collection_name()].find_one({"template_id": template_id})
        if result:
            return cls(**result)
        return None

    @classmethod
    async def find_all(cls) -> List["StrategyTemplate"]:
        """查找所有策略模板"""
        db = await db_manager.get_mongodb_database(cls.get_db_name())
        cursor = db[cls.get_collection_name()].find()
        return [cls(**doc) async for doc in cursor]

    def validate_parameters(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """验证参数值 - 升级版，支持新的validation结构"""
        validated = {}
        errors = []

        for param_name, param_config in self.parameters.items():
            value = parameters.get(param_name)

            # 检查必填项
            if param_config.validation and param_config.validation.required:
                if value is None or value == "":
                    errors.append(f"参数 {param_name} 是必填项")
                    continue

            # 如果值为空且非必填，使用默认值
            if value is None or value == "":
                validated[param_name] = param_config.default
                continue

            # 类型验证
            if param_config.type == "number":
                try:
                    value = float(value)
                    if param_config.validation:
                        if param_config.validation.min is not None and value < param_config.validation.min:
                            errors.append(f"参数 {param_name} 不能小于 {param_config.validation.min}")
                        if param_config.validation.max is not None and value > param_config.validation.max:
                            errors.append(f"参数 {param_name} 不能大于 {param_config.validation.max}")
                except (ValueError, TypeError):
                    errors.append(f"参数 {param_name} 必须是数字")
                    continue

            elif param_config.type == "select":
                if param_config.options:
                    valid_values = [opt["value"] for opt in param_config.options]
                    if value not in valid_values:
                        errors.append(f"参数 {param_name} 的值必须是 {valid_values} 中的一个")
                        continue

            # 检查可见性条件
            if param_config.visibleWhen:
                if not self._check_visibility(param_config.visibleWhen, validated):
                    continue  # 不可见的参数跳过

            validated[param_name] = value

        if errors:
            raise ValueError("; ".join(errors))

        return validated

    def _check_visibility(self, visible_when: Dict[str, Any], current_values: Dict[str, Any]) -> bool:
        """检查参数可见性条件"""
        param = visible_when.get("param")
        if not param:
            return True

        current_value = current_values.get(param)

        # 支持 eq 条件
        if "eq" in visible_when:
            return current_value == visible_when["eq"]

        # 支持 in 条件
        if "in" in visible_when:
            return current_value in visible_when["in"]

        # 向后兼容旧的 value 字段
        if "value" in visible_when:
            return current_value == visible_when["value"]

        return True

    async def save_to_db(self):
        """保存到数据库"""
        db = await db_manager.get_mongodb_database(self.get_db_name())
        data = self.dict(exclude={"id"})
        if hasattr(self, "id") and self.id:
            # 更新
            await db[self.get_collection_name()].update_one(
                {"_id": ObjectId(self.id)},
                {"$set": data}
            )
        else:
            # 创建
            result = await db[self.get_collection_name()].insert_one(data)
            self.id = result.inserted_id
        return self