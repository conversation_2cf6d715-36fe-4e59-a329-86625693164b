import React, { forwardRef } from 'react';
import { Button, Tooltip } from 'antd';
import type { ButtonProps } from 'antd';
import classNames from 'classnames';
import { LoadingOutlined } from '@ant-design/icons';
import './QuantumButton.scss';

export interface QuantumButtonProps extends Omit<ButtonProps, 'type' | 'size' | 'variant'> {
  variant?: 'primary' | 'secondary' | 'outline';
  buttonSize?: 'small' | 'medium' | 'large';
  fullWidth?: boolean;
  loading?: boolean;
  tooltipPlacement?: 'top' | 'bottom' | 'left' | 'right';
  tooltipTitle?: React.ReactNode;
}

const QuantumButton = forwardRef<HTMLButtonElement, QuantumButtonProps>(({
  variant = 'primary',
  buttonSize = 'medium',
  className,
  fullWidth,
  loading = false,
  disabled = false,
  tooltipPlacement = 'top',
  tooltipTitle,
  icon,
  children,
  ...props
}, ref) => {
  // 将自定义的 variant 映射到 antd 的 type
  const buttonType = variant === 'outline' ? 'default' : 
                    variant === 'secondary' ? 'default' : 'primary';
  
  // 将自定义的 buttonSize 映射到 antd 的 size
  const size = buttonSize === 'medium' ? 'middle' :
               buttonSize === 'small' ? 'small' : 'large';

  const buttonClassName = classNames(
    'quantum-button',
    `quantum-button--${variant}`,
    `quantum-button--${buttonSize}`,
    {
      'quantum-button--full-width': fullWidth,
      'quantum-button--loading': loading,
    },
    className
  );

  const buttonContent = (
    <Button
      ref={ref}
      className={buttonClassName}
      type={buttonType as ButtonProps['type']}
      size={size}
      disabled={disabled || loading}
      icon={loading ? <LoadingOutlined /> : icon}
      {...props}
    >
      {children}
    </Button>
  );

  if (tooltipTitle) {
    return (
      <Tooltip title={tooltipTitle} placement={tooltipPlacement}>
        {buttonContent}
      </Tooltip>
    );
  }

  return buttonContent;
});

QuantumButton.displayName = 'QuantumButton';

export default QuantumButton; 