// 浅色主题的 3D 世界地图场景
.world-map-scene.light-theme {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 50%, #bae6fd 100%);
  color: #0f172a;
  font-family: 'Inter', 'SF Pro Display', system-ui, sans-serif;
}

// 3D Canvas容器优化
.world-map-scene.light-theme canvas {
  outline: none !important;
}

// 3D Canvas指针事件优化 - 在HUD区域让事件穿透
.world-map-canvas {
  position: absolute !important;
  top: 0;
  left: 0;
  z-index: 1; // 确保在HUD按钮下方
}

// 欢迎横幅样式
.welcome-banner {
  position: absolute;
  top: 50px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 16px;
  padding: 24px 32px;
  box-shadow: 0 8px 32px rgba(59, 130, 246, 0.15);
  z-index: 100;
  pointer-events: auto;
  min-width: 400px;
  text-align: center;
  
  .banner-content {
    .banner-title {
      font-size: 24px;
      font-weight: 700;
      color: #1e40af;
      margin: 0 0 8px 0;
      letter-spacing: -0.02em;
    }
    
    .banner-subtitle {
      font-size: 16px;
      color: #64748b;
      margin: 0;
      line-height: 1.5;
    }
  }
  
  .banner-close {
    position: absolute;
    top: 12px;
    right: 12px;
    width: 32px;
    height: 32px;
    border: none;
    background: rgba(148, 163, 184, 0.1);
    border-radius: 50%;
    color: #64748b;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    transition: all 0.2s ease;
    
    &:hover {
      background: rgba(239, 68, 68, 0.1);
      color: #dc2626;
      transform: scale(1.1);
    }
  }
}

//用户头像区域 - 增强游戏化设计
.user-panel {
  position: absolute;
  top: 20px;
  left: 20px;
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  pointer-events: auto;
  z-index: 10;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
    
    .user-content .user-avatar {
      transform: scale(1.1);
      box-shadow: 0 8px 24px rgba(59, 130, 246, 0.4);
    }
    
    .user-info-panel {
      transform: translateX(5px);
    }
  }
  
  .user-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(240, 249, 255, 0.9));
    backdrop-filter: blur(20px);
    border-radius: 50%;
    border: 3px solid rgba(59, 130, 246, 0.2);
    box-shadow: 
      0 8px 32px rgba(59, 130, 246, 0.15),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);
    width: 80px;
    height: 80px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    
    .user-avatar {
      width: 36px;
      height: 36px;
      border-radius: 50%;
      background: linear-gradient(45deg, #3b82f6, #8b5cf6);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 18px;
      color: white;
      box-shadow: 
        0 4px 16px rgba(59, 130, 246, 0.3),
        inset 0 2px 4px rgba(255, 255, 255, 0.2);
      transition: all 0.3s ease;
      margin-bottom: 2px;
    }
    
    .user-name {
      color: #1e40af;
      font-size: 9px;
      font-weight: 700;
      text-align: center;
      line-height: 1;
      letter-spacing: 0.3px;
      margin-bottom: 1px;
    }
    
    .user-level {
      color: #8b5cf6;
      font-size: 8px;
      font-weight: 800;
      text-align: center;
      line-height: 1;
      background: rgba(139, 92, 246, 0.1);
      padding: 1px 4px;
      border-radius: 8px;
    }
  }

  .user-info-panel {
    margin-left: 12px;
    display: flex;
    flex-direction: column;
    gap: 8px;
    transition: all 0.3s ease;
    
    .user-badge {
      display: flex;
      flex-direction: column;
      gap: 4px;

      .title {
        display: inline-flex;
        align-items: center;
        height: 24px;
        padding: 0 12px;
        border-radius: 20px;
        background: linear-gradient(135deg, #3b82f6, #8b5cf6);
        color: #fff;
        font-weight: 700;
        font-size: 11px;
        letter-spacing: 0.3px;
        box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);
        white-space: nowrap;
      }

      .score, .gems {
        display: inline-flex;
        align-items: center;
        height: 20px;
        padding: 0 10px;
        border-radius: 12px;
        background: rgba(255, 255, 255, 0.9);
        color: #1e40af;
        font-weight: 600;
        font-size: 10px;
        border: 1px solid rgba(59, 130, 246, 0.25);
        backdrop-filter: blur(10px);
        white-space: nowrap;
        box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
      }
      
      .gems {
        background: rgba(139, 92, 246, 0.1);
        border-color: rgba(139, 92, 246, 0.3);
        color: #8b5cf6;
      }
    }
    
    .exp-bar {
      position: relative;
      width: 120px;
      height: 12px;
      background: rgba(255, 255, 255, 0.3);
      border-radius: 6px;
      overflow: hidden;
      border: 1px solid rgba(59, 130, 246, 0.2);
      backdrop-filter: blur(5px);
      
      .exp-fill {
        height: 100%;
        background: linear-gradient(90deg, #10b981, #3b82f6);
        border-radius: 6px;
        transition: width 0.5s ease;
        box-shadow: 0 0 10px rgba(16, 185, 129, 0.4);
      }
      
      .exp-text {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 8px;
        font-weight: 600;
        color: #1e40af;
        text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
        white-space: nowrap;
      }
    }
  }
}

// 左侧功能按钮组 - 圆形纵向排列
.left-actions {
  position: absolute;
  top: 120px; // 位于用户头像下方
  left: 24px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  z-index: 10;
  
  .action-btn.circular {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 72px;
    height: 72px;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(240, 249, 255, 0.9));
    backdrop-filter: blur(20px);
    border: 3px solid rgba(59, 130, 246, 0.2);
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 
      0 8px 24px rgba(59, 130, 246, 0.15),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);
    pointer-events: auto;
    position: relative;
    overflow: hidden;
    
    // 游戏化光效背景
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: radial-gradient(circle, rgba(59, 130, 246, 0.1), transparent);
      opacity: 0;
      transition: opacity 0.6s ease;
    }
    
    .btn-icon {
      font-size: 24px;
      margin-bottom: 2px;
      transition: all 0.3s ease;
      filter: drop-shadow(0 2px 4px rgba(59, 130, 246, 0.2));
    }
    
    .btn-label {
      font-size: 10px;
      font-weight: 700;
      color: #3b82f6;
      transition: all 0.3s ease;
      letter-spacing: 0.5px;
      text-align: center;
      line-height: 1;
    }
    
    &:hover {
      background: linear-gradient(135deg, rgba(255, 255, 255, 1), rgba(240, 249, 255, 0.95));
      border-color: rgba(59, 130, 246, 0.4);
      transform: translateY(-4px) scale(1.1);
      box-shadow: 
        0 16px 40px rgba(59, 130, 246, 0.25),
        inset 0 1px 0 rgba(255, 255, 255, 0.5);
      
      &::before {
        opacity: 1;
      }
      
      .btn-icon {
        transform: scale(1.2);
        filter: drop-shadow(0 4px 8px rgba(59, 130, 246, 0.3));
      }
      
      .btn-label {
        color: #1e40af;
        transform: translateY(-1px);
      }
    }
    
    &:active {
      transform: translateY(-2px) scale(1.05);
      transition: all 0.1s ease;
    }
  }
}

// 全屏按钮 - 右上角灰色平面化设计
.fullscreen-btn {
  position: absolute;
  top: 24px;
  right: 24px;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(148, 163, 184, 0.8);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(148, 163, 184, 0.3);
  border-radius: 12px;
  color: #64748b;
  font-size: 20px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 12px rgba(148, 163, 184, 0.15);
  pointer-events: auto;
  z-index: 10;
  
  &:hover {
    background: rgba(148, 163, 184, 0.95);
    border-color: rgba(148, 163, 184, 0.5);
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 8px 20px rgba(148, 163, 184, 0.25);
    color: #475569;
  }
  
  &:active {
    transform: translateY(-1px) scale(1.02);
    transition: all 0.1s ease;
  }
}

// 底部操作栏 - 游戏化设计
.bottom-actions {
  position: absolute;
  bottom: 32px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  gap: 12px;
  z-index: 10;
  
  .bottom-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-width: 64px;
    padding: 12px 8px;
    border: none;
    border-radius: 16px;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    
    .btn-icon {
      font-size: 20px;
      margin-bottom: 4px;
      transition: all 0.3s ease;
    }
    
    .btn-label {
      font-size: 10px;
      font-weight: 600;
      transition: all 0.3s ease;
      letter-spacing: 0.5px;
    }
    
    // 次要按钮样式
    &.secondary {
      background: rgba(255, 255, 255, 0.8);
      backdrop-filter: blur(16px);
      border: 1px solid rgba(148, 163, 184, 0.3);
      color: #64748b;
      
      &:hover {
        background: rgba(255, 255, 255, 0.95);
        border-color: rgba(59, 130, 246, 0.3);
        transform: translateY(-2px);
        
        .btn-icon {
          transform: scale(1.1);
        }
        
        .btn-label {
          color: #3b82f6;
        }
      }
    }
    
    // 主要按钮样式（创建策略）
    &.primary {
      background: linear-gradient(135deg, #3b82f6, #8b5cf6);
      color: white;
      border: 2px solid rgba(255, 255, 255, 0.2);
      min-width: 120px;
      padding: 16px 20px;
      border-radius: 24px;
      box-shadow: 
        0 8px 32px rgba(59, 130, 246, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
      
      .btn-icon {
        font-size: 28px;
        font-weight: bold;
        margin-bottom: 6px;
        
        &.plus-icon {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 32px;
          height: 32px;
          background: rgba(255, 255, 255, 0.2);
          border-radius: 50%;
          margin-bottom: 8px;
        }
      }
      
      .btn-label {
        font-size: 13px;
        font-weight: 700;
      }
      
      &:hover {
        background: linear-gradient(135deg, #2563eb, #7c3aed);
        transform: translateY(-3px) scale(1.05);
        box-shadow: 
          0 16px 40px rgba(59, 130, 246, 0.4),
          inset 0 1px 0 rgba(255, 255, 255, 0.3);
        
        .btn-icon {
          transform: scale(1.1);
        }
        
        .btn-label {
          transform: translateY(-1px);
        }
      }
      
      &:active {
        transform: translateY(-1px) scale(1.02);
      }
    }
  }
}

// 地标详情面板
.landmark-panel {
  position: absolute;
  top: 50%;
  right: 32px;
  transform: translateY(-50%);
  width: 320px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 16px;
  box-shadow: 0 12px 40px rgba(59, 130, 246, 0.15);
  z-index: 100;
  overflow: hidden;
  
  .panel-header {
    display: flex;
    align-items: center;
    padding: 20px;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-bottom: 1px solid rgba(59, 130, 246, 0.1);
    
    .landmark-icon {
      width: 48px;
      height: 48px;
      background: linear-gradient(45deg, #3b82f6, #1d4ed8);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      margin-right: 16px;
      color: white;
      box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
    }
    
    .landmark-info {
      flex: 1;
      
      .landmark-name {
        color: #1e40af;
        font-size: 18px;
        font-weight: 700;
        margin: 0;
        line-height: 1.3;
      }
    }
    
    .panel-close {
      width: 32px;
      height: 32px;
      border: none;
      background: rgba(148, 163, 184, 0.1);
      border-radius: 50%;
      color: #64748b;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16px;
      transition: all 0.2s ease;
      
      &:hover {
        background: rgba(239, 68, 68, 0.1);
        color: #dc2626;
        transform: scale(1.1);
      }
    }
  }
  
  .panel-content {
    padding: 20px;
    
    .landmark-description {
      color: #475569;
      font-size: 14px;
      line-height: 1.6;
      margin-bottom: 20px;
    }
    
    .enter-btn {
      width: 100%;
      padding: 14px 20px;
      background: linear-gradient(45deg, #3b82f6, #1d4ed8);
      color: white;
      border: none;
      border-radius: 12px;
      font-size: 16px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
      position: relative;
      overflow: hidden;
      
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.6s ease;
      }
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
        background: linear-gradient(45deg, #2563eb, #1e40af);
        
        &::before {
          left: 100%;
        }
      }
      
      &:active {
        transform: translateY(0);
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.5);
        transition: all 0.1s ease;
      }
    }
  }
}

// CSS动画关键帧
@keyframes button-shimmer {
  0% {
    transform: translateX(-100%) rotate(45deg);
  }
  50% {
    transform: translateX(100%) rotate(45deg);
  }
  100% {
    transform: translateX(200%) rotate(45deg);
  }
}

@keyframes avatar-shine {
  0% {
    transform: translateX(-100%) rotate(45deg);
  }
  50% {
    transform: translateX(100%) rotate(45deg);
  }
  100% {
    transform: translateX(200%) rotate(45deg);
  }
}

// 移动端适配
@media (max-width: 768px) {
  .welcome-banner {
    top: 30px;
    left: 20px;
    right: 20px;
    transform: none;
    min-width: unset;
    padding: 20px;
    
    .banner-content {
      .banner-title {
        font-size: 20px;
      }
      
      .banner-subtitle {
        font-size: 14px;
      }
    }
  }
  
  .landmark-panel {
    position: fixed;
    bottom: 20px;
    left: 20px;
    right: 20px;
    top: auto;
    transform: none;
    width: auto;
  }
  
  .user-panel {
    top: 16px;
    left: 16px;
    
    .user-content {
      width: 60px;
      height: 60px;
      
      .user-avatar {
        width: 28px;
        height: 28px;
        font-size: 14px;
        margin-bottom: 1px;
      }
      
      .user-name {
        font-size: 7px;
        margin-bottom: 1px;
      }
      
      .user-level {
        font-size: 6px;
        padding: 0 3px;
      }
    }

    .user-info-panel {
      margin-left: 8px;
      gap: 6px;
      
      .user-badge {
        gap: 3px;

        .title { 
          height: 20px; 
          font-size: 9px; 
          padding: 0 8px; 
        }
        
        .score, .gems { 
          height: 16px; 
          font-size: 8px; 
          padding: 0 6px; 
        }
      }
      
      .exp-bar {
        width: 100px;
        height: 10px;
        
        .exp-text {
          font-size: 7px;
        }
      }
    }
  }
  
  .left-actions {
    top: 90px;
    left: 12px;
    gap: 12px;
    
    .action-btn.circular {
      width: 56px;
      height: 56px;
      border-width: 2px;
      
      .btn-icon {
        font-size: 18px;
        margin-bottom: 1px;
      }
      
      .btn-label {
        font-size: 8px;
      }
    }
  }
  
  .fullscreen-btn {
    top: 16px;
    right: 16px;
    width: 40px;
    height: 40px;
    font-size: 16px;
  }
  
  .bottom-actions {
    bottom: 20px;
    gap: 8px;
    
    .bottom-btn {
      min-width: 56px;
      padding: 10px 6px;
      
      .btn-icon {
        font-size: 18px;
      }
      
      .btn-label {
        font-size: 9px;
      }
      
      &.primary {
        min-width: 100px;
        padding: 14px 16px;
        
        .btn-icon {
          font-size: 24px;
          
          &.plus-icon {
            width: 28px;
            height: 28px;
          }
        }
        
        .btn-label {
          font-size: 12px;
        }
      }
    }
  }
}