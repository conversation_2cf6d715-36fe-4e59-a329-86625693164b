from pydantic import BaseSettings, EmailStr
from typing import Optional, List, Dict, Any
import os

class Settings(BaseSettings):
    PROJECT_NAME: str = "QuantCard"
    VERSION: str = "0.1.0"
    DESCRIPTION: str = "量化交易策略平台"
    API_V1_STR: str = "/api/v1"
    
    # CORS配置
    CORS_ORIGINS: List[str] = [
        "http://localhost:3000",      # 前端开发服务器
        "http://127.0.0.1:3000",      # 前端开发服务器（使用IP）
        "http://localhost:5173",      # Vite 默认端口
        "http://127.0.0.1:5173"       # Vite 默认端口（使用IP）
    ]
    CORS_ALLOW_CREDENTIALS: bool = True
    CORS_ALLOW_METHODS: List[str] = ["*"]
    CORS_ALLOW_HEADERS: List[str] = ["*"]
    CORS_EXPOSE_HEADERS: List[str] = ["*"]
    CORS_MAX_AGE: int = 600
    
    # MongoDB配置
    MONGODB_URL: str = "mongodb://localhost:27017"
    MONGODB_DB: str = "quantcard"
    
    # PostgreSQL配置
    POSTGRES_HOST: str = "localhost"
    POSTGRES_PORT: int = 65188
    POSTGRES_USER: str = "postgres"
    POSTGRES_PASSWORD: str = "StockAssistant123!"
    POSTGRES_DB: str = "stock_base_info"
    
    # QuestDB配置
    QUESTDB_HOST: str = os.getenv("QUESTDB_HOST", "localhost")
    QUESTDB_PORT: int = int(os.getenv("QUESTDB_PORT", "8812"))
    
    # JWT配置
    SECRET_KEY: str = os.getenv("SECRET_KEY", "your-secret-key-here")
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 24
    
    # 超级管理员配置
    FIRST_SUPERUSER: str = os.getenv("FIRST_SUPERUSER", "admin")
    FIRST_SUPERUSER_PASSWORD: str = os.getenv("FIRST_SUPERUSER_PASSWORD", "admin123")
    
    class Config:
        case_sensitive = True
        env_file = ".env"

settings = Settings()

def get_settings() -> Settings:
    """
    获取应用设置
    
    Returns:
        Settings: 应用设置
    """
    return settings

def get_postgres_config(db_name: Optional[str] = None) -> Dict[str, Any]:
    """获取PostgreSQL配置
    
    Args:
        db_name: 数据库名称，如果不指定则使用默认数据库
        
    Returns:
        Dict[str, Any]: PostgreSQL配置
    """
    return {
        'type': 'postgresql',
        'host': settings.POSTGRES_HOST,
        'port': settings.POSTGRES_PORT,
        'user': settings.POSTGRES_USER,
        'password': settings.POSTGRES_PASSWORD,
        'database': db_name or settings.POSTGRES_DB
    }
    
def get_mongodb_config(db_name: Optional[str] = None) -> Dict[str, Any]:
    """获取MongoDB配置
    
    Args:
        db_name: 数据库名称，如果不指定则使用默认数据库
        
    Returns:
        Dict[str, Any]: MongoDB配置
    """
    url_parts = settings.MONGODB_URL.split("://")
    if len(url_parts) > 1:
        protocol = url_parts[0]
        host_port = url_parts[1].split(":")
        host = host_port[0]
        port = int(host_port[1]) if len(host_port) > 1 else 27017
    else:
        protocol = "mongodb"
        host = url_parts[0]
        port = 27017
        
    return {
        "type": "mongodb",
        "host": host,
        "port": port,
        "database": db_name or settings.MONGODB_DB,
        "user": "",  # 如果需要认证，从环境变量或配置中获取
        "password": "",  # 如果需要认证，从环境变量或配置中获取
        "protocol": protocol
    }

def get_questdb_config() -> Dict[str, Any]:
    """获取QuestDB配置
    
    Returns:
        Dict[str, Any]: QuestDB配置
    """
    return {
        'host': settings.QUESTDB_HOST,
        'port': settings.QUESTDB_PORT
    }