// 游戏化深色半透明通知系统
.game-notifications {
  position: fixed;
  top: 0;
  right: 0;
  width: 400px;
  height: 100vh;
  z-index: 9999;
  pointer-events: none;
  padding: 20px;
  
  @media (max-width: 768px) {
    width: 100%;
    padding: 10px;
  }
}

.game-notification {
  position: absolute;
  right: 0;
  width: 360px;
  background: rgba(0, 0, 0, 0.85);
  backdrop-filter: blur(20px);
  border: 1px solid var(--notification-color);
  border-radius: 16px;
  overflow: hidden;
  pointer-events: auto;
  cursor: pointer;
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.4),
    0 0 0 1px rgba(255, 255, 255, 0.1);
  
  @media (max-width: 768px) {
    width: calc(100% - 20px);
  }
  
  // 背景光效
  .notification-glow {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      135deg,
      transparent 0%,
      var(--notification-color) 50%,
      transparent 100%
    );
    opacity: 0.1;
    animation: glow-pulse 3s ease-in-out infinite;
  }
  
  // 内容区域
  .notification-content {
    position: relative;
    display: flex;
    align-items: flex-start;
    padding: 16px;
    z-index: 1;
    
    .notification-icon {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background: linear-gradient(45deg, var(--notification-color), rgba(255, 255, 255, 0.1));
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 20px;
      margin-right: 12px;
      flex-shrink: 0;
      box-shadow: 0 0 20px var(--notification-color);
    }
    
    .notification-text {
      flex: 1;
      min-width: 0;
      
      .notification-title {
        color: #ffffff;
        font-size: 16px;
        font-weight: 700;
        margin-bottom: 4px;
        line-height: 1.3;
      }
      
      .notification-message {
        color: rgba(255, 255, 255, 0.8);
        font-size: 14px;
        line-height: 1.4;
        word-wrap: break-word;
      }
    }
    
    .notification-close {
      width: 24px;
      height: 24px;
      border: none;
      background: rgba(255, 255, 255, 0.1);
      color: rgba(255, 255, 255, 0.6);
      border-radius: 50%;
      font-size: 16px;
      cursor: pointer;
      transition: all 0.2s ease;
      flex-shrink: 0;
      margin-left: 8px;
      
      &:hover {
        background: rgba(255, 255, 255, 0.2);
        color: #ffffff;
        transform: scale(1.1);
      }
    }
  }
  
  // 进度条
  .notification-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 3px;
    background: var(--notification-color);
    box-shadow: 0 0 10px var(--notification-color);
  }
  
  // 悬停效果
  &:hover {
    transform: scale(1.02);
    border-color: var(--notification-color);
    box-shadow: 
      0 12px 40px rgba(0, 0, 0, 0.5),
      0 0 0 1px var(--notification-color);
    
    .notification-glow {
      opacity: 0.2;
    }
  }
  
  // 类型特定样式
  &.success {
    border-left: 4px solid #10b981;
  }
  
  &.warning {
    border-left: 4px solid #f59e0b;
  }
  
  &.error {
    border-left: 4px solid #ef4444;
    
    .notification-glow {
      animation: error-pulse 2s ease-in-out infinite;
    }
  }
  
  &.info {
    border-left: 4px solid #3b82f6;
  }
}

// 动画关键帧
@keyframes glow-pulse {
  0%, 100% {
    opacity: 0.1;
  }
  50% {
    opacity: 0.3;
  }
}

@keyframes error-pulse {
  0%, 100% {
    opacity: 0.1;
  }
  50% {
    opacity: 0.5;
  }
}

// 游戏化动画效果
.game-notification {
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent 0%,
      rgba(255, 255, 255, 0.1) 50%,
      transparent 100%
    );
    transition: left 0.6s ease;
    z-index: 2;
  }
  
  &:hover::before {
    left: 100%;
  }
}

// 响应式设置
@media (max-width: 768px) {
  .game-notifications {
    left: 0;
    right: 0;
    width: 100%;
  }
  
  .game-notification {
    position: relative;
    margin-bottom: 10px;
    
    .notification-content {
      padding: 12px;
      
      .notification-icon {
        width: 36px;
        height: 36px;
        font-size: 18px;
      }
      
      .notification-text {
        .notification-title {
          font-size: 14px;
        }
        
        .notification-message {
          font-size: 13px;
        }
      }
    }
  }
}
