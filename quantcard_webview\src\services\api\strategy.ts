/**
 * 策略API服务（HTTP）
 */

function getAuthToken(): string | undefined {
  try {
    const raw = window.localStorage.getItem('quantcard_auth') || window.sessionStorage.getItem('quantcard_auth')
    if (!raw) return undefined
    const obj = JSON.parse(raw)
    return obj?.token || obj?.access_token || obj?.authToken
  } catch {
    return undefined
  }
}

function authHeaders(extra: Record<string, string> = {}) {
  const token = getAuthToken()
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
    ...extra
  }
  if (token) headers['Authorization'] = `Bearer ${token}`
  return headers
}

export interface DebugRequestPayload {
  cards: Array<{ template_id: string; parameters: Record<string, any>; name?: string }>
  execution_mode?: 'sequential' | 'parallel'
  group_type?: 'filter' | 'timing'
  timing_symbols?: string
  kline_period?: string
}

export interface StrategyGroupPayload {
  name: string
  description?: string
  group_type?: 'filter' | 'timing'
  timing_symbols?: string
  kline_period?: string
  cards: Array<{
    id?: string
    name?: string
    description?: string
    parameters: Record<string, any>
    template_id: string
    template_name?: string
    position?: any
  }>
  execution_mode: 'sequential' | 'parallel'
  execution_config?: {
    execution_type?: 'onetime' | 'continuous'
    schedule?: any
    [key: string]: any
  }
}

// 模板 - 使用统一的模板服务
import { templateService } from '../templateService'

export async function fetchStrategyTemplate(templateId: string) {
  const token = getAuthToken()
  const template = await templateService.getTemplate(templateId, token)
  if (!template) {
    throw new Error(`获取模板失败: 模板 ${templateId} 不存在`)
  }
  return { success: true, data: template }
}

// 调试
export async function debugStrategy(payload: DebugRequestPayload) {
  const res = await fetch(`/api/v1/strategies/debug`, {
    method: 'POST',
    headers: authHeaders(),
    credentials: 'include',
    body: JSON.stringify(payload)
  })
  if (!res.ok) throw new Error(`调试执行失败: ${res.status}`)
  return res.json()
}

// 策略组 CRUD
export async function createStrategyGroup(payload: StrategyGroupPayload) {
  const res = await fetch(`/api/v1/strategies/groups`, {
    method: 'POST',
    headers: authHeaders(),
    credentials: 'include',
    body: JSON.stringify(payload)
  })
  if (!res.ok) throw new Error(`创建策略组失败: ${res.status}`)
  return res.json()
}

export async function updateStrategyGroup(groupId: string, payload: Partial<StrategyGroupPayload>) {
  const res = await fetch(`/api/v1/strategies/groups/${groupId}`, {
    method: 'PUT',
    headers: authHeaders(),
    credentials: 'include',
    body: JSON.stringify(payload)
  })
  if (!res.ok) throw new Error(`更新策略组失败: ${res.status}`)
  return res.json()
}

export async function getStrategyGroup(groupId: string) {
  const res = await fetch(`/api/v1/strategies/groups/${groupId}`, {
    method: 'GET',
    headers: authHeaders(),
    credentials: 'include'
  })
  if (!res.ok) throw new Error(`获取策略组失败: ${res.status}`)
  return res.json()
}

export async function listStrategyGroups(page = 1, pageSize = 10) {
  const res = await fetch(`/api/v1/strategies/groups?page=${page}&pageSize=${pageSize}`, {
    method: 'GET',
    headers: authHeaders(),
    credentials: 'include'
  })
  if (!res.ok) throw new Error(`获取策略组列表失败: ${res.status}`)
  return res.json()
}

export async function executeStrategyGroup(groupId: string, mode: 'sequential' | 'parallel' = 'sequential') {
  const res = await fetch(`/api/v1/strategies/groups/${groupId}/execute?mode=${mode}`, {
    method: 'POST',
    headers: authHeaders(),
    credentials: 'include'
  })
  if (!res.ok) throw new Error(`执行策略组失败: ${res.status}`)
  return res.json()
}

export async function startStrategyGroup(groupId: string) {
  const res = await fetch(`/api/v1/strategies/groups/${groupId}/start`, {
    method: 'POST',
    headers: authHeaders(),
    credentials: 'include'
  })
  if (!res.ok) throw new Error(`启动策略组失败: ${res.status}`)
  return res.json()
}

export async function stopStrategyGroup(groupId: string) {
  const res = await fetch(`/api/v1/strategies/groups/${groupId}/stop`, {
    method: 'POST',
    headers: authHeaders(),
    credentials: 'include'
  })
  if (!res.ok) throw new Error(`停止策略组失败: ${res.status}`)
  return res.json()
}

export async function deleteStrategyGroup(groupId: string) {
  const res = await fetch(`/api/v1/strategies/groups/${groupId}`, {
    method: 'DELETE',
    headers: authHeaders(),
    credentials: 'include'
  })
  if (!res.ok) throw new Error(`删除策略组失败: ${res.status}`)
  return res.json()
}

export async function getStrategyGroupExecutionHistory(groupId: string) {
  const res = await fetch(`/api/v1/strategies/groups/${groupId}/execution-history`, {
    method: 'GET',
    headers: authHeaders(),
    credentials: 'include'
  })
  if (!res.ok) throw new Error(`获取策略组执行历史失败: ${res.status}`)
  return res.json()
}