/**
 * 🛠️ 参数编辑器工具函数
 * 统一处理参数可见性逻辑，避免重复定义
 */

import type { StrategyParameter } from './types';

/**
 * 检查参数是否应该显示
 * 统一的可见性判断逻辑，支持多种条件格式
 */
export function shouldShowParameter(
  param: StrategyParameter,
  values: Record<string, any>,
  parameters: Record<string, StrategyParameter>
): boolean {
  if (!param.visibleWhen) return true;

  const visibleWhen = param.visibleWhen as any;
  
  // 支持新格式: { param: 'operator', eq: '大于' }
  if (visibleWhen.param) {
    const currentValue = values[visibleWhen.param] ?? parameters[visibleWhen.param]?.default;
    
    // 支持 eq 条件
    if (typeof visibleWhen.eq !== 'undefined') {
      return currentValue === visibleWhen.eq;
    }
    
    // 支持 in 条件（数组）
    if (Array.isArray(visibleWhen.in)) {
      return visibleWhen.in.includes(currentValue);
    }
  }
  
  // 支持旧格式: { parameter: 'operator', value: '大于' }
  if (visibleWhen.parameter) {
    const currentValue = values[visibleWhen.parameter] ?? parameters[visibleWhen.parameter]?.default;
    
    // 支持数组类型的value
    if (Array.isArray(visibleWhen.value)) {
      return visibleWhen.value.includes(currentValue);
    }
    
    // 单值匹配
    return currentValue === visibleWhen.value;
  }

  return true;
}

/**
 * 获取可见的参数列表
 */
export function getVisibleParameters(
  parameters: Record<string, StrategyParameter>,
  values: Record<string, any>
): Array<[string, StrategyParameter]> {
  return Object.entries(parameters).filter(([name, param]) => 
    shouldShowParameter(param, values, parameters)
  );
}

/**
 * 获取未分组的可见参数
 */
export function getUngroupedVisibleParameters(
  parameters: Record<string, StrategyParameter>,
  parameterGroups: Record<string, any> | undefined,
  values: Record<string, any>
): Array<[string, StrategyParameter]> {
  const groupedParamNames = new Set(
    Object.values(parameterGroups || {}).flatMap(group => group.parameters)
  );
  
  return Object.entries(parameters)
    .filter(([name, param]) => 
      !groupedParamNames.has(name) && shouldShowParameter(param, values, parameters)
    );
}
