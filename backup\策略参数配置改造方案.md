## 目标
- 统一“策略卡/策略组”从构建、配置、调试到执行的全流程认知与接口
- 梳理与对齐模板参数(parameters)与参数组(parameterGroups)在后端模型与前端渲染中的职责
- 提出一套兼容现状、支持移动端游戏风格 UI 的参数配置改造方案，分阶段推进

## 一、现状梳理

### 1.1 策略卡/策略组全流程
- 构建/管理
  - 模板来源：Mongo 集合 strategy_templates；模板文件位于 backend/app/strategies/templates/<template_id>/template.json；有的模板含 template_code 指向 strategy.py。
  - 后端模型：StrategyTemplate 定义了 parameters、parameterGroups、ui 等；StrategyCard 提供 load_strategy_class() 以 registry 按 template_code 加载类。
  - 前端（Webview/游戏风）
    - 通过 GET /api/v1/strategies/templates/{id} 获取模板详情
    - 在 StrategyCreationScene 中打开 AdvancedParameterModal，使用 InlineParameterEditor 渲染参数/参数组

- 配置/编辑
  - 参数定义：template.json 中的 parameters 是键值到 ParameterConfig 的映射（type、label、default、options、visibleWhen、unit 等）
  - 参数分组：parameterGroups 提供分段/行内展示等 UI 提示（prefix、layout、displayMode、separator）
  - 前端参数编辑：
    - AdvancedParameterModal.props: parameters、parameterGroups、values
    - 条件显示：visibleWhen 由 InlineParameterEditor 在前端生效

- 调试/执行
  - 单卡/组：POST /api/v1/strategies/debug 由 StrategyService.execute_strategy 统一走组执行路径
  - 组执行：StrategyGroupExecutor.execute_group 支持 sequential/parallel、filter/timing；把各卡的 RuntimeContext 串起来并传播 previous_signals
  - 运行时：StrategyExecutor._run_strategy -> 模板.load_strategy_class() -> 实例.execute(ctx)；ctx.parameters 直接传入 strategy.generate_signal(data, ctx.parameters)
  - 择时共享数据：TimingDataManager 基于 cache_group_id 和 group_kline_period

结论：参数的“业务含义/校验/默认值”由后端模板定义；“显隐/分组/布局”主要由前端解释。执行时仅使用 parameters 实值。

### 1.2 模板参数与参数组的使用方式
- 后端模型
  - StrategyTemplate.parameters: Dict[str, ParameterConfig]
  - StrategyTemplate.parameterGroups: Dict[str, ParameterGroup]
  - ParameterConfig 字段：type、label、description、required、default、options、validation、unit、group、order、visibleWhen
  - ParameterGroup 字段：parameters、displayMode、prefix、separator、layout
- 后端执行
  - 几乎只消费 ctx.parameters 实值；parameterGroups 仅用于 UI；visibleWhen 逻辑在前端实现
- 前端渲染
  - AdvancedParameterEditor/InlineParameterEditor 读取 parameters、parameterGroups
  - 支持 visibleWhen、select/number/boolean/text、区间判断（operator=区间 时显示第2数值）

必要性判断：
- parameters 承载“参数元数据+默认值+校验+枚举”等，后端、前端都需要，必须存在
- parameterGroups 承载“呈现分组/行文前缀/布局/区间展示”等纯 UI 语义，后端执行不依赖，但对高效、友好的配置体验至关重要。建议保留，但界面语义应更清晰地与后端逻辑解耦

### 1.3 存在的问题
- Schema 不完全一致：前端类型 StrategyParameter 与后端 ParameterConfig 字段名有轻微差异（text/string、min/max 分散在 validation vs 顶层等）
- 可选字段零散：参数的校验(validation)、可见条件(visibleWhen)、单位(unit)等的来源与位置不完全一贯，导致前端适配代码复杂
- 参数组 displayMode/prefix/separator/layout 等含义未被完全利用（移动端/游戏 UI 可进一步发挥）
- 模板返回结构字段命名存在 data.parameters vs parameters、parameter_groups vs parameterGroups 的兼容逻辑

## 二、参数模型与前端对齐建议

### 2.1 统一 ParameterConfig 规范（后端权威）
- 字段建议（保持向后兼容）：
  - type: 'number' | 'string' | 'select' | 'boolean' | 'enum'
  - label: string
  - description?: string
  - required?: boolean
  - default?: any
  - options?: Array<{ label: string; value: any }>
  - unit?: string
  - validation?: { min?: number; max?: number; step?: number; pattern?: string }
  - visibleWhen?: { parameter: string; value: any | any[] }
  - order?: number
- 迁移兼容：若 validation 缺失但存在顶层 min/max/step，后端在返回模板时合并到 validation 中，前端统一从 validation 读取

### 2.2 统一 ParameterGroup 规范（前端呈现）
- 字段保留：parameters: string[]; prefix?: string; layout?: 'horizontal' | 'vertical'; displayMode?: 'inline' | 'block'; separator?: string
- 位置：仅影响 UI 渲染；后端无需依赖

### 2.3 API 返回规范
- GET /strategies/templates/{id}
  - 返回规范化字段：
    - parameters: Record<string, ParameterConfig>
    - parameterGroups: Record<string, ParameterGroup>
    - ui: { icon?: string; color?: string; group?: string; order?: number; form?: any }
  - 去除 data.parameters / parameter_groups 的双形态，保留一份权威结构（短期前端可继续做兜底兼容）

### 2.4 前端渲染对齐
- AdvancedParameterEditor/InlineParameterEditor
  - 统一读取 ParameterConfig.validation 进行 min/max/step/regex
  - 统一 type 映射：string->text；enum==select 归一
  - visibleWhen 保持现逻辑
  - 支持 group.layout、displayMode；移动端: vertical 为主；inline 用于短句条件表达

## 三、移动端游戏风格 UI 集成优化
- 小组件化
  - 策略卡参数片段组件：支持“句式拼装”模式，利用 group.prefix + 参数渲染成语义短句；例如“市值 大于 100 亿元”
  - 区间高亮：当 isRangeCondition=true，组块背景/描边强调（现有 ParameterGroup.tsx 已初步支持）
- 表单交互
  - 紧凑模式 compact=true 默认开启；必要时悬浮说明图标
  - 大按钮/滑杆替代数值输入：对 number 支持移动端滑杆（min/max/step 来源于 validation）
  - Select 增强：弹出式选择器/底部抽屉
- 预设与重置
  - 支持“加载模板默认值”“一键重置”“保存常用预设”
- 可访问性与国际化
  - label/description 走 i18n key 预留（后续模板可提供 i18n 字段）

## 四、改造分阶段计划

### 阶段A：一致性与无感兼容（1周）
- 后端
  - 在 StrategyService.get_strategy_template 中做返回规范化：
    - 兼容 data.parameters/parameter_groups -> 统一标准字段 parameters/parameterGroups
    - 将顶层 min/max/step 合并写入 validation，保留原字段以兼容旧前端
  - 在 StrategyTemplate Pydantic 校验层允许 string/text 同义
- 前端
  - AdvancedParameterEditor 统一从 parameter.validation 读取 min/max/step
  - 类型映射：string|text -> text；enum->select
  - 兼容两种大小写键名（parameterGroups vs parameter_groups）但优先使用驼峰

### 阶段B：UI 表达力增强（1-2周）
- 提供“句式渲染模式”：ParameterGroup + prefix + select/number 拼句
- 滑杆控件引入：对 number 且有 min/max/step 的参数使用滑杆（PC 保留输入框备选）
- Drawer 弹层选择器：优化 select 在移动端的交互

### 阶段C：配置与复用（1周）
- 预设管理：参数值集合的本地保存/云端绑定（基于用户ID）
- 模板级 UI 指南：ui.form 增加 mobileHints，例如 { prefer: 'drawer', size: 'compact' }

### 阶段D：长期优化（可选）
- Schema 版本化：parametersSchemaVersion，便于灰度升级
- 参数依赖表达增强：visibleWhen 支持多条件（数组或表达式）
- 后端参数校验：在执行前按 ParameterConfig.validation 做服务端兜底校验（返回详细提示）

## 五、回答关键问题
1) 全流程：如 1.1 所述，从模板获取 -> 前端编辑 -> 调试/执行 -> 结果/日志/缓存 的链路清晰，组执行支持串行并行、择时共享数据
2) parameters vs parameterGroups：
   - parameters = 业务参数定义与默认值/校验；用于执行和前端渲染
   - parameterGroups = UI 展示与句式组织；不参与执行逻辑，但显著提升可用性与一致性
   - 结论：两者都有必要保留；但应在 API 与前端类型上一致对齐，杜绝歧义
3) Webview 移动端集成：采用阶段B的句式渲染、滑杆、抽屉选择器、紧凑布局，结合 ui.form mobileHints，实现“低输入成本，高表达密度”的游戏化体验

## 六、验收标准与落地建议
- 统一返回结构：后端 GET /templates/{id} 保证提供 parameters/parameterGroups（驼峰）且包含 validation
- 前端渲染：数值型参数有滑杆，区间组有视觉强调；visibleWhen 生效；移动端交互友好
- 兼容性：旧模板无需修改即可正常渲染与执行
- 文档化：在模板目录提供 template.schema.md，给出字段说明与示例

## 附：示例规范化模板片段
- market_cap_filter/template.json（节选、示意）
{
  "parameters": {
    "operator": { "type": "select", "label": "条件", "default": "大于", "options": [ ... ] },
    "market_cap1": { "type": "number", "label": "市值条件1", "default": 100, "unit": "亿元", "validation": { "min": 0, "max": 100000, "step": 1 } },
    "market_cap2": { "type": "number", "label": "市值条件2", "default": null, "unit": "亿元", "visibleWhen": { "parameter": "operator", "value": ["区间", "between"] }, "validation": { "min": 0, "max": 100000 } }
  },
  "parameterGroups": {
    "market_cap_filter": { "parameters": ["operator", "market_cap1", "market_cap2"], "prefix": "市值", "layout": "horizontal", "displayMode": "inline", "separator": "-" }
  }
}


## 七、无兼容性约束的重构方案（理想形态）

在可不考虑历史兼容的前提下，推荐自上而下做“强约束、弱耦合”的统一设计，彻底消除混乱源头：

### 7.1 单一权威模板 Schema v2
- 模板文件统一为 schemaVersion=2 的 JSON，字段固定且驼峰化：
  - template_id: string（保持现有命名，避免与其他 id 混淆）
  - name: string
  - version: string
  - description?: string (纯文本，不再混入 HTML)
  - tags: string[]（功能标签含“选股/择时/回测”）
  - parameters: Record<string, ParameterConfigV2>
  - parameterGroups: Record<string, ParameterGroupV2>（保持原命名，清晰表达含义）
  - ui: UiConfigV2
  - template_code: string（保持现有命名）

- ParameterConfigV2（仅保留必要字段，严格校验）
  - type: 'number' | 'string' | 'select' | 'boolean' | 'multi-select' | 'symbol' | 'date' | 'period'
  - label: string
  - default?: any
  - options?: Array<{ label: string; value: any }>（仅 select/multi-select）
  - unit?: string
  - validation?: { min?: number; max?: number; step?: number; pattern?: string; required?: boolean }
  - visibleWhen?: { all?: Array<{ param: string; equals?: any; in?: any[] }>; any?: Array<{ param: string; equals?: any; in?: any[] }> }
  - help?: string（短提示，纯文本）
  - order?: number

- ParameterGroupV2（纯 UI 定义，不参与执行）
  - title?: string（组标题）
  - style?: 'inline' | 'block' | 'sentence'
  - layout?: 'horizontal' | 'vertical'
  - sentencePrefix?: string（“市值”）
  - sentenceSeparator?: string（“-”）
  - parameters: string[]（组内参数名顺序）

- UiConfigV2
  - icon?: string; color?: string; category?: string; order?: number
  - form?: { layout?: 'vertical' | 'horizontal'; mobile?: { prefer?: 'drawer' | 'modal'; compact?: boolean } }

说明：
- 彻底移除 data.parameters / parameter_groups 等多形态输出；仅输出权威字段
- 坚持“参数负责业务，分组负责展示”，执行侧只读取 parameters 的实值

### 7.2 后端重构
- Model
  - StrategyTemplateV2（Pydantic）严格校验上述 v2 Schema
  - StrategyCard 使用模板的 code + registry 加载策略类
- API
  - GET /templates/{id}：直接返回 v2 结构，无兼容分支
  - 列表 API 亦返回同构结构
- 执行
  - 在 StrategyExecutor 执行前做一次服务端参数校验：按 ParameterConfigV2.validation 规则校验参数实值
  - 仅把“校验后”的 parameters 传入策略类

### 7.3 前端重构
- 类型对齐
  - 以 StrategyParameterV2、ParameterGroupV2 定义严格类型
  - 删除对旧字段的兜底逻辑
- 渲染
  - InlineParameterEditor 读取 parameterGroups 与 parameters
  - 支持 style: 'sentence'（句式拼装）、'block'（面板）、'inline'
  - visibleWhen 支持 all/any 多条件，统一实现
- 控件
  - number 自动渲染滑杆（min/max/step）+ 数字输入并存
  - multi-select、symbol、date、period 提供专用控件
- 交互
  - 内置“重置为模板默认”“保存为预设”
  - 移动端 form.mobile 指示使用 Drawer/Modal 与 compact 布局

### 7.4 模板示例（v2）
{
  "schemaVersion": 2,
  "template_id": "market_cap_filter",
  "name": "市值",
  "version": "2.0.0",
  "tags": ["选股", "基础筛选"],
  "parameters": {
    "operator": { "type": "select", "label": "条件", "default": "大于", "options": [{"label":"大于","value":"gt"},{"label":"小于","value":"lt"},{"label":"区间","value":"between"}] },
    "market_cap_min": { "type": "number", "label": "下限", "default": 100, "unit": "亿元", "validation": { "min": 0, "max": 100000, "step": 1 }, "visibleWhen": { "any": [{"param":"operator","equals":"between"},{"param":"operator","equals":"gt"}] } },
    "market_cap_max": { "type": "number", "label": "上限", "default": 500, "unit": "亿元", "validation": { "min": 0, "max": 100000, "step": 1 }, "visibleWhen": { "any": [{"param":"operator","equals":"between"},{"param":"operator","equals":"lt"}] } }
  },
  "parameterGroups": {
    "sentence": { "style": "sentence", "sentencePrefix": "市值", "sentenceSeparator": "-", "layout": "horizontal", "parameters": ["operator", "market_cap_min", "market_cap_max"] }
  },
  "ui": { "icon": "filter", "color": "#1890ff", "category": "筛选", "form": { "layout": "vertical", "mobile": { "prefer": "drawer", "compact": true } } },
  "template_code": "market_cap_filter/strategy.py"
}

### 7.5 迁移策略
- 一次性脚本迁移 template.json 到 v2 结构（字段重命名与可见条件转换）
- 前端替换类型与解析器，移除兼容代码
- 回归测试：
  - 表单渲染一致（或更优）
  - visibleWhen 多条件正确
  - 服务端校验生效，错误友好提示
  - 执行结果与旧版逻辑一致

### 7.6 预期收益
- 参数模型清晰：无多形态与大小写差异
- UI/执行解耦：分组仅作展示语义，执行只看参数值
- 代码可维护性大幅提升：前后端各自简化兼容分支
- 更强表达力：多条件显隐、句式、专用控件类型

## 八、专家评价与优化建议

### 8.1 方案评价
**优点：**
- 全流程梳理清晰，从模板定义到执行的链路完整
- 识别了关键问题：多形态字段、兼容逻辑复杂、UI/执行职责混淆
- 分阶段实施策略务实，既有渐进式改进也有理想重构方案

**过度设计风险：**
- Schema v2 引入了过多新概念（schemaVersion、all/any 复合条件、多种新控件类型）
- 字段重命名（id->template_id、groups->parameterGroups）增加迁移成本但收益有限
- 新增控件类型（symbol、date、period）可能超出当前业务需求

### 8.2 优化建议

**保持务实原则：**
1. **字段命名保持稳定**
   - template_id：保持现有命名，避免与其他 id 类型混淆 ✓
   - parameterGroups：保持原命名，语义清晰且修改量最小 ✓
   - template_code：保持现有命名 ✓

2. **渐进式改进优于激进重构**
   - 优先解决现有痛点：统一 validation 读取、消除双形态返回
   - 暂缓引入复杂新特性：all/any 复合条件可后续按需添加
   - 控件类型保持克制：先优化现有 number/string/select/boolean，新类型按实际需求逐步添加

3. **简化 Schema v2 设计**
   - 移除 schemaVersion 字段（通过 API 版本控制即可）
   - visibleWhen 保持现有简单结构，避免过度复杂化
   - 专注核心问题：字段一致性、校验统一、UI/执行解耦

### 8.3 修正后的重构方案

**核心原则：最小化改动，最大化收益**

**后端优化：**
- ParameterConfig 统一 validation 字段，移除顶层 min/max/step
- API 返回统一驼峰字段，移除 data.parameters 等兼容形态
- 保持现有字段命名：template_id、parameterGroups、template_code

**前端优化：**
- 统一从 parameter.validation 读取约束条件
- 类型映射简化：string/text 统一，enum->select
- 保持 parameterGroups 命名和现有逻辑

**UI 增强（按需）：**
- number 参数增加滑杆选项（基于 validation.min/max/step）
- select 在移动端使用抽屉式选择器
- 句式渲染利用现有 prefix/separator 逻辑

### 8.4 实施建议
1. **第一步：解决一致性问题**（1-2天）
   - 后端统一返回结构，前端统一解析逻辑
   - 无需新增字段或重命名，仅规范化现有结构

2. **第二步：UI 体验优化**（3-5天）
   - 基于现有 parameterGroups 增强移动端交互
   - 添加滑杆、抽屉等控件，保持向后兼容

3. **第三步：按需扩展**（后续迭代）
   - 根据实际使用反馈决定是否需要复合条件、新控件类型等

**避免的陷阱：**
- 不为了"完美"而引入过多抽象层次
- 不重命名已经清晰的字段
- 不一次性引入所有"可能有用"的特性
- 优先解决现有问题，而非预设未来需求

