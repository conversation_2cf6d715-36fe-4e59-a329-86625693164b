name: ATR动态止损策略
description: 基于平均真实范围的智能风险管理策略，提供动态止损、止盈和仓位管理
version: 1.0.0
author: QuantCard团队
tags:
  - 风险管理
  - ATR指标
  - 动态止损
  - 仓位管理
  - 追踪止损

strategy_class_name: ATRDynamicStopStrategy

parameters:
  atr_period:
    type: int
    default: 14
    min: 7
    max: 30
    description: ATR计算周期
  stop_multiplier:
    type: float
    default: 2.0
    min: 1.0
    max: 5.0
    description: ATR止损倍数
  profit_multiplier:
    type: float
    default: 3.0
    min: 1.5
    max: 6.0
    description: ATR止盈倍数
  risk_per_trade:
    type: float
    default: 0.02
    min: 0.01
    max: 0.05
    description: 单笔交易风险比例
  max_position_size:
    type: float
    default: 0.2
    min: 0.1
    max: 0.5
    description: 最大仓位比例
  max_atr_change:
    type: float
    default: 0.5
    min: 0.2
    max: 1.0
    description: ATR变化警告阈值

data_sources:
  timing:
    database:
      type: questdb
      table: kline_1min
    fields:
      - symbol
      - time
      - open
      - high
      - low
      - close
      - volume
      - amount
    time_field: time
    frequency: 1m
    window_size: 100
    
  monitor:
    database:
      type: questdb
      table: kline_1min
    fields:
      - symbol
      - time
      - open
      - high
      - low
      - close
      - volume
      - amount
    time_field: time
    frequency: 1m
    window_size: 50

outputs:
  timing:
    type: signal
    fields:
      - symbol
      - name
      - direction
      - confidence
      - trigger_condition
      - current_price
      - atr
      - atr_pct
      - stop_long
      - stop_short
      - profit_long
      - profit_short
      - trailing_stop_long
      - trailing_stop_short
      - position_size
      - risk_level
      - volatility_state
      - stop_distance_pct
      
  monitor:
    type: alert
    fields:
      - symbol
      - name
      - risk_signal_type
      - atr_change
      - recommended_action
      - alert_time