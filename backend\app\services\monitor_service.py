from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import logging
from ..utils.error_utils import <PERSON>rrorHand<PERSON>
from ..utils.response_formatter import ResponseFormatter
from ..models.monitor import SystemMetrics, PerformanceMetrics, ErrorLog, UserActivity

try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False
    logging.warning("psutil模块未安装，系统监控功能受限。请运行 pip install psutil 安装此依赖。")

class MonitorService:
    @staticmethod
    async def get_system_metrics() -> Dict[str, Any]:
        """获取系统指标"""
        try:
            if not PSUTIL_AVAILABLE:
                return ResponseFormatter.error(
                    message="无法获取系统指标，psutil模块未安装",
                    code=500
                )
                
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            metrics = {
                'cpu_usage': cpu_percent,
                'memory_usage': memory.percent,
                'disk_usage': disk.percent,
                'timestamp': datetime.now()
            }
            
            await SystemMetrics(**metrics).save()
            return ResponseFormatter.success(
                data=metrics,
                message="获取系统指标成功"
            )
        except Exception as e:
            logging.error(f"获取系统指标失败: {str(e)}")
            ErrorHandler.raise_operation_failed("获取", "系统指标")

    @staticmethod
    async def get_performance_metrics(start_time: datetime, end_time: datetime) -> Dict[str, Any]:
        """获取性能指标"""
        try:
            query = {
                'timestamp': {
                    '$gte': start_time,
                    '$lte': end_time
                }
            }
            
            metrics = await PerformanceMetrics.find(query).to_list()
            total = len(metrics)
            
            return ResponseFormatter.success(
                data=[metric.dict() for metric in metrics],
                total=total,
                message="获取性能指标成功"
            )
        except Exception as e:
            logging.error(f"获取性能指标失败: {str(e)}")
            ErrorHandler.raise_operation_failed("获取", "性能指标")

    @staticmethod
    async def track_error(error_data: Dict[str, Any]) -> Dict[str, Any]:
        """记录错误日志"""
        try:
            error_log = ErrorLog(
                error_type=error_data.get('error_type'),
                error_message=error_data.get('error_message'),
                stack_trace=error_data.get('stack_trace'),
                timestamp=datetime.now()
            )
            
            await error_log.save()
            return ResponseFormatter.success(
                data=error_log.dict(),
                message="记录错误日志成功"
            )
        except Exception as e:
            logging.error(f"记录错误日志失败: {str(e)}")
            ErrorHandler.raise_operation_failed("记录", "错误日志")

    @staticmethod
    async def track_user_activity(activity_data: Dict[str, Any]) -> Dict[str, Any]:
        """记录用户行为"""
        try:
            activity = UserActivity(
                user_id=activity_data.get('user_id'),
                action=activity_data.get('action'),
                resource=activity_data.get('resource'),
                details=activity_data.get('details'),
                timestamp=datetime.now()
            )
            
            await activity.save()
            return ResponseFormatter.success(
                data=activity.dict(),
                message="记录用户行为成功"
            )
        except Exception as e:
            logging.error(f"记录用户行为失败: {str(e)}")
            ErrorHandler.raise_operation_failed("记录", "用户行为")

    @staticmethod
    async def get_error_logs(
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        error_type: Optional[str] = None,
        limit: int = 100
    ) -> Dict[str, Any]:
        """获取错误日志"""
        try:
            query = {}
            if start_time and end_time:
                query['timestamp'] = {
                    '$gte': start_time,
                    '$lte': end_time
                }
            if error_type:
                query['error_type'] = error_type
                
            error_logs = await ErrorLog.find(query).limit(limit).to_list()
            total = len(error_logs)
            
            return ResponseFormatter.success(
                data=[log.dict() for log in error_logs],
                total=total,
                message="获取错误日志成功"
            )
        except Exception as e:
            logging.error(f"获取错误日志失败: {str(e)}")
            ErrorHandler.raise_operation_failed("获取", "错误日志")

    @staticmethod
    async def get_user_activities(
        user_id: Optional[str] = None,
        action: Optional[str] = None,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        limit: int = 100
    ) -> Dict[str, Any]:
        """获取用户行为记录"""
        try:
            query = {}
            if user_id:
                query['user_id'] = user_id
            if action:
                query['action'] = action
            if start_time and end_time:
                query['timestamp'] = {
                    '$gte': start_time,
                    '$lte': end_time
                }
                
            activities = await UserActivity.find(query).limit(limit).to_list()
            total = len(activities)
            
            return ResponseFormatter.success(
                data=[activity.dict() for activity in activities],
                total=total,
                message="获取用户行为记录成功"
            )
        except Exception as e:
            logging.error(f"获取用户行为记录失败: {str(e)}")
            ErrorHandler.raise_operation_failed("获取", "用户行为记录") 