// API 响应基础接口
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
}

// 包装的API响应接口
export interface WrappedResponse<T = any> {
  success: boolean;
  message: string;
  data: T;
}

// 后端成功响应接口
export interface ApiSuccessResponse<T = any> {
  success: boolean;
  message: string;
  data: T;
}

// 策略参数接口
export interface StrategyParameter {
  type: 'number' | 'string' | 'select' | 'enum' | 'boolean';
  label: string;
  description?: string;
  required?: boolean;
  default?: any;
  options?: Array<{ label: string; value: any }>;
  validation?: any;
  unit?: string;
  group?: string;
  order?: number;
  visibleWhen?: {
    parameter: string;
    value: any;
  };
}

// 策略模板接口
export interface StrategyTemplate {
  id: string;
  name: string;
  description: string;
  type: string;
  strategy_type: string;  // 策略功能类型: filter-选股, timing-择时
  version: string;
  author: string;
  stars: number;
  tags: string[];
  parameters: Record<string, StrategyParameter>;
  parameterGroups: Record<string, ParameterGroup>;
  ui: {
    icon?: string;
    color?: string;
    group?: string;
    order?: number;
    form?: {
      layout?: 'horizontal' | 'vertical' | 'inline';
      compact?: boolean;
      showDescription?: boolean;
    };
  };
  is_active: boolean;
  template_code: string;
}

// 策略节点接口
export interface StrategyNode {
  id: string;
  templateId: string;
  name: string;
  description: string;
  type: string;
  status: 'active' | 'inactive' | 'testing' | 'error';
  parameters: Record<string, any>;
  position: { x: number; y: number };
  performance?: {
    returns: number;
    sharpeRatio: number;
    maxDrawdown: number;
    winRate: number;
  };
}

// 策略组合接口
export interface StrategyComposition {
  id: string;
  name: string;
  description: string;
  nodes: StrategyNode[];
  edges: Array<{
    id: string;
    source: string;
    target: string;
    type?: string;
  }>;
  status: 'draft' | 'active' | 'inactive' | 'testing';
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  performance?: {
    totalReturns: number;
    annualizedReturns: number;
    sharpeRatio: number;
    maxDrawdown: number;
    winRate: number;
  };
}

// 分页请求参数
export interface PaginationParams {
  page: number;
  pageSize: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// 分页响应
export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  pageSize: number;
}

// 分页响应包装（后端实际返回）
export interface WrappedPaginatedResponse<T> extends ApiSuccessResponse<PaginatedResponse<T>> {}

// 策略搜索参数
export interface StrategySearchParams {
  page?: number;
  pageSize?: number;
  keyword?: string;
  id?: string;  // 策略模板ID，唯一标识符
  tags?: string;  // 标签，多个用逗号分隔，包含功能标签("选股","择时","回测")
  status?: string;
}

// 策略卡片类型
export interface StrategyCard {
  id: string;
  name: string;
  description: string;
  strategy_type: string;  // 策略功能类型: filter-选股, timing-择时
  icon?: string;
  parameters: StrategyParameter[];
  template_code: string;
  created_at: string;
  updated_at: string;
  created_by?: string;
  is_public: boolean;
  tags: string[];
  version: string;
}

// 参数组配置
export interface ParameterGroup {
  parameters: string[];
  displayMode: 'inline' | 'separate';
  prefix?: string;
  separator?: string;
  layout?: 'horizontal' | 'vertical';
}

// 策略卡模板接口
export interface StrategyTag {
  name: string;
  color: string;
}

export interface StrategyCardTemplate {
  id: string;
  name: string;
  description: string;
  version: string;
  author: string;
  stars: number;
  tags: string[];  // 包含功能标记（"选股"/"择时"/"回测"）
  parameters: Record<string, StrategyParameter>;
  parameterGroups?: Record<string, ParameterGroup>;
  ui?: {
    form?: {
      layout?: 'horizontal' | 'vertical' | 'inline';
      compact?: boolean;
      showDescription?: boolean;
    };
  };
  is_active?: boolean;
  template_code?: string;
  created_at: string;
  updated_at: string;
  usage_count?: number;
}

// 策略卡实例接口（用于策略组中的具体策略卡）
export interface StrategyCardInstance {
  id: string;
  name: string;
  description: string;
  parameters: Record<string, any>;  // 用户配置的具体参数值
  position?: { x: number; y: number };  // 在画布中的位置
  order?: number;  // 在串行执行时的顺序
  template?: StrategyCardTemplate;  // 完整的模板配置（前端使用）
  template_id?: string;  // 模板ID引用（后端存储）
  template_name?: string; // 模板名称（便于显示）
}

// 策略组接口
export interface StrategyGroup {
  id: string;
  name: string;
  description: string;
  group_type?: 'filter' | 'timing';  // 策略组类型: filter-选股, timing-择时
  timing_symbols?: string;  // 择时标的列表，逗号分隔，仅当group_type为timing时有效
  kline_period?: string;  // K线周期，用于统一择时策略组的数据周期
  cards: StrategyCardInstance[];  // 使用策略卡实例
  execution_mode: 'sequential' | 'parallel';
  execution_config?: {
    execution_type?: 'onetime' | 'continuous';
    schedule?: any;
    [key: string]: any;
  };
  status: 'draft' | 'active' | 'inactive' | 'error';
  created_at: string;
  updated_at: string;
  created_by: string;
  is_active: boolean;
  schedule?: {
    type: 'interval' | 'cron';
    value: string;
    timezone?: string;
  };
  risk_settings: {
    max_drawdown?: number;
    stop_loss?: number;
    take_profit?: number;
    [key: string]: any;
  };
  performance_metrics?: {
    returns: number;
    sharpe_ratio: number;
    max_drawdown: number;
    win_rate: number;
    total_returns?: number; // 兼容旧版API可能返回的total_returns字段
  };
  execution_logs?: Array<any>; // 执行日志
}

// 错误响应
export interface ApiError {
  code: number;
  message: string;
  details?: Record<string, string[]>;
  path?: string;
  timestamp?: string;
}