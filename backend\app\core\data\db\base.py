"""
数据库连接管理基础模块
"""
from typing import Dict, Optional, Any, AsyncGenerator
from threading import Lock
import logging
from sqlalchemy.ext.asyncio import AsyncEngine, create_async_engine, AsyncSession
try:
    from sqlalchemy.ext.asyncio import async_sessionmaker
except ImportError:
    # 兼容旧版本SQLAlchemy
    from sqlalchemy.orm import sessionmaker
    async_sessionmaker = sessionmaker
from motor.motor_asyncio import AsyncIOMotorClient
from pymongo.database import Database
from contextlib import asynccontextmanager
import psycopg2
from psycopg2.extras import RealDictCursor
import pandas as pd

from ...config import get_postgres_config, get_mongodb_config, get_questdb_config

logger = logging.getLogger(__name__)

class DatabaseManager:
    """数据库连接管理器"""
    
    def __init__(self):
        self._postgres_engines: Dict[str, AsyncEngine] = {}
        self._session_factories: Dict[str, async_sessionmaker[AsyncSession]] = {}
        self._mongo_clients: Dict[str, AsyncIOMotorClient] = {}
        self._questdb_connections: Dict[str, Any] = {}
        self._init_lock = Lock()
        
        # 数据库连接池配置
        self.pool_size = 20
        self.max_overflow = 30
        self.pool_timeout = 30
        self.pool_recycle = 1800
        self.pool_pre_ping = True
        
        # MongoDB连接配置
        self.mongodb_max_pool_size = 50
        self.mongodb_min_pool_size = 10
        self.mongodb_max_idle_time_ms = 60000  # 60秒
        self.mongodb_wait_queue_timeout_ms = 5000  # 5秒
        self.mongodb_server_selection_timeout_ms = 5000  # 5秒
        self.mongodb_connect_timeout_ms = 5000  # 5秒
        self.mongodb_socket_timeout_ms = 5000  # 5秒
        
        # QuestDB缓存
        self._questdb_cache = {}
        
    async def get_postgres_engine(self, db_config: Dict[str, Any]) -> AsyncEngine:
        """获取PostgreSQL引擎"""
        db_key = f"{db_config['host']}:{db_config['port']}/{db_config['database']}"
        
        if db_key not in self._postgres_engines:
            with self._init_lock:
                if db_key not in self._postgres_engines:
                    url = f"postgresql+asyncpg://{db_config['user']}:{db_config['password']}@{db_config['host']}:{db_config['port']}/{db_config['database']}"
                    engine = create_async_engine(
                        url,
                        pool_size=self.pool_size,
                        max_overflow=self.max_overflow,
                        pool_timeout=self.pool_timeout,
                        pool_recycle=self.pool_recycle,
                        pool_pre_ping=self.pool_pre_ping
                    )
                    self._postgres_engines[db_key] = engine
                    
                    # 创建session factory
                    self._session_factories[db_key] = async_sessionmaker(
                        engine,
                        class_=AsyncSession,
                        expire_on_commit=False
                    )
                    
        return self._postgres_engines[db_key]
        
    @asynccontextmanager
    async def get_session(self, db_name: Optional[str] = None) -> AsyncGenerator[AsyncSession, None]:
        """获取数据库会话
        
        Args:
            db_name: 数据库名称，如果不指定则使用默认数据库
            
        Yields:
            AsyncSession: 数据库会话
        """
        try:
            # 获取数据库配置
            db_config = get_postgres_config(db_name)
            db_key = f"{db_config['host']}:{db_config['port']}/{db_config['database']}"
            
            # 确保引擎已创建
            await self.get_postgres_engine(db_config)
            
            # 获取session factory
            session_factory = self._session_factories[db_key]
            
            # 创建新会话
            async with session_factory() as session:
                yield session
                await session.commit()
                
        except Exception as e:
            logger.error(f"获取数据库会话失败: {str(e)}")
            raise
        
    async def get_postgres_connection(self, db_name: Optional[str] = None) -> AsyncSession:
        """获取PostgreSQL连接（已废弃，请使用get_session）"""
        try:
            # 获取数据库配置
            db_config = get_postgres_config(db_name)
            
            # 获取数据库引擎
            engine = await self.get_postgres_engine(db_config)
            
            # 创建会话
            session = AsyncSession(engine)
            return session
            
        except Exception as e:
            logger.error(f"获取PostgreSQL连接失败: {str(e)}")
            raise
            
    async def get_mongodb_connection(self, db_name: str) -> Database:
        """获取MongoDB连接（兼容旧接口）"""
        return await self.get_mongodb_database(db_name)
        
    async def get_mongodb_client(self, db_name: str) -> AsyncIOMotorClient:
        """获取MongoDB客户端"""
        if db_name not in self._mongo_clients:
            with self._init_lock:
                if db_name not in self._mongo_clients:
                    config = get_mongodb_config(db_name)
                    url = f"{config['protocol']}://{config['host']}:{config['port']}"
                    
                    # 如果有认证信息，添加到URL中
                    if config.get('user') and config.get('password'):
                        url = f"{config['protocol']}://{config['user']}:{config['password']}@{config['host']}:{config['port']}"
                    
                    client = AsyncIOMotorClient(
                        url,
                        maxPoolSize=self.mongodb_max_pool_size,
                        minPoolSize=self.mongodb_min_pool_size,
                        maxIdleTimeMS=self.mongodb_max_idle_time_ms,
                        waitQueueTimeoutMS=self.mongodb_wait_queue_timeout_ms,
                        serverSelectionTimeoutMS=self.mongodb_server_selection_timeout_ms,
                        connectTimeoutMS=self.mongodb_connect_timeout_ms,
                        socketTimeoutMS=self.mongodb_socket_timeout_ms
                    )
                    self._mongo_clients[db_name] = client
                    
        return self._mongo_clients[db_name]
        
    async def get_mongodb_database(self, db_name: str) -> Database:
        """获取MongoDB数据库，使用异步上下文管理器确保连接正确关闭"""
        try:
            client = await self.get_mongodb_client(db_name)
            return client[db_name]
        except Exception as e:
            logger.error(f"获取MongoDB数据库失败: {str(e)}")
            # 如果获取失败，尝试关闭已有的连接然后重新获取
            if db_name in self._mongo_clients:
                try:
                    self._mongo_clients[db_name].close()
                    del self._mongo_clients[db_name]
                except Exception:
                    pass
            raise
        
    async def get_questdb_connection(self, db_config: Optional[Dict[str, Any]] = None) -> Any:
        """
        获取QuestDB连接
        
        Args:
            db_config: QuestDB配置，如果不指定则使用默认配置
            
        Returns:
            QuestDB连接
        """
        if db_config is None:
            db_config = get_questdb_config()
        
        db_key = f"{db_config['host']}:{db_config['port']}"
        
        if db_key not in self._questdb_connections:
            with self._init_lock:
                if db_key not in self._questdb_connections:
                    try:
                        conn = psycopg2.connect(
                            host=db_config['host'],
                            port=db_config['port'],
                            user='admin',  # QuestDB默认用户
                            password='quest',  # QuestDB默认密码
                            dbname='qdb',  # QuestDB默认数据库名
                            connect_timeout=30  # 连接超时
                        )
                        self._questdb_connections[db_key] = conn
                        logger.info(f"成功连接QuestDB: {db_config['host']}:{db_config['port']}")
                    except Exception as e:
                        logger.error(f"连接QuestDB失败: {str(e)}")
                        raise
                        
        return self._questdb_connections[db_key]
    
    async def execute_questdb_query(self, query: str, params: Optional[list] = None) -> pd.DataFrame:
        """
        执行QuestDB查询并返回结果
        
        Args:
            query: SQL查询语句
            params: 查询参数列表
            
        Returns:
            pd.DataFrame: 查询结果
        """
        conn = None
        try:
            conn = await self.get_questdb_connection()
            
            with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                cursor.execute(query, params or [])
                results = cursor.fetchall()
                return pd.DataFrame(results)
        except Exception as e:
            logger.error(f"执行QuestDB查询失败: {str(e)}")
            logger.error(f"查询语句:\n{query}")
            # 返回空DataFrame而不是抛出异常，提高容错性
            return pd.DataFrame()
    
    def get_questdb_cache(self, key: str) -> Any:
        """获取QuestDB缓存"""
        return self._questdb_cache.get(key)
        
    def set_questdb_cache(self, key: str, value: Any) -> None:
        """设置QuestDB缓存"""
        self._questdb_cache[key] = value
        
    def clear_questdb_cache(self, key: Optional[str] = None) -> None:
        """清除QuestDB缓存"""
        if key:
            if key in self._questdb_cache:
                del self._questdb_cache[key]
        else:
            self._questdb_cache.clear()
        
    async def close(self):
        """关闭所有连接"""
        # 关闭PostgreSQL连接
        for engine in self._postgres_engines.values():
            await engine.dispose()
            
        # 关闭MongoDB连接
        for client in self._mongo_clients.values():
            client.close()
            
        # 关闭QuestDB连接
        for conn in self._questdb_connections.values():
            conn.close()
            
        self._postgres_engines.clear()
        self._session_factories.clear()
        self._mongo_clients.clear()
        self._questdb_connections.clear()
        
    async def cleanup_idle_connections(self):
        """清理空闲连接"""
        import gc
        gc.collect()
        return

# 创建全局数据库管理器实例
db_manager = DatabaseManager()

class MongoModel:
    """MongoDB 模型基类"""
    
    @classmethod
    async def get_collection(cls, db_name: str = None):
        """获取集合"""
        if db_name is None:
            if hasattr(cls, "get_db_name") and callable(cls.get_db_name):
                db_name = cls.get_db_name()
            else:
                db_name = "quantcard"  # 默认数据库名
                
        db = await db_manager.get_mongodb_database(db_name)
        
        if hasattr(cls, "get_collection_name") and callable(cls.get_collection_name):
            collection_name = cls.get_collection_name()
        else:
            collection_name = cls.__name__.lower()
            
        return db[collection_name]
        
    async def save(self):
        """保存文档"""
        collection = await self.__class__.get_collection()
        data = {}
        
        # 提取可序列化的属性
        for key, value in self.__dict__.items():
            if key != "id" and not key.startswith("_"):
                data[key] = value
                
        if hasattr(self, 'id') and self.id:
            # 如果有ID，执行更新
            from bson import ObjectId
            await collection.update_one(
                {'_id': ObjectId(self.id)},
                {'$set': data}
            )
            return self
        else:
            # 否则执行创建
            result = await collection.insert_one(data)
            self.id = str(result.inserted_id)
            return self
            
    @classmethod
    async def find_one(cls, filter_dict: dict):
        """查找单个文档"""
        collection = await cls.get_collection()
        doc = await collection.find_one(filter_dict)
        if doc:
            # 将_id转换为id
            if '_id' in doc:
                doc['id'] = doc.pop('_id')
            return cls(**doc)
        return None
        
    @classmethod
    async def find_many(cls, filter_dict: dict, sort=None, limit=None, skip=None):
        """查找多个文档"""
        collection = await cls.get_collection()
        cursor = collection.find(filter_dict)
        
        if sort:
            cursor = cursor.sort(sort)
        if skip:
            cursor = cursor.skip(skip)
        if limit:
            cursor = cursor.limit(limit)
            
        result = []
        async for doc in cursor:
            # 将_id转换为id
            if '_id' in doc:
                doc['id'] = doc.pop('_id')
            result.append(cls(**doc))
            
        return result
        
    @classmethod
    async def delete_one(cls, filter_dict: dict):
        """删除单个文档"""
        collection = await cls.get_collection()
        return await collection.delete_one(filter_dict)
        
    @classmethod
    async def delete_many(cls, filter_dict: dict):
        """删除多个文档"""
        collection = await cls.get_collection()
        return await collection.delete_many(filter_dict)
