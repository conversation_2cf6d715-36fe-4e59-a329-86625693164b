.add-widget-modal {
  .widget-type-title {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 12px;
    color: #333;
  }

  .widget-templates-container {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
    width: 100%;
    margin-bottom: 16px;

    @media (max-width: 576px) {
      grid-template-columns: 1fr;
    }
  }

  .widget-template-radio {
    width: 100%;
    margin-right: 0;
    
    &::before {
      display: none;
    }
  }

  .widget-template-card {
    height: 140px;
    transition: all 0.3s ease;
    border: 1px solid #e8e8e8;
    width: 100%;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    
    &:hover {
      border-color: #1890ff;
      box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
    }
  }

  .ant-radio-checked + .widget-template-card {
    border-color: #1890ff;
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
    
    .widget-template-icon,
    .widget-template-title {
      color: #1890ff;
    }
    
    &::before {
      content: "";
      position: absolute;
      top: 0;
      right: 0;
      border-width: 0 24px 24px 0;
      border-style: solid;
      border-color: #1890ff #1890ff transparent transparent;
    }
  }

  .widget-template-icon {
    font-size: 24px;
    margin-bottom: 12px;
    color: #333;
  }

  .widget-template-title {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 8px;
    color: #333;
  }

  .widget-template-description {
    font-size: 12px;
    color: #666;
    line-height: 1.5;
  }

  // 新增配置选项样式
  .config-options-title {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    margin-bottom: 12px;
    text-align: center;
  }

  .ant-tabs-content {
    margin-top: 10px;
  }

  .ant-form-item {
    margin-bottom: 16px;
  }

  .ant-select {
    width: 100%;
  }
} 