"""
业绩预告筛选策略
"""
import logging
import pandas as pd
from typing import Dict, Any, List, Optional
from datetime import datetime
import yaml
import json
from pathlib import Path

from app.core.strategy.base import UnifiedStrategyCard
from app.core.runtime.types import RuntimeContext, Signal, SignalType
from app.core.runtime.types import StrategyMode
from app.core.data.db.base import db_manager
from sqlalchemy import text

logger = logging.getLogger(__name__)

# 从 template.json 加载配置
template_path = Path(__file__).parent / "template.json"
with open(template_path, "r", encoding="utf-8") as f:
    template = json.load(f)

class PerformanceForecastFilterStrategy(UnifiedStrategyCard):
    """业绩预告筛选策略"""
    
    def __init__(self, context: Optional[RuntimeContext] = None):
        """初始化策略"""
        super().__init__(context)
        
        # 加载配置文件
        config_path = Path(__file__).parent / "config.yaml"
        with open(config_path, "r", encoding="utf-8") as f:
            self.config = yaml.safe_load(f)

    async def prepare_data(self, context: RuntimeContext) -> pd.DataFrame:
        """准备数据"""
        try:
            # 检查串行执行模式
            if not self._check_sequential_mode(context):
                return pd.DataFrame()
            
            # 获取模式配置 - 直接使用执行模式作为数据源配置
            mode = context.mode.value if context.mode else "filter"
            mode_config = self.config["data_sources"].get(mode, self.config["data_sources"]["filter"])
            
            # 获取股票代码列表
            symbols = self._get_stock_symbols(context)
            
            # 检查缓存
            cache_key = f"{self._get_cache_key(context)}_{mode}"
            cached_data = self._data_cache.get(cache_key)
            if cached_data is not None:
                if symbols and not cached_data.empty:
                    filtered_cache = cached_data[cached_data["股票代码"].isin(symbols)]
                    self._log(f"从缓存获取: {len(filtered_cache)}条数据")
                    return filtered_cache
                return cached_data
            
            # 获取数据
            db_type = mode_config["database"]["type"]
            if db_type == "postgresql":
                data = await self._get_data_from_postgresql(mode_config, symbols)
            else:
                self._log(f"不支持的数据库类型: {db_type}", "warning")
                return pd.DataFrame()
            
            if data.empty:
                return pd.DataFrame()
            
            # 缓存数据
            self._data_cache.set(cache_key, data, ttl=60)
            return data
            
        except Exception as e:
            self._log(f"准备数据失败: {str(e)}", "error")
            return pd.DataFrame()
    
    async def _get_data_from_postgresql(self, mode_config: Dict[str, Any], symbols: List[str]) -> pd.DataFrame:
        """从PostgreSQL获取数据"""
        try:
            if "database" not in mode_config or "table" not in mode_config["database"]:
                raise ValueError("配置错误：数据源未定义表名")
            
            table = mode_config["database"]["table"]
            fields = mode_config.get("fields", ["股票代码", "预测指标", "业绩变动幅度", "预告类型"])
            
            fields_str = ", ".join(fields)
            sql_query = f"SELECT {fields_str} FROM {table}"
            
            if symbols:
                placeholders = ", ".join([f"'{symbol}'" for symbol in symbols])
                sql_query += f" WHERE 股票代码 IN ({placeholders})"
            
            async with db_manager.get_session() as session:
                result = await session.execute(text(sql_query))
                rows = result.fetchall()
                return pd.DataFrame(rows, columns=fields)
                
        except Exception as e:
            self._log(f"从PostgreSQL获取数据失败: {str(e)}", "error")
            return pd.DataFrame()
            
    async def generate_signal(self, data: pd.DataFrame, params: Dict[str, Any]) -> List[Signal]:
        """生成信号"""
        try:
            if data.empty:
                return []
            
            # 获取参数
            forecast_indicator = params.get("forecast_indicator", "全部")
            change_magnitude_operator = params.get("change_magnitude_operator", "不做限制")
            change_magnitude_value = params.get("change_magnitude_value", 0)
            forecast_type = params.get("forecast_type", "全部")
            
            # 记录筛选条件
            filter_conditions = []
            if forecast_indicator != "全部":
                filter_conditions.append(f"预测指标为[{forecast_indicator}]")
            if change_magnitude_operator != "不做限制":
                filter_conditions.append(f"业绩变动幅度{change_magnitude_operator}[{change_magnitude_value}%]")
            if forecast_type != "全部":
                filter_conditions.append(f"预告类型为[{forecast_type}]")
                
            self._log(f"筛选条件: {' AND '.join(filter_conditions) if filter_conditions else '无'}")
            
            # 筛选数据
            filtered_data = data.copy()
            
            # 1. 按预测指标筛选
            if forecast_indicator != "全部":
                filtered_data = filtered_data[filtered_data["预测指标"].str.contains(forecast_indicator, na=False)]
            
            # 2. 按业绩变动幅度筛选
            if change_magnitude_operator != "不做限制":
                # 确保业绩变动幅度列的数据类型为数值型
                filtered_data["业绩变动幅度"] = pd.to_numeric(filtered_data["业绩变动幅度"], errors="coerce")
                
                # 确保显示的筛选条件正确包含数值
                filter_conditions = [f for f in filter_conditions if "业绩变动幅度" not in f]
                filter_conditions.append(f"业绩变动幅度{change_magnitude_operator}[{change_magnitude_value}%]")
                
                # 执行筛选
                if change_magnitude_operator == "大于":
                    filtered_data = filtered_data[filtered_data["业绩变动幅度"] > change_magnitude_value]
                elif change_magnitude_operator == "小于":
                    filtered_data = filtered_data[filtered_data["业绩变动幅度"] < change_magnitude_value]
            
            # 3. 按预告类型筛选
            if forecast_type != "全部":
                filtered_data = filtered_data[filtered_data["预告类型"] == forecast_type]
            
            # 获取唯一的股票代码
            stock_codes = filtered_data["股票代码"].unique()
            
            self._log(f"筛选出 {len(stock_codes)}只股票")
            
            # 使用统一的create_signal方法生成信号
            signals = []
            
            for stock_code in stock_codes:
                # 获取该股票的行数据
                stock_data = filtered_data[filtered_data["股票代码"] == stock_code]
                
                # 尝试获取股票名称
                stock_name = stock_data["股票名称"].iloc[0] if "股票名称" in stock_data.columns else ""
                
                signals.append(
                    self.create_signal(
                    symbol=stock_code,
                        name=stock_name,
                    direction="BUY",
                        signal_type="fundamental",
                    confidence=0.8,
                        trigger_condition=" & ".join(filter_conditions),
                        filter_conditions=filter_conditions,
                        forecast_count=len(stock_data),
                        forecast_indicators=stock_data["预测指标"].unique().tolist(),
                        forecast_types=stock_data["预告类型"].unique().tolist()
                    )
                )
            
            return signals
            
        except Exception as e:
            self._log(f"生成信号失败: {str(e)}", "error")
            return [] 