"""
基础缓存实现
提供底层的缓存机制和内存管理
"""
from typing import Dict, Any, Optional, List, Union, Type
from datetime import datetime, timedelta
import sys
import asyncio
import logging
from dataclasses import dataclass
from collections import OrderedDict
import pandas as pd

logger = logging.getLogger(__name__)

@dataclass
class CacheItem:
    """缓存项"""
    key: str
    data: Any
    expire_at: Optional[datetime]
    last_access: datetime = datetime.now()
    access_count: int = 0
    metadata: Dict[str, Any] = None
    
    @property
    def size(self) -> int:
        """获取缓存项大小"""
        return sys.getsizeof(self.data)
    
    def is_expired(self) -> bool:
        """检查是否过期"""
        if self.expire_at is None:
            return False
        return datetime.now() > self.expire_at
        
    def access(self) -> None:
        """记录访问"""
        self.last_access = datetime.now()
        self.access_count += 1

class UnifiedCache:
    """统一的缓存管理器"""
    
    # 单例模式实现
    _instances: Dict[str, 'UnifiedCache'] = {}
    
    @classmethod
    def get_instance(cls, name: str = "default", **kwargs) -> 'UnifiedCache':
        """获取缓存实例
        
        Args:
            name: 缓存实例名称
            **kwargs: 缓存配置参数
            
        Returns:
            UnifiedCache: 缓存实例
        """
        if name not in cls._instances:
            cls._instances[name] = cls(**kwargs)
            logger.info(f"创建缓存实例: {name}")
        return cls._instances[name]
    
    def __init__(
        self,
        data_type: Optional[Type] = None,
        max_size: int = 1000,  # 最大缓存条目数
        max_memory_mb: int = 500,  # 最大内存占用(MB)
        cleanup_interval: int = 300,  # 清理间隔(秒)
        default_ttl: Optional[int] = None  # 默认过期时间(秒)
    ):
        self._cache: Dict[str, CacheItem] = OrderedDict()
        self._lock = asyncio.Lock()
        self._max_size = max_size
        self._max_memory = max_memory_mb * 1024 * 1024  # 转换为字节
        self._cleanup_task = None
        self._total_memory = 0
        self._cleanup_interval = cleanup_interval
        self.data_type = data_type
        self.default_ttl = default_ttl
        
    async def start(self):
        """启动缓存管理器"""
        if not self._cleanup_task:
            self._cleanup_task = asyncio.create_task(self._cleanup_loop())
            logger.info("缓存管理器已启动")
            
    async def stop(self):
        """停止缓存管理器"""
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
            self._cleanup_task = None
            logger.info("缓存管理器已停止")
            
    def get(self, key: str) -> Optional[Any]:
        """获取缓存数据"""
        item = self._cache.get(key)
        if not item or item.is_expired():
            if item:
                self._remove(key)
            return None
            
        item.access()
        return item.data
            
    def set(
        self,
        key: str,
        value: Any,
        ttl: Optional[int] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> None:
        """设置缓存数据"""
        # 类型检查
        if self.data_type and not isinstance(value, self.data_type):
            raise TypeError(f"缓存的数据必须是 {self.data_type.__name__} 类型")
            
        # 如果已存在，先移除
        if key in self._cache:
            self._remove(key)
            
        # 使用默认TTL
        if ttl is None and self.default_ttl is not None:
            ttl = self.default_ttl
            
        # 创建新的缓存项
        expire_at = datetime.now() + timedelta(seconds=ttl) if ttl else None
        
        # 处理元数据
        if metadata is None:
            metadata = {}
            
        # 如果是DataFrame，添加统计信息
        if isinstance(value, pd.DataFrame):
            metadata.update({
                "rows": len(value),
                "columns": list(value.columns),
                "memory_usage": value.memory_usage(deep=True).sum(),
                "cached_at": datetime.now().isoformat()
            })
            
        item = CacheItem(
            key=key,
            data=value,
            expire_at=expire_at,
            metadata=metadata
        )
        
        # 检查内存限制
        new_size = item.size
        if self._total_memory + new_size > self._max_memory:
            self._evict_until_fit(new_size)
            
        # 检查数量限制
        if len(self._cache) >= self._max_size:
            self._evict_one()
            
        # 添加新项
        self._cache[key] = item
        self._total_memory += new_size
            
    def delete(self, key: str) -> bool:
        """删除缓存数据"""
        if key in self._cache:
            self._remove(key)
            return True
        return False
            
    def clear(self) -> None:
        """清空缓存"""
        self._cache.clear()
        self._total_memory = 0
            
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        now = datetime.now()
        active_items = [item for item in self._cache.values() if not item.is_expired()]
        expired_items = [item for item in self._cache.values() if item.is_expired()]
        
        stats = {
            "total_items": len(self._cache),
            "active_items": len(active_items),
            "expired_items": len(expired_items),
            "memory_usage_mb": self._total_memory / (1024 * 1024),
            "memory_limit_mb": self._max_memory / (1024 * 1024),
            "size_limit": self._max_size
        }
        
        if self.data_type:
            stats["data_type"] = self.data_type.__name__
            
        return stats
            
    def _remove(self, key: str) -> None:
        """移除缓存项"""
        if key in self._cache:
            item = self._cache.pop(key)
            self._total_memory -= item.size
            
    def _evict_one(self) -> None:
        """驱逐一个缓存项"""
        if not self._cache:
            return
            
        # 优先移除过期项
        now = datetime.now()
        expired = [k for k, v in self._cache.items() if v.is_expired()]
        if expired:
            self._remove(expired[0])
            return
            
        # 其次按最后访问时间排序
        sorted_items = sorted(
            self._cache.items(),
            key=lambda x: (x[1].last_access, -x[1].access_count)
        )
        if sorted_items:
            self._remove(sorted_items[0][0])
            
    def _evict_until_fit(self, required_size: int) -> None:
        """驱逐缓存项直到有足够空间"""
        while (
            self._total_memory + required_size > self._max_memory
            and self._cache
        ):
            self._evict_one()
            
    async def _cleanup_loop(self) -> None:
        """定期清理过期项"""
        while True:
            try:
                await asyncio.sleep(self._cleanup_interval)
                now = datetime.now()
                expired = [k for k, v in self._cache.items() if v.is_expired()]
                for key in expired:
                    self._remove(key)
                if expired:
                    logger.debug(f"已清理 {len(expired)} 个过期缓存项")
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"清理缓存时出错: {e}")

# 创建全局缓存管理器
cache_manager = {
    "market_data": UnifiedCache.get_instance("market_data", data_type=pd.DataFrame, default_ttl=60),
    "strategy": UnifiedCache.get_instance("strategy", default_ttl=3600),
    "user": UnifiedCache.get_instance("user", default_ttl=3600),
    "system": UnifiedCache.get_instance("system", default_ttl=86400)
}

# 为了保持向后兼容性，保留DataCache类作为UnifiedCache的别名
DataCache = lambda **kwargs: UnifiedCache.get_instance("data_cache", data_type=pd.DataFrame, default_ttl=3600, **kwargs)
