// @use "../styles/themes/cyber-theme" as cyber;
// 暂时注释掉主题导入，稍后统一处理主题系统

.scene-router {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
}

// 场景容器
.scene-container {
  width: 100%;
  height: 100%;
  position: relative;
}

.scene-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  
  // 确保场景内容不会溢出
  overflow: hidden;
}

// 加载器样�?- 浅色主题
.scene-loader {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 50%, #bae6fd 100%);
  color: #1e40af;
  
  .loader-spinner {
    position: relative;
    width: 80px;
    height: 80px;
    margin-bottom: 20px;
    
    .spinner-ring {
      position: absolute;
      border: 2px solid transparent;
      border-top: 2px solid #3b82f6;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      
      &:nth-child(1) {
        width: 80px;
        height: 80px;
        animation-duration: 1s;
      }
      
      &:nth-child(2) {
        width: 60px;
        height: 60px;
        top: 10px;
        left: 10px;
        border-top-color: #8b5cf6;
        animation-duration: 0.8s;
        animation-direction: reverse;
      }
      
      &:nth-child(3) {
        width: 40px;
        height: 40px;
        top: 20px;
        left: 20px;
        border-top-color: #10b981;
        animation-duration: 0.6s;
      }
    }
  }
  
  .loader-text {
    font-size: 18px;
    font-weight: 500;
    letter-spacing: 2px;
    animation: pulse 2s ease-in-out infinite;
    text-transform: uppercase;
  }
  
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
  
  @keyframes pulse {
    0%, 100% { opacity: 0.6; }
    50% { opacity: 1; }
  }
}

// 转换遮罩层已删除 - 不再需要过渡动�?
// 错误状�?.scene-error {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(40, 0, 0, 0.9), rgba(60, 20, 20, 0.8));
  
  .error-content {
    text-align: center;
    color: #ff6b6b;
    
    .error-icon {
      font-size: 60px;
      margin-bottom: 20px;
    }
    
    .error-message {
      font-size: 18px;
      margin-bottom: 30px;
      max-width: 400px;
      line-height: 1.6;
    }
    
    .error-retry-btn {
      padding: 12px 24px;
      background: linear-gradient(45deg, #ff4757, #ff6b6b);
      color: white;
      border: none;
      border-radius: 25px;
      font-size: 16px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      text-transform: uppercase;
      letter-spacing: 1px;
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(255, 107, 107, 0.4);
      }
      
      &:active {
        transform: translateY(0);
      }
    }
  }
}

// 调试信息 (仅开发环�?
.scene-debug-info {
  position: fixed;
  bottom: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.8);
  color: #00ffff;
  padding: 10px;
  border-radius: 8px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  z-index: 10000;
  max-width: 300px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(0, 255, 255, 0.3);
  
  .debug-item {
    margin-bottom: 5px;
    
    span {
      font-weight: bold;
      color: #ffff00;
    }
    
    &:last-child {
      margin-bottom: 0;
    }
  }
}

// 移动端优�?@media (max-width: 768px) {
  .scene-loader {
    .loader-spinner {
      width: 60px;
      height: 60px;
      
      .spinner-ring {
        &:nth-child(1) {
          width: 60px;
          height: 60px;
        }
        
        &:nth-child(2) {
          width: 45px;
          height: 45px;
          top: 7.5px;
          left: 7.5px;
        }
        
        &:nth-child(3) {
          width: 30px;
          height: 30px;
          top: 15px;
          left: 15px;
        }
      }
    }
    
    .loader-text {
      font-size: 14px;
    }
  }
  
  .scene-error {
    .error-content {
      padding: 20px;
      
      .error-icon {
        font-size: 40px;
      }
      
      .error-message {
        font-size: 16px;
      }
      
      .error-retry-btn {
        padding: 10px 20px;
        font-size: 14px;
      }
    }
  }
  
  .scene-debug-info {
    bottom: 5px;
    right: 5px;
    padding: 8px;
    font-size: 10px;
    max-width: 200px;
  }
}

// 高性能模式（降低动画复杂度�?@media (max-width: 768px) and (prefers-reduced-motion: reduce) {
  .scene-loader {
    .loader-spinner {
      .spinner-ring {
        animation-duration: 2s !important;
      }
    }
    
    .loader-text {
      animation: none;
      opacity: 0.8;
    }
  }
}

// 暗黑模式支持
@media (prefers-color-scheme: dark) {
  .scene-debug-info {
    background: rgba(20, 20, 20, 0.95);
    border-color: rgba(0, 255, 255, 0.5);
  }
}

// 动画性能优化
.scene-wrapper {
  will-change: transform, opacity;
  backface-visibility: hidden;
  transform-style: preserve-3d;
}

// 确保场景切换时的层级正确
.scene-container {
  isolation: isolate;
}

.scene-wrapper {
  contain: layout style paint;
}
