"""
用户管理相关API

提供用户信息管理、权限控制等功能
"""
from typing import List, Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query, Body
from datetime import datetime
from bson import ObjectId

from ....models.user import User
from ....schemas.user import UserCreate, UserUpdate, UserResponse, DashboardLayout
from ....core.deps import get_current_user, get_current_active_superuser
from ....utils.response_utils import ResponseFormatter
from ....utils.response_formatter import ResponseFormatter as RF
from ....core.data.db.base import db_manager
import logging

router = APIRouter()
logger = logging.getLogger(__name__)

# 当前用户相关API
@router.get("/me", response_model=UserResponse)
async def read_user_me(
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """获取当前用户信息"""
    logger.info(f"获取当前用户信息: {current_user.get('username')}")
    
    return UserResponse(
        id=current_user["id"],
        username=current_user["username"],
        email=current_user.get("email"),
        is_active=current_user.get("is_active", True),
        is_superuser=current_user.get("is_superuser", False),
        created_at=current_user.get("created_at", datetime.utcnow()),
        updated_at=current_user.get("updated_at", datetime.utcnow()),
        last_login=current_user.get("last_login"),
        avatar=current_user.get("avatar"),
        favorites=current_user.get("favorites", [])
    )

@router.put("/me", response_model=UserResponse)
async def update_user_me(
    user_update: UserUpdate,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """更新当前用户信息"""
    try:
        # 获取数据库连接
        db = await db_manager.get_mongodb_database("quantcard")
        
        # 构建更新数据
        update_data = {}
        for field, value in user_update.dict(exclude_unset=True).items():
            if value is not None:
                update_data[field] = value
        
        if update_data:
            # 更新用户信息
            update_data["updated_at"] = datetime.utcnow()
            await db.users.update_one(
                {"_id": ObjectId(current_user["id"])},
                {"$set": update_data}
            )
            
            # 获取更新后的用户信息
            updated_user = await db.users.find_one({"_id": ObjectId(current_user["id"])})
            if "_id" in updated_user:
                updated_user["id"] = str(updated_user.pop("_id"))
            
            return UserResponse(
                id=updated_user["id"],
                username=updated_user["username"],
                email=updated_user.get("email"),
                is_active=updated_user.get("is_active", True),
                is_superuser=updated_user.get("is_superuser", False),
                created_at=updated_user.get("created_at", datetime.utcnow()),
                updated_at=updated_user.get("updated_at", datetime.utcnow()),
                last_login=updated_user.get("last_login"),
                avatar=updated_user.get("avatar"),
                favorites=updated_user.get("favorites", [])
            )
        
        # 如果没有更新数据，直接返回当前用户信息
        return UserResponse(
            id=current_user["id"],
            username=current_user["username"],
            email=current_user.get("email"),
            is_active=current_user.get("is_active", True),
            is_superuser=current_user.get("is_superuser", False),
            created_at=current_user.get("created_at", datetime.utcnow()),
            updated_at=current_user.get("updated_at", datetime.utcnow()),
            last_login=current_user.get("last_login"),
            avatar=current_user.get("avatar"),
            favorites=current_user.get("favorites", [])
        )
    except Exception as e:
        logger.error(f"更新用户信息失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )

# 用户管理API（管理员权限）
@router.get("/", response_model=List[UserResponse])
async def read_users(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    current_user: Dict[str, Any] = Depends(get_current_active_superuser)
):
    """获取用户列表（仅超级管理员）"""
    try:
        # 获取数据库连接
        db = await db_manager.get_mongodb_database("quantcard")
        
        # 查询用户列表
        users = await db.users.find().skip(skip).limit(limit).to_list(length=limit)
        total = await db.users.count_documents({})
        
        # 转换为响应模型
        user_list = [
            UserResponse(
                id=str(user["_id"]),
                username=user["username"],
                email=user.get("email"),
                is_active=user.get("is_active", True),
                is_superuser=user.get("is_superuser", False),
                created_at=user.get("created_at", datetime.utcnow()),
                updated_at=user.get("updated_at", datetime.utcnow()),
                last_login=user.get("last_login"),
                avatar=user.get("avatar"),
                favorites=user.get("favorites", [])
            )
            for user in users
        ]
        
        logger.info(f"获取用户列表成功，共{len(user_list)}条记录")
        return user_list
    except Exception as e:
        logger.error(f"获取用户列表失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )

@router.post("/", response_model=UserResponse)
async def create_user(
    user_data: UserCreate,
    current_user: Dict[str, Any] = Depends(get_current_active_superuser)
):
    """创建新用户（仅超级管理员）"""
    try:
        logger.info(f"尝试创建新用户: {user_data.username}")
        
        # 获取数据库连接
        db = await db_manager.get_mongodb_database("quantcard")
        
        # 检查用户名是否已存在
        existing_user = await db.users.find_one({"username": user_data.username})
        if existing_user:
            logger.warning(f"用户名已存在: {user_data.username}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="用户名已存在"
            )
        
        # 创建新用户
        from passlib.context import CryptContext
        pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
        hashed_password = pwd_context.hash(user_data.password)
        
        user_dict = {
            "username": user_data.username,
            "email": user_data.email,
            "hashed_password": hashed_password,
            "is_active": True,
            "is_superuser": user_data.is_superuser,
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow()
        }
        
        result = await db.users.insert_one(user_dict)
        user_dict["id"] = str(result.inserted_id)
        
        logger.info(f"新用户创建成功: {user_data.username}")
        
        return UserResponse(
            id=user_dict["id"],
            username=user_dict["username"],
            email=user_dict.get("email"),
            is_active=user_dict.get("is_active", True),
            is_superuser=user_dict.get("is_superuser", False),
            created_at=user_dict.get("created_at", datetime.utcnow()),
            updated_at=user_dict.get("updated_at", datetime.utcnow())
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建用户失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建用户失败: {str(e)}"
        )

@router.get("/{user_id}", response_model=UserResponse)
async def read_user(
    user_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """获取指定用户信息"""
    try:
        # 检查权限
        if current_user["id"] != user_id and not current_user.get("is_superuser"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有足够的权限"
            )
        
        # 获取数据库连接
        db = await db_manager.get_mongodb_database("quantcard")
        
        # 查询用户
        try:
            user = await db.users.find_one({"_id": ObjectId(user_id)})
        except Exception:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无效的用户ID格式"
            )
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )
        
        # 转换为响应模型
        return UserResponse(
            id=str(user["_id"]),
            username=user["username"],
            email=user.get("email"),
            is_active=user.get("is_active", True),
            is_superuser=user.get("is_superuser", False),
            created_at=user.get("created_at", datetime.utcnow()),
            updated_at=user.get("updated_at", datetime.utcnow()),
            last_login=user.get("last_login"),
            avatar=user.get("avatar"),
            favorites=user.get("favorites", [])
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取用户信息失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )

@router.put("/{user_id}", response_model=UserResponse)
async def update_user(
    user_id: str,
    user_update: UserUpdate,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """更新指定用户信息"""
    try:
        # 检查权限
        if current_user["id"] != user_id and not current_user.get("is_superuser"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有足够的权限"
            )
            
        # 获取数据库连接
        db = await db_manager.get_mongodb_database("quantcard")
        
        try:
            # 查询用户是否存在
            user = await db.users.find_one({"_id": ObjectId(user_id)})
        except Exception:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无效的用户ID格式"
            )
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )
        
        # 构建更新数据
        update_data = {}
        for field, value in user_update.dict(exclude_unset=True).items():
            if value is not None:
                update_data[field] = value
        
        if update_data:
            # 添加更新时间
            update_data["updated_at"] = datetime.utcnow()
            
            # 更新用户信息
            await db.users.update_one(
                {"_id": ObjectId(user_id)},
                {"$set": update_data}
            )
            
            # 获取更新后的用户信息
            updated_user = await db.users.find_one({"_id": ObjectId(user_id)})
            
            return UserResponse(
                id=str(updated_user["_id"]),
                username=updated_user["username"],
                email=updated_user.get("email"),
                is_active=updated_user.get("is_active", True),
                is_superuser=updated_user.get("is_superuser", False),
                created_at=updated_user.get("created_at", datetime.utcnow()),
                updated_at=updated_user.get("updated_at", datetime.utcnow()),
                last_login=updated_user.get("last_login"),
                avatar=updated_user.get("avatar"),
                favorites=updated_user.get("favorites", [])
            )
        
        # 如果没有更新数据，直接返回原用户信息
        return UserResponse(
            id=str(user["_id"]),
            username=user["username"],
            email=user.get("email"),
            is_active=user.get("is_active", True),
            is_superuser=user.get("is_superuser", False),
            created_at=user.get("created_at", datetime.utcnow()),
            updated_at=user.get("updated_at", datetime.utcnow()),
            last_login=user.get("last_login"),
            avatar=user.get("avatar"),
            favorites=user.get("favorites", [])
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新用户信息失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )

@router.delete("/{user_id}")
async def delete_user(
    user_id: str,
    current_user: Dict[str, Any] = Depends(get_current_active_superuser)
):
    """删除指定用户（仅超级管理员）"""
    try:
        # 获取数据库连接
        db = await db_manager.get_mongodb_database("quantcard")
        
        # 检查不能删除自己
        if current_user["id"] == user_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="不能删除当前登录的用户"
            )
        
        try:
            result = await db.users.delete_one({"_id": ObjectId(user_id)})
        except Exception:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无效的用户ID格式"
            )
            
        if result.deleted_count == 0:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )
            
        return RF.success(message="用户删除成功")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除用户失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )

@router.post("/layout", response_model=Dict[str, Any])
async def save_user_layout(
    layout_data: Dict[str, Any] = Body(...),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """保存用户仪表板布局"""
    try:
        # 获取数据库连接
        db = await db_manager.get_mongodb_database("quantcard")
        
        # 更新用户的布局信息
        await db.users.update_one(
            {"_id": ObjectId(current_user["id"])},
            {"$set": {
                "dashboard_layout": layout_data.get("layout", []),
                "updated_at": datetime.utcnow()
            }}
        )
        
        logger.info(f"用户 {current_user['username']} 保存仪表板布局成功")
        return {"status": "success", "message": "布局保存成功"}
    except Exception as e:
        logger.error(f"保存用户布局失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"保存布局失败: {str(e)}"
        )

@router.get("/current/layout", response_model=Dict[str, Any])
async def get_user_layout(
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """获取当前用户的仪表板布局"""
    try:
        # 获取数据库连接
        db = await db_manager.get_mongodb_database("quantcard")
        
        # 查询用户信息
        user = await db.users.find_one({"_id": ObjectId(current_user["id"])})
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )
        
        # 获取布局信息
        layout = user.get("dashboard_layout", [])
        
        logger.info(f"获取用户 {current_user['username']} 的仪表板布局成功")
        return {"layout": layout}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取用户布局失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取布局失败: {str(e)}"
        ) 