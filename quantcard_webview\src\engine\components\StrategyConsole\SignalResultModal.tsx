/**
 * 📊 信号结果模态框组件
 * 展示策略执行后的信号结果
 */

import React from 'react'
import styled from 'styled-components'
import { motion, AnimatePresence } from 'framer-motion'

// 🎨 样式组件
const ModalOverlay = styled(motion.div)`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
`

const ModalContent = styled(motion.div)`
  background: #ffffff;
  border-radius: 20px;
  width: 100%;
  max-width: 700px;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
`

const ModalHeader = styled.div`
  padding: 1.5rem;
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
`

const ModalTitle = styled.h2`
  margin: 0;
  font-size: 1.25rem;
  font-weight: 700;
  color: #1a202c;
`

const CloseButton = styled(motion.button)`
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 50%;
  background: #f8fafc;
  color: #64748b;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  
  &:hover {
    background: #e2e8f0;
  }
`

const ModalBody = styled.div`
  padding: 1.5rem;
  max-height: 60vh;
  overflow-y: auto;
`

const SignalList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
`

const SignalItem = styled(motion.div)`
  background: #f8fafc;
  border-radius: 12px;
  padding: 1rem;
  border: 1px solid #e2e8f0;
`

const SignalHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
`

const StockCode = styled.div`
  font-size: 1rem;
  font-weight: 700;
  color: #1a202c;
`

const StockName = styled.div`
  font-size: 0.875rem;
  color: #64748b;
  margin-top: 0.25rem;
`

const SignalPrice = styled.div<{ $change?: number }>`
  font-size: 1.1rem;
  font-weight: 600;
  color: ${props => 
    props.$change && props.$change > 0 ? '#10b981' : 
    props.$change && props.$change < 0 ? '#ef4444' : '#1a202c'
  };
`

const SignalDetails = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 0.75rem;
`

const DetailItem = styled.div`
  text-align: center;
`

const DetailValue = styled.div`
  font-size: 0.875rem;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 0.25rem;
`

const DetailLabel = styled.div`
  font-size: 0.75rem;
  color: #64748b;
`

const EmptyState = styled.div`
  text-align: center;
  padding: 3rem 1rem;
  color: #64748b;
`

const SummaryCard = styled.div`
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px;
  padding: 1rem;
  margin-bottom: 1.5rem;
  text-align: center;
`

const SummaryTitle = styled.div`
  font-size: 1.1rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
`

const SummaryCount = styled.div`
  font-size: 2rem;
  font-weight: 700;
`

// 🎮 组件属性
interface SignalResultModalProps {
  isOpen: boolean
  onClose: () => void
  signals: any[]
  strategyName?: string
}

// 🎮 信号结果模态框组件
function SignalResultModal({ 
  isOpen, 
  onClose, 
  signals = [], 
  strategyName = '策略' 
}: SignalResultModalProps) {
  
  // 🎨 格式化价格
  const formatPrice = (price: number) => {
    if (!price) return '-'
    return price.toFixed(2)
  }
  
  // 🎨 格式化市值
  const formatMarketCap = (value: number) => {
    if (!value) return '-'
    if (value >= 100000000) {
      return (value / 100000000).toFixed(2) + '亿'
    } else if (value >= 10000) {
      return (value / 10000).toFixed(2) + '万'
    }
    return value.toFixed(2)
  }
  
  // 🎨 格式化涨跌幅
  const formatChange = (change: number) => {
    if (!change) return '0.00%'
    const sign = change > 0 ? '+' : ''
    return `${sign}${change.toFixed(2)}%`
  }
  
  if (!isOpen) return null

  return (
    <AnimatePresence>
      <ModalOverlay
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        onClick={onClose}
      >
        <ModalContent
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.9, opacity: 0 }}
          onClick={(e) => e.stopPropagation()}
        >
          <ModalHeader>
            <ModalTitle>📊 {strategyName} 执行结果</ModalTitle>
            <CloseButton
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={onClose}
            >
              ✕
            </CloseButton>
          </ModalHeader>
          
          <ModalBody>
            {signals.length > 0 && (
              <SummaryCard>
                <SummaryTitle>筛选结果</SummaryTitle>
                <SummaryCount>{signals.length}</SummaryCount>
                <div style={{ fontSize: '0.875rem', marginTop: '0.5rem' }}>
                  个符合条件的股票
                </div>
              </SummaryCard>
            )}
            
            {signals.length === 0 ? (
              <EmptyState>
                <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>📊</div>
                <div>暂无信号数据</div>
              </EmptyState>
            ) : (
              <SignalList>
                {signals.map((signal, index) => (
                  <SignalItem
                    key={signal.code || index}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                  >
                    <SignalHeader>
                      <div>
                        <StockCode>{signal.code || signal.symbol}</StockCode>
                        <StockName>
                          {signal.market_data?.name || signal.name || '未知股票'}
                        </StockName>
                      </div>
                      <SignalPrice $change={signal.market_data?.day60_change}>
                        ¥{formatPrice(signal.market_data?.price || signal.price)}
                      </SignalPrice>
                    </SignalHeader>
                    
                    <SignalDetails>
                      <DetailItem>
                        <DetailValue>
                          {formatMarketCap(signal.market_data?.market_cap)}
                        </DetailValue>
                        <DetailLabel>市值</DetailLabel>
                      </DetailItem>
                      
                      <DetailItem>
                        <DetailValue>
                          {formatChange(signal.market_data?.day60_change || 0)}
                        </DetailValue>
                        <DetailLabel>60日涨跌幅</DetailLabel>
                      </DetailItem>
                      
                      <DetailItem>
                        <DetailValue>
                          {signal.market_data?.industry || '-'}
                        </DetailValue>
                        <DetailLabel>行业</DetailLabel>
                      </DetailItem>
                    </SignalDetails>
                  </SignalItem>
                ))}
              </SignalList>
            )}
          </ModalBody>
        </ModalContent>
      </ModalOverlay>
    </AnimatePresence>
  )
}

export default SignalResultModal
