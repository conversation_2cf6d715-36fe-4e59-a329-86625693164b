/**
 * 📊 我的策略页面组件
 * 展示和管理用户的策略组，支持执行、编辑、删除等操作
 */

import React, { useEffect } from 'react'
import styled from 'styled-components'
import { motion } from 'framer-motion'
import { useStrategyState, useUIState } from '../../../store/hooks'

// 🎨 样式组件
const PageSection = styled.div`
  margin-bottom: 2rem;
`

const SectionTitle = styled.h3`
  margin: 0 0 1rem 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
  display: flex;
  align-items: center;
  gap: 0.5rem;
`

const StrategyCard = styled(motion.div)`
  background: #ffffff;
  border-radius: 12px;
  padding: 1rem;
  margin-bottom: 1rem;
  border: 1px solid #e2e8f0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px 12px 0 0;
  }
`

const StrategyHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.75rem;
`

const StrategyName = styled.h4`
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: #333;
`

const StrategyMeta = styled.div`
  display: flex;
  gap: 0.5rem;
  margin-top: 0.25rem;
  flex-wrap: wrap;
`

const Badge = styled.span<{ $variant: 'status' | 'type' | 'count' }>`
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  
  ${props => {
    switch (props.$variant) {
      case 'status':
        return `
          background: #10b98120;
          color: #10b981;
        `
      case 'type':
        return `
          background: #3b82f620;
          color: #3b82f6;
        `
      case 'count':
        return `
          background: #6b728020;
          color: #6b7280;
        `
      default:
        return ''
    }
  }}
`

const StrategyDescription = styled.div`
  font-size: 0.85rem;
  color: #666;
  margin-bottom: 1rem;
  line-height: 1.4;
`

const PerformanceMetrics = styled.div`
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.5rem;
  margin-bottom: 1rem;
  padding: 0.75rem;
  background: #f8f9fa;
  border-radius: 8px;
`

const MetricItem = styled.div`
  text-align: center;
`

const MetricValue = styled.div`
  font-size: 1.1rem;
  font-weight: bold;
  color: #333;
`

const MetricLabel = styled.div`
  font-size: 0.8rem;
  color: #666;
`

const ActionButtons = styled.div`
  display: flex;
  gap: 0.5rem;
  justify-content: flex-end;
  margin-top: 1rem;
`

const ActionButton = styled(motion.button)<{ $variant: 'primary' | 'secondary' }>`
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 8px;
  font-size: 0.8rem;
  font-weight: 500;
  cursor: pointer;
  
  ${props => props.$variant === 'primary' ? `
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
  ` : `
    background: #f8fafc;
    color: #333;
    border: 1px solid #e2e8f0;
  `}
  
  &:hover {
    transform: translateY(-1px);
  }
`

const EmptyState = styled.div`
  text-align: center;
  padding: 3rem 1rem;
  color: #666;
`

const LoadingState = styled.div`
  text-align: center;
  padding: 2rem;
  color: #666;
`

const CreateButton = styled(motion.button)`
  width: 100%;
  padding: 1rem;
  margin-bottom: 1rem;
  border: none;
  border-radius: 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  
  &:hover {
    transform: translateY(-1px);
  }
`

// 🎮 我的策略页面组件
function MyStrategiesPage() {
  const { groups, loading, error, loadGroups } = useStrategyState()
  const { switchScene } = useUIState()

  // 🔄 加载策略组
  useEffect(() => {
    console.log('MyStrategiesPage mounted, loading groups...')
    loadGroups()
  }, [loadGroups])

  // 调试日志
  useEffect(() => {
    console.log('MyStrategiesPage - Strategy data:', { groups, loading, error })
  }, [groups, loading, error])

  return (
    <PageSection>
      <SectionTitle>
        📊 我的策略
      </SectionTitle>
      
      {/* 创建策略按钮 */}
      <CreateButton
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
        onClick={() => switchScene('StrategyCreation')}
      >
        <span>➕</span>
        创建新策略
      </CreateButton>
      
      {loading ? (
        <LoadingState>加载中...</LoadingState>
      ) : error ? (
        <EmptyState>
          <div style={{ fontSize: '2rem', marginBottom: '1rem' }}>⚠️</div>
          <div>加载失败: {error}</div>
        </EmptyState>
      ) : groups.length === 0 ? (
        <EmptyState>
          <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>📊</div>
          <div>暂无策略组合</div>
        </EmptyState>
      ) : (
        groups.map((strategy, index) => (
          <StrategyCard
            key={strategy.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <StrategyHeader>
              <div>
                <StrategyName>{strategy.name}</StrategyName>
                <StrategyMeta>
                  <Badge $variant="status">
                    {strategy.status === 'active' ? '运行中' : '已停止'}
                  </Badge>
                  <Badge $variant="type">
                    {strategy.group_type === 'timing' ? '择时策略' : '选股策略'}
                  </Badge>
                  <Badge $variant="count">
                    {strategy.cards?.length || 0} 张卡片
                  </Badge>
                </StrategyMeta>
              </div>
            </StrategyHeader>
            
            {strategy.description && (
              <StrategyDescription>
                {strategy.description}
              </StrategyDescription>
            )}
            
            {/* 性能指标 */}
            {strategy.performance_metrics && (
              <PerformanceMetrics>
                <MetricItem>
                  <MetricValue>
                    {strategy.performance_metrics.total_return?.toFixed(2) || '0.00'}%
                  </MetricValue>
                  <MetricLabel>总收益</MetricLabel>
                </MetricItem>
                <MetricItem>
                  <MetricValue>
                    {strategy.performance_metrics.win_rate?.toFixed(1) || '0.0'}%
                  </MetricValue>
                  <MetricLabel>胜率</MetricLabel>
                </MetricItem>
              </PerformanceMetrics>
            )}
            
            <ActionButtons>
              <ActionButton
                $variant="secondary"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => console.log('编辑策略:', strategy.id)}
              >
                编辑
              </ActionButton>
              <ActionButton
                $variant="primary"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => console.log('执行策略:', strategy.id)}
              >
                {strategy.status === 'active' ? '停止' : '执行'}
              </ActionButton>
            </ActionButtons>
          </StrategyCard>
        ))
      )}
    </PageSection>
  )
}

export default MyStrategiesPage
