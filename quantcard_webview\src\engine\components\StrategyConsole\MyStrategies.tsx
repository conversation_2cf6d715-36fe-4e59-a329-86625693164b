/**
 * 📊 我的策略页面组件
 * 展示和管理用户的策略组，支持执行、编辑、删除等操作
 */

import React, { useEffect, useState } from 'react'
import styled from 'styled-components'
import { motion } from 'framer-motion'
import { useStrategyState, useUIState } from '../../../store/hooks'
import StrategyCard from './StrategyCard'
import StrategyViewModal from './StrategyViewModal'
import StrategySettingsModal from './StrategySettingsModal'
import SignalResultModal from './SignalResultModal'

// 🎨 样式组件
const PageSection = styled.div`
  margin-bottom: 2rem;
`

const SectionTitle = styled.h3`
  margin: 0 0 1rem 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
  display: flex;
  align-items: center;
  gap: 0.5rem;
`



const EmptyState = styled.div`
  text-align: center;
  padding: 3rem 1rem;
  color: #666;
`

const LoadingState = styled.div`
  text-align: center;
  padding: 2rem;
  color: #666;
`

const CreateButton = styled(motion.button)`
  width: 100%;
  padding: 1rem;
  margin-bottom: 1rem;
  border: none;
  border-radius: 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;

  &:hover {
    transform: translateY(-1px);
  }

  &:active {
    transform: scale(0.98);
  }
`

const RefreshButton = styled(motion.button)`
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  width: 56px;
  height: 56px;
  border: none;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
  z-index: 100;
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;

  &:active {
    transform: scale(0.95);
  }
`

const StrategiesList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding-bottom: 5rem; /* 为浮动按钮留出空间 */
`

// 🎮 我的策略页面组件
function MyStrategiesPage() {
  const {
    groups,
    loading,
    error,
    loadGroups,
    executeGroup,
    startGroup,
    stopGroup,
    updateGroup,
    deleteGroup
  } = useStrategyState()
  const { switchScene } = useUIState()

  // 🎨 模态框状态
  const [viewModalOpen, setViewModalOpen] = useState(false)
  const [settingsModalOpen, setSettingsModalOpen] = useState(false)
  const [signalResultModalOpen, setSignalResultModalOpen] = useState(false)
  const [selectedStrategyId, setSelectedStrategyId] = useState<string | null>(null)
  const [executingId, setExecutingId] = useState<string | null>(null)
  const [executionResult, setExecutionResult] = useState<{
    signals: any[]
    strategyName: string
  } | null>(null)
  const [refreshing, setRefreshing] = useState(false)

  // 🔄 加载策略组
  useEffect(() => {
    console.log('MyStrategiesPage mounted, loading groups...')
    loadGroups()
  }, [loadGroups])

  // 调试日志
  useEffect(() => {
    console.log('MyStrategiesPage - Strategy data:', { groups, loading, error })
  }, [groups, loading, error])

  // 🎯 处理策略执行
  const handleExecute = async (strategyId: string) => {
    setExecutingId(strategyId)
    try {
      const result = await executeGroup(strategyId)
      console.log('策略执行成功:', strategyId, result)

      // 如果有信号结果，显示结果模态框
      if (result?.data?.signals && result.data.signals.length > 0) {
        const strategy = groups.find(g => g.id === strategyId)
        setExecutionResult({
          signals: result.data.signals,
          strategyName: strategy?.name || '策略'
        })
        setSignalResultModalOpen(true)
      }
    } catch (error) {
      console.error('策略执行失败:', error)
    } finally {
      setExecutingId(null)
    }
  }

  // 🚀 处理策略启动
  const handleStart = async (strategyId: string) => {
    setExecutingId(strategyId)
    try {
      await startGroup(strategyId)
      console.log('策略启动成功:', strategyId)
    } catch (error) {
      console.error('策略启动失败:', error)
    } finally {
      setExecutingId(null)
    }
  }

  // 🛑 处理策略停止
  const handleStop = async (strategyId: string) => {
    setExecutingId(strategyId)
    try {
      await stopGroup(strategyId)
      console.log('策略停止成功:', strategyId)
    } catch (error) {
      console.error('策略停止失败:', error)
    } finally {
      setExecutingId(null)
    }
  }

  // 📊 处理查看策略
  const handleView = (strategyId: string) => {
    setSelectedStrategyId(strategyId)
    setViewModalOpen(true)
  }

  // ⚙️ 处理策略设置
  const handleSettings = (strategyId: string) => {
    setSelectedStrategyId(strategyId)
    setSettingsModalOpen(true)
  }

  // 🗑️ 处理删除策略
  const handleDelete = async (strategyId: string) => {
    try {
      await deleteGroup(strategyId)
      console.log('策略删除成功:', strategyId)
    } catch (error) {
      console.error('策略删除失败:', error)
    }
  }

  // 🔄 处理刷新
  const handleRefresh = async () => {
    setRefreshing(true)
    try {
      await loadGroups()
    } catch (error) {
      console.error('刷新失败:', error)
    } finally {
      setRefreshing(false)
    }
  }

  return (
    <PageSection>
      <SectionTitle>
        📊 我的策略
      </SectionTitle>
      
      {/* 创建策略按钮 */}
      <CreateButton
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
        onClick={() => switchScene('StrategyCreation')}
      >
        <span>➕</span>
        创建新策略
      </CreateButton>
      
      {loading ? (
        <LoadingState>加载中...</LoadingState>
      ) : error ? (
        <EmptyState>
          <div style={{ fontSize: '2rem', marginBottom: '1rem' }}>⚠️</div>
          <div>加载失败: {error}</div>
        </EmptyState>
      ) : groups.length === 0 ? (
        <EmptyState>
          <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>📊</div>
          <div>暂无策略组合</div>
        </EmptyState>
      ) : (
        <StrategiesList>
          {groups.map((strategy, index) => (
            <StrategyCard
              key={strategy.id}
              strategy={strategy}
              onExecute={handleExecute}
              onStart={handleStart}
              onStop={handleStop}
              onView={handleView}
              onSettings={handleSettings}
              onDelete={handleDelete}
              loading={loading}
              executingId={executingId}
            />
          ))}
        </StrategiesList>
      )}

      {/* 浮动刷新按钮 */}
      <RefreshButton
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
        onClick={handleRefresh}
        disabled={refreshing}
        animate={refreshing ? { rotate: 360 } : { rotate: 0 }}
        transition={refreshing ? { duration: 1, repeat: Infinity, ease: "linear" } : {}}
      >
        🔄
      </RefreshButton>

      {/* 查看模态框 */}
      <StrategyViewModal
        isOpen={viewModalOpen}
        onClose={() => {
          setViewModalOpen(false)
          setSelectedStrategyId(null)
        }}
        strategyId={selectedStrategyId}
      />

      {/* 设置模态框 */}
      <StrategySettingsModal
        isOpen={settingsModalOpen}
        onClose={() => {
          setSettingsModalOpen(false)
          setSelectedStrategyId(null)
        }}
        strategyId={selectedStrategyId}
      />

      {/* 信号结果模态框 */}
      <SignalResultModal
        isOpen={signalResultModalOpen}
        onClose={() => {
          setSignalResultModalOpen(false)
          setExecutionResult(null)
        }}
        signals={executionResult?.signals || []}
        strategyName={executionResult?.strategyName}
      />
    </PageSection>
  )
}

export default MyStrategiesPage
