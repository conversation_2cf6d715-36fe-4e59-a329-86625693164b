@use "sass:map";
@use "sass:color";
@use "@/styles/themes/variables" as *;

.quantum-button {
  border-radius: map.get($border-radius, full);
  transition: map.get($transition, base);
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: map.get($spacing, 2);
  position: relative;
  overflow: hidden;
  
  &--primary {
    background: linear-gradient(45deg, $color-primary, color.adjust($color-primary, $lightness: 20%));
    border: none;
    color: map.get($color-neutral, white);
    box-shadow: map.get($shadow, sm);

    &:hover {
      transform: translateY(-2px);
      box-shadow: map.get($hover-effect, glow);
      background: linear-gradient(45deg, color.adjust($color-primary, $lightness: -5%), $color-primary);
    }

    &:active {
      transform: translateY(0);
      background: linear-gradient(45deg, color.adjust($color-primary, $lightness: -10%), color.adjust($color-primary, $lightness: -5%));
    }

    &:disabled {
      background: map.get($color-neutral, gray-200);
      transform: none;
      box-shadow: none;
      cursor: not-allowed;
    }
  }

  &--secondary {
    background: transparent;
    border: 1px solid map.get($color-neutral, gray-200);
    color: map.get($color-neutral, gray-600);

    &:hover {
      border-color: $color-primary;
      color: $color-primary;
      background: color.adjust($color-primary, $alpha: -0.95);
    }

    &:active {
      background: color.adjust($color-primary, $alpha: -0.9);
    }

    &:disabled {
      border-color: map.get($color-neutral, gray-100);
      color: map.get($color-neutral, gray-300);
      cursor: not-allowed;
    }
  }

  &--outline {
    background: transparent;
    border: 2px solid $color-primary;
    color: $color-primary;

    &:hover {
      background: color.adjust($color-primary, $alpha: -0.95);
      transform: translateY(-2px);
      box-shadow: map.get($shadow, sm);
    }

    &:active {
      transform: translateY(0);
      background: color.adjust($color-primary, $alpha: -0.9);
    }

    &:disabled {
      border-color: map.get($color-neutral, gray-200);
      color: map.get($color-neutral, gray-300);
      cursor: not-allowed;
    }
  }

  &--small {
    height: 32px;
    padding: 0 map.get($spacing, 3);
    font-size: map.get($font-size, sm);
  }

  &--medium {
    height: 40px;
    padding: 0 map.get($spacing, 4);
    font-size: map.get($font-size, base);
  }

  &--large {
    height: 48px;
    padding: 0 map.get($spacing, 5);
    font-size: map.get($font-size, lg);
  }

  &--full-width {
    width: 100%;
  }

  .anticon {
    font-size: 1.2em;
    display: inline-flex;
    align-items: center;
  }

  // 加载状态
  &--loading {
    position: relative;
    pointer-events: none;

    .anticon-loading {
      margin-right: map.get($spacing, 2);
    }
  }

  // 波纹效果
  &::after {
    content: '';
    display: block;
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    pointer-events: none;
    background-image: radial-gradient(circle, #fff 10%, transparent 10.01%);
    background-repeat: no-repeat;
    background-position: 50%;
    transform: scale(10, 10);
    opacity: 0;
    transition: transform .5s, opacity 1s;
  }

  &:active::after {
    transform: scale(0, 0);
    opacity: .2;
    transition: 0s;
  }
} 