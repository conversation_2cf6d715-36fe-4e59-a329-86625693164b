/**
 * 🎯 择时标的输入组件
 * 智能的股票代码输入和验证
 */

import React, { useState, useCallback } from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';

const InputContainer = styled.div`
  position: relative;
`;

const InputField = styled.textarea`
  width: 100%;
  min-height: 100px;
  padding: 1rem;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  font-size: 1rem;
  font-family: "Inter", sans-serif;
  background: white;
  resize: vertical;
  transition: all 0.2s ease;
  
  &:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }
  
  &::placeholder {
    color: #9ca3af;
  }
`;

const InputHelper = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 0.5rem;
  font-size: 0.875rem;
`;

const SymbolCount = styled.div<{ $valid: boolean }>`
  color: ${props => props.$valid ? '#10b981' : '#6b7280'};
  font-weight: 500;
`;

const ValidationStatus = styled(motion.div)<{ $type: 'success' | 'warning' | 'error' }>`
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: ${props => {
    switch(props.$type) {
      case 'success': return '#10b981';
      case 'warning': return '#f59e0b';
      case 'error': return '#ef4444';
      default: return '#6b7280';
    }
  }};
  font-weight: 500;
`;

const ExampleHint = styled.div`
  margin-top: 0.75rem;
  padding: 0.75rem;
  background: rgba(59, 130, 246, 0.05);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 8px;
  font-size: 0.875rem;
  color: #374151;
`;

const ExampleTitle = styled.div`
  font-weight: 600;
  color: #3b82f6;
  margin-bottom: 0.25rem;
`;

interface TimingSymbolsInputProps {
  value: string;
  onChange: (value: string) => void;
}

const TimingSymbolsInput: React.FC<TimingSymbolsInputProps> = ({
  value,
  onChange,
}) => {
  const [focused, setFocused] = useState(false);

  // 解析和验证股票代码
  const parseSymbols = useCallback((input: string) => {
    if (!input.trim()) return [];
    
    // 支持多种分隔符：逗号、分号、空格、换行
    const symbols = input
      .split(/[,;\s\n]+/)
      .map(s => s.trim().toUpperCase())
      .filter(s => s.length > 0);
    
    return symbols;
  }, []);

  const validateSymbol = useCallback((symbol: string) => {
    // 基础A股代码验证：6位数字
    const pattern = /^[0-9]{6}$/;
    return pattern.test(symbol);
  }, []);

  const symbols = parseSymbols(value);
  const validSymbols = symbols.filter(validateSymbol);
  const invalidSymbols = symbols.filter(s => !validateSymbol(s));

  const getValidationStatus = () => {
    if (symbols.length === 0) return { type: 'warning' as const, message: '请输入股票代码' };
    if (invalidSymbols.length > 0) return { type: 'error' as const, message: `${invalidSymbols.length}个代码格式错误` };
    if (validSymbols.length > 20) return { type: 'warning' as const, message: '建议不超过20只股票' };
    return { type: 'success' as const, message: '格式正确' };
  };

  const validation = getValidationStatus();

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    onChange(e.target.value);
  };

  return (
    <InputContainer>
      <InputField
        value={value}
        onChange={handleChange}
        onFocus={() => setFocused(true)}
        onBlur={() => setFocused(false)}
        placeholder="输入股票代码，多个用逗号分隔，如：600519,000858,002415"
      />
      
      <InputHelper>
        <SymbolCount $valid={validSymbols.length > 0}>
          已输入 {validSymbols.length} 个有效标的
          {invalidSymbols.length > 0 && ` (${invalidSymbols.length}个格式错误)`}
        </SymbolCount>
        
        <ValidationStatus
          $type={validation.type}
          initial={{ opacity: 0, x: 10 }}
          animate={{ opacity: 1, x: 0 }}
          key={validation.message}
        >
          {validation.type === 'success' && '✓'}
          {validation.type === 'warning' && '⚠️'}
          {validation.type === 'error' && '❌'}
          {validation.message}
        </ValidationStatus>
      </InputHelper>

      {/* 显示示例和帮助信息 */}
      {(focused || symbols.length === 0) && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
        >
          <ExampleHint>
            <ExampleTitle>格式说明：</ExampleTitle>
            • 股票代码为6位数字（如：600519、000858）<br/>
            • 多个代码用逗号、空格或换行分隔<br/>
            • 支持沪深A股主板、中小板、创业板代码<br/>
            • 建议同时分析的股票数量不超过20只
          </ExampleHint>
        </motion.div>
      )}
    </InputContainer>
  );
};

export default TimingSymbolsInput; 