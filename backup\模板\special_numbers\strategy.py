"""
特殊数字策略
检测价格中的特殊数字模式(AAB/ABA/AAA等)的选股和择时策略
"""
import logging
import pandas as pd
import numpy as np
import re
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
import yaml
import json
from pathlib import Path

from app.core.strategy.base import UnifiedStrategyCard
from app.core.runtime.types import RuntimeContext, Signal, SignalType, StrategyMode
from app.services.daily_kline_service import DailyKlineService
from app.services.minute_kline_service import MinuteKlineService

logger = logging.getLogger(__name__)

# 从 template.json 加载配置
template_path = Path(__file__).parent / "template.json"
with open(template_path, "r", encoding="utf-8") as f:
    template = json.load(f)

class SpecialNumbers(UnifiedStrategyCard):
    """特殊数字策略：检测价格中的特殊数字模式"""
    
    def __init__(self, context: Optional[RuntimeContext] = None):
        """初始化策略"""
        super().__init__(context)
        
        # 加载配置文件
        config_path = Path(__file__).parent / "config.yaml"
        with open(config_path, "r", encoding="utf-8") as f:
            self.config = yaml.safe_load(f)
        
        # 根据不同模式初始化不同的服务
        self.daily_kline_service = DailyKlineService.get_instance()
        self.minute_kline_service = MinuteKlineService.get_instance()

    async def prepare_data(self, context: RuntimeContext) -> Dict[str, pd.DataFrame]:
        """准备数据"""
        try:
            # 检查串行执行模式
            if not self._check_sequential_mode(context):
                return {}
            
            if context.mode == StrategyMode.FILTER:
                # 选股模式：使用日K线数据
                return await self._prepare_filter_data(context)
            else:
                # 择时模式：使用分钟K线数据
                return await self._prepare_timing_data(context)
            
        except Exception as e:
            self._log(f"准备数据失败: {str(e)}", "error")
            return {}
    
    async def _prepare_filter_data(self, context: RuntimeContext) -> Dict[str, pd.DataFrame]:
        """准备选股数据"""
        try:
            # 从context.data_cache获取日K线数据
            kline_data = context.data_cache.get("daily_kline_data", {})
            if not kline_data:
                self._log("未能从上下文中获取日K线数据", "warning")
                return {}
            
            return self._process_kline_data(kline_data, context.parameters)
            
        except Exception as e:
            self._log(f"准备选股数据失败: {str(e)}", "error")
            return {}
    
    async def _prepare_timing_data(self, context: RuntimeContext) -> Dict[str, pd.DataFrame]:
        """准备择时数据"""
        try:
            # 从context.data_cache获取分钟K线数据
            kline_data = context.data_cache.get("kline_data", {})
            if not kline_data:
                self._log("未能从上下文中获取分钟K线数据", "warning")
                return {}
            
            return self._process_kline_data(kline_data, context.parameters)
            
        except Exception as e:
            self._log(f"准备择时数据失败: {str(e)}", "error")
            return {}
    
    def _process_kline_data(self, kline_data: Dict[str, pd.DataFrame], params: Dict[str, Any]) -> Dict[str, pd.DataFrame]:
        """处理K线数据"""
        try:
            processed_data = {}
            lookback_days = int(params.get("lookback_days", 20))
            
            for symbol, df in kline_data.items():
                if df.empty or len(df) < lookback_days:
                    continue
                
                # 确保数据按时间排序
                time_col = 'trade_date' if 'trade_date' in df.columns else 'time'
                df = df.sort_values(by=time_col).copy()
                
                # 检测特殊数字模式
                df = self._detect_special_number_patterns(df, params)
                
                # 计算模式强度和信号
                df = self._calculate_pattern_signals(df, params)
                
                # 过滤有效数据
                df = self._filter_valid_patterns(df, params)
                
                if not df.empty:
                    processed_data[symbol] = df
                    
                    # 打印调试信息
                    pattern_count = len(df[df['has_special_pattern'] == True])
                    if pattern_count > 0:
                        self._log(f"股票{symbol}发现{pattern_count}个特殊数字模式")
                        recent_patterns = df[df['has_special_pattern'] == True].tail(3)
                        for _, row in recent_patterns.iterrows():
                            time_str = row[time_col].strftime('%Y-%m-%d %H:%M:%S') if hasattr(row[time_col], 'strftime') else str(row[time_col])
                            self._log(f"  {time_str}: {row['pattern_type']}, 强度{row['pattern_strength']:.3f}")
            
            self._log(f"处理{len(processed_data)}只股票的特殊数字数据")
            return processed_data
            
        except Exception as e:
            self._log(f"处理K线数据失败: {str(e)}", "error")
            return {}
    
    def _detect_special_number_patterns(self, df: pd.DataFrame, params: Dict[str, Any]) -> pd.DataFrame:
        """检测特殊数字模式"""
        try:
            price_type = params.get("price_type", "both")
            pattern_types = params.get("pattern_types", ["AAB", "ABA", "AAA"])
            pattern_window = int(params.get("pattern_window", 3))
            
            df['has_special_pattern'] = False
            df['pattern_type'] = ""
            df['pattern_details'] = ""
            df['pattern_price'] = 0.0
            df['pattern_price_type'] = ""
            
            # 根据价格类型决定检测哪些价格
            price_columns = []
            if price_type in ["high", "both"]:
                price_columns.append("high")
            if price_type in ["low", "both"]:
                price_columns.append("low")
            
            for i in range(len(df)):
                for price_col in price_columns:
                    # 获取当前价格的字符串表示
                    current_price = df.iloc[i][price_col]
                    price_str = self._format_price_for_pattern(current_price)
                    
                    if len(price_str) >= 3:  # 至少3位数字才能构成模式
                        # 检测各种数字模式
                        detected_patterns = []
                        
                        for pattern_type in pattern_types:
                            if self._check_pattern_match(price_str, pattern_type):
                                detected_patterns.append({
                                    'type': pattern_type,
                                    'price': current_price,
                                    'price_type': price_col,
                                    'details': f"{pattern_type}_{price_str}"
                                })
                        
                        # 如果发现模式，记录第一个
                        if detected_patterns:
                            pattern = detected_patterns[0]  # 取第一个匹配的模式
                            df.iloc[i, df.columns.get_loc('has_special_pattern')] = True
                            df.iloc[i, df.columns.get_loc('pattern_type')] = pattern['type']
                            df.iloc[i, df.columns.get_loc('pattern_details')] = pattern['details']
                            df.iloc[i, df.columns.get_loc('pattern_price')] = pattern['price']
                            df.iloc[i, df.columns.get_loc('pattern_price_type')] = pattern['price_type']
                            break  # 找到第一个模式就跳出
            
            return df
            
        except Exception as e:
            self._log(f"检测特殊数字模式失败: {str(e)}", "error")
            return df
    
    def _format_price_for_pattern(self, price: float) -> str:
        """格式化价格用于模式检测"""
        try:
            # 将价格转换为字符串，去掉小数点
            price_str = f"{price:.2f}".replace(".", "")
            # 去掉前导零
            price_str = price_str.lstrip("0")
            # 如果全是0，返回"0"
            if not price_str:
                price_str = "0"
            return price_str
        except:
            return "0"
    
    def _check_pattern_match(self, price_str: str, pattern_type: str) -> bool:
        """检查价格字符串是否符合指定模式"""
        try:
            if len(price_str) < 3:
                return False
            
            if pattern_type == "AAB":
                # 前两位相同，第三位不同 (例如: 112, 223, 334)
                return len(price_str) >= 3 and price_str[0] == price_str[1] and price_str[1] != price_str[2]
            
            elif pattern_type == "ABA":
                # 第一位和第三位相同，第二位不同 (例如: 121, 232, 343)
                return len(price_str) >= 3 and price_str[0] == price_str[2] and price_str[0] != price_str[1]
            
            elif pattern_type == "AAA":
                # 前三位相同 (例如: 111, 222, 333)
                return len(price_str) >= 3 and price_str[0] == price_str[1] == price_str[2]
            
            elif pattern_type == "ABB":
                # 第一位不同，后两位相同 (例如: 122, 233, 344)
                return len(price_str) >= 3 and price_str[0] != price_str[1] and price_str[1] == price_str[2]
            
            elif pattern_type == "ABAB":
                # 四位数ABAB模式 (例如: 1212, 2323, 3434)
                return len(price_str) >= 4 and price_str[0] == price_str[2] and price_str[1] == price_str[3] and price_str[0] != price_str[1]
            
            # 可以扩展更多模式
            return False
            
        except Exception as e:
            return False
    
    def _calculate_pattern_signals(self, df: pd.DataFrame, params: Dict[str, Any]) -> pd.DataFrame:
        """计算模式信号强度"""
        try:
            df['pattern_strength'] = 0.0
            df['pattern_signal'] = 'HOLD'
            
            pattern_data = df[df['has_special_pattern'] == True]
            
            for idx in pattern_data.index:
                pattern_type = df.loc[idx, 'pattern_type']
                pattern_price = df.loc[idx, 'pattern_price']
                pattern_price_type = df.loc[idx, 'pattern_price_type']
                
                # 基础强度根据模式类型
                base_strength = self._get_pattern_base_strength(pattern_type)
                
                # 价格位置影响（高价位的特殊数字更有意义）
                price_factor = min(1.0, max(0.3, np.log10(pattern_price + 1) / 2))
                
                # 模式稀有度（AAA比AAB更稀有）
                rarity_factor = self._get_pattern_rarity_factor(pattern_type)
                
                # 价格类型影响（最高价的模式可能更重要）
                price_type_factor = 1.0 if pattern_price_type == 'high' else 0.8
                
                # 综合计算强度
                strength = base_strength * price_factor * rarity_factor * price_type_factor
                strength = max(0.3, min(0.9, strength))
                
                df.loc[idx, 'pattern_strength'] = strength
                
                # 生成信号（这里简化为买入信号，实际可以根据更复杂的逻辑）
                if pattern_price_type == 'high' and strength > 0.6:
                    df.loc[idx, 'pattern_signal'] = 'BUY'
                elif pattern_price_type == 'low' and strength > 0.6:
                    df.loc[idx, 'pattern_signal'] = 'SELL' 
                else:
                    df.loc[idx, 'pattern_signal'] = 'HOLD'
            
            return df
            
        except Exception as e:
            self._log(f"计算模式信号失败: {str(e)}", "error")
            return df
    
    def _get_pattern_base_strength(self, pattern_type: str) -> float:
        """获取模式基础强度"""
        strength_map = {
            'AAA': 0.8,    # 三连号最强
            'ABAB': 0.7,   # 四位重复次之
            'ABA': 0.6,    # 回文数字
            'AAB': 0.5,    # 双重开头
            'ABB': 0.5     # 双重结尾
        }
        return strength_map.get(pattern_type, 0.4)
    
    def _get_pattern_rarity_factor(self, pattern_type: str) -> float:
        """获取模式稀有度因子"""
        rarity_map = {
            'AAA': 1.0,    # 最稀有
            'ABAB': 0.9,   
            'ABA': 0.8,
            'AAB': 0.7,
            'ABB': 0.7
        }
        return rarity_map.get(pattern_type, 0.6)
    
    def _filter_valid_patterns(self, df: pd.DataFrame, params: Dict[str, Any]) -> pd.DataFrame:
        """过滤有效的模式"""
        try:
            min_pattern_strength = float(params.get("min_pattern_strength", 0.5))
            min_price_level = float(params.get("min_price_level", 1.0))
            
            # 过滤条件
            valid_condition = (
                (df['pattern_strength'] >= min_pattern_strength) &
                (df['pattern_price'] >= min_price_level)
            )
            
            # 保留有模式的行，或者保留所有行但标记无效模式
            df.loc[~valid_condition, 'has_special_pattern'] = False
            df.loc[~valid_condition, 'pattern_signal'] = 'HOLD'
            
            return df
            
        except Exception as e:
            self._log(f"过滤有效模式失败: {str(e)}", "error")
            return df
    
    async def generate_signal(self, data: Dict[str, pd.DataFrame], params: Dict[str, Any]) -> List[Signal]:
        """生成信号"""
        try:
            if not data:
                return []
            
            signals = []
            
            for symbol, df in data.items():
                if df.empty:
                    continue
                
                # 获取所有特殊模式信号
                pattern_signals = df[df['has_special_pattern'] == True]
                
                if not pattern_signals.empty:
                    # 处理最近的模式信号
                    for _, row in pattern_signals.tail(5).iterrows():  # 取最近5个模式
                        time_col = 'trade_date' if 'trade_date' in df.columns else 'time'
                        signal_time = row[time_col]
                        current_price = row['close']
                        pattern_type = row['pattern_type']
                        pattern_strength = row['pattern_strength']
                        pattern_details = row['pattern_details']
                        pattern_price = row['pattern_price']
                        pattern_price_type = row['pattern_price_type']
                        signal_direction = row['pattern_signal']
                        
                        # 构建触发条件描述
                        condition = f"特殊数字{pattern_type}_{pattern_price_type}{pattern_price:.2f}_{pattern_details}"
                        
                        # 创建信号
                        signals.append(
                            self.create_signal(
                                symbol=symbol,
                                name=symbol,
                                direction=signal_direction,
                                signal_type="technical",
                                confidence=pattern_strength,
                                trigger_condition=condition,
                                current_price=float(current_price),
                                signal_time=signal_time.strftime('%Y-%m-%d %H:%M:%S') if hasattr(signal_time, 'strftime') else str(signal_time),
                                pattern_type=pattern_type,
                                pattern_details=pattern_details,
                                pattern_price=float(pattern_price),
                                pattern_price_type=pattern_price_type,
                                pattern_strength=float(pattern_strength)
                            )
                        )
                else:
                    # 没有特殊模式，生成观望信号
                    latest = df.iloc[-1]
                    current_price = latest['close']
                    
                    signals.append(
                        self.create_signal(
                            symbol=symbol,
                            name=symbol,
                            direction="HOLD",
                            signal_type="technical",
                            confidence=0.3,
                            trigger_condition="无特殊数字模式",
                            current_price=float(current_price)
                        )
                    )
            
            self._log(f"特殊数字策略共生成{len(signals)}个信号")
            buy_signals = len([s for s in signals if s.direction == "BUY"])
            sell_signals = len([s for s in signals if s.direction == "SELL"])
            hold_signals = len([s for s in signals if s.direction == "HOLD"])
            self._log(f"买入{buy_signals}个，卖出{sell_signals}个，观望{hold_signals}个")
            
            return signals
            
        except Exception as e:
            self._log(f"生成信号失败: {str(e)}", "error")
            return []
    
    async def execute(self, context: RuntimeContext) -> List[Signal]:
        """执行策略"""
        try:
            # 添加执行模式日志
            mode_name = "选股模式" if context.mode == StrategyMode.FILTER else "择时模式"
            self._log(f"执行特殊数字策略，模式: {mode_name}")
            
            # 准备数据
            data = await self.prepare_data(context)
            
            # 生成信号
            signals = await self.generate_signal(data, context.parameters)
            
            # 记录结果
            pattern_signals = len([s for s in signals if "特殊数字" in s.trigger_condition])
            self._log(f"特殊数字策略执行完成，发现{pattern_signals}个特殊数字模式信号")
            
            return signals
            
        except Exception as e:
            self._log(f"策略执行失败: {str(e)}", "error")
            return []