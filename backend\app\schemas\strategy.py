"""
策略相关的 Pydantic 模型
"""
from typing import List, Dict, Any, Optional, Generic, TypeVar, Union
from datetime import datetime
from pydantic import BaseModel, Field
from ..core.runtime.types import ExecutionMode, StrategyMode

T = TypeVar('T')

class ListResponse(BaseModel, Generic[T]):
    """通用列表响应模型"""
    data: List[T]
    total: int

class DetailResponse(BaseModel, Generic[T]):
    """通用详情响应模型"""
    data: T

class Parameter(BaseModel):
    """策略参数模型"""
    name: str = Field(..., description="参数名称")
    type: str = Field(..., description="参数类型")
    default: Any = Field(None, description="默认值")
    description: str = Field(None, description="参数描述")
    options: Optional[List[Any]] = Field(None, description="可选值列表")

class StrategyCardBase(BaseModel):
    """策略卡片基础模型"""
    name: str = Field(..., description="策略名称")
    description: str = Field(None, description="策略描述")
    type: str = Field(..., description="策略类型")
    strategy_type: str = Field("filter", description="策略功能类型: filter-选股, timing-择时")
    parameters: List[Parameter] = Field(default_factory=list, description="策略参数列表")
    tags: List[str] = Field(default_factory=list, description="标签列表，用于分类和筛选")

class StrategyCardCreate(StrategyCardBase):
    """创建策略卡片的请求模型"""
    code: str = Field(..., description="策略代码")

class StrategyCardUpdate(StrategyCardBase):
    """更新策略卡片的请求模型"""
    code: Optional[str] = Field(None, description="策略代码")
    is_active: Optional[bool] = Field(None, description="是否启用")

class StrategyCardResponse(StrategyCardBase):
    """策略卡片响应模型"""
    id: str = Field(..., description="策略卡片ID")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    created_by: Optional[str] = Field(None, description="创建者ID")
    is_public: bool = Field(default=True, description="是否公开")
    version: str = Field(default="1.0.0", description="版本号")

    class Config:
        from_attributes = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class StrategyGroupBase(BaseModel):
    """策略组合基础模型"""
    name: str = Field(..., description="组合名称")
    description: Optional[str] = Field(None, description="组合描述")
    group_type: str = Field("filter", description="策略组类型: filter-选股, timing-择时")
    timing_symbols: Optional[str] = Field(None, description="择时标的列表，逗号分隔，仅当group_type为timing时有效")
    kline_period: Optional[str] = Field("5min", description="K线周期，用于统一择时策略组的数据周期")

class StrategyGroup(StrategyGroupBase):
    """策略组合模型"""
    id: str = Field(..., description="策略组ID")
    user_id: str = Field(..., description="用户ID")
    cards: List[Dict[str, Any]] = Field(default_factory=list, description="策略卡片列表")
    execution_config: Dict[str, Any] = Field(default_factory=dict, description="执行配置")
    status: str = Field("stopped", description="运行状态")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

    class Config:
        from_attributes = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class StrategyGroupCreate(StrategyGroupBase):
    """创建策略组合的请求模型"""
    cards: List[Dict[str, Any]] = Field(default_factory=list, description="策略卡片列表")
    execution_config: Dict[str, Any] = Field(default_factory=dict, description="执行配置，包含mode(执行方式)和schedule等")

    class Config:
        schema_extra = {
            "example": {
                "name": "示例策略组",
                "description": "这是一个示例策略组",
                "group_type": "filter",
                "cards": [
                    {
                        "type": "market_filter_v1",
                        "parameters": {
                            "indicator": "市净率",
                            "operator": "小于",
                            "value1": 1,
                            "value2": 0
                        }
                    }
                ],
                "execution_config": {
                    "mode": ExecutionMode.SEQUENTIAL.value,
                    "schedule": "0 0 * * *"
                }
            }
        }

class StrategyGroupUpdate(StrategyGroupBase):
    """更新策略组合的请求模型"""
    cards: Optional[List[Dict[str, Any]]] = Field(None, description="策略卡片列表")
    execution_config: Optional[Dict[str, Any]] = Field(None, description="执行配置")
    status: Optional[str] = Field(None, description="运行状态")

class StrategyGroupResponse(StrategyGroup):
    """策略组合响应模型"""
    class Config:
        from_attributes = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class SignalBase(BaseModel):
    """策略信号基础模型"""
    type: str = Field(..., description="信号类型")
    stock_list: List[str] = Field(default_factory=list, description="股票代码列表")
    metrics: Dict[str, float] = Field(default_factory=dict, description="指标数据")
    confidence: float = Field(..., description="信号置信度")

class SignalCreate(SignalBase):
    """创建策略信号的请求模型"""
    strategy_group_id: str = Field(..., description="策略组合ID")

class SignalInDB(SignalBase):
    """数据库中的策略信号模型"""
    id: str = Field(..., description="信号ID")
    strategy_group_id: str = Field(..., description="策略组合ID")
    timestamp: datetime = Field(..., description="信号生成时间")
    status: str = Field(..., description="信号状态")
    execution_time: float = Field(..., description="执行耗时")

class StrategyCreate(BaseModel):
    """策略创建模型"""
    name: str = Field(..., description="策略名称")
    type: str = Field(..., description="策略类型")
    description: str = Field(..., description="策略描述")
    parameters: Dict[str, Any] = Field(..., description="策略参数")

class StrategyResponse(BaseModel):
    """策略响应模型"""
    id: str = Field(..., description="策略ID")
    user_id: str = Field(..., description="用户ID")
    name: str = Field(..., description="策略名称")
    type: str = Field(..., description="策略类型")
    description: str = Field(..., description="策略描述")
    parameters: Dict[str, Any] = Field(..., description="策略参数")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

    class Config:
        from_attributes = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class StrategyExecutionRequest(BaseModel):
    """策略执行请求模型"""
    id: str = Field(..., description="策略模板ID(MongoDB _id)")
    parameters: Optional[Dict[str, Any]] = Field(None, description="策略参数")
    strategy_mode: Optional[str] = Field(StrategyMode.FILTER.value, description="策略模式，可选值：filter, timing, backtest, analysis")
    execution_mode: Optional[str] = Field(ExecutionMode.SEQUENTIAL.value, description="执行方式，可选值：sequential, parallel")
    previous_signals: Optional[List[Dict[str, Any]]] = Field(None, description="前一个策略的信号")
    next_cards: Optional[List[Dict[str, Any]]] = Field(None, description="下一个策略卡片")
    real_time_data: Optional[bool] = Field(False, description="是否使用实时数据（用于择时策略）")
    data_lookback_days: Optional[int] = Field(5, description="数据回溯天数（用于择时策略）")
    group_type: Optional[str] = Field("filter", description="策略组类型，可选值：filter, timing")
    timing_symbols: Optional[str] = Field(None, description="择时标的列表，逗号分隔，仅当group_type为timing时有效")

    class Config:
        from_attributes = True

class StrategyCard(BaseModel):
    """策略卡片模型"""
    template_id: str = Field(..., description="策略模板ID")
    parameters: Dict[str, Any] = Field(default_factory=dict, description="策略参数")
    name: Optional[str] = Field(None, description="策略卡片名称")

class DebugRequest(BaseModel):
    """调试请求模型"""
    cards: List[StrategyCard] = Field(..., description="策略卡片列表")
    execution_mode: Optional[str] = Field("sequential", description="执行方式，可选值：sequential, parallel")
    group_type: Optional[str] = Field("filter", description="策略组类型，可选值：filter, timing")
    timing_symbols: Optional[str] = Field(None, description="择时标的列表，逗号分隔")
    kline_period: Optional[str] = Field(None, description="K线周期")

    class Config:
        from_attributes = True
        
        schema_extra = {
            "example": {
                "id": "67eea268fb8304074ef6180c",
                "parameters": {
                    "short_period": 5,
                    "long_period": 20,
                    "kline_period": "5min",
                    "lookback_days": 3,
                    "signal_threshold": 0.02,
                    "stock_pool": ["600519", "000858", "300750"]
                },
                "strategy_mode": "timing",
                "execution_mode": "sequential",
                "real_time_data": True,
                "data_lookback_days": 5
            }
        }