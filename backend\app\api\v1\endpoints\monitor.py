"""
系统监控API

提供系统性能和运行状态监控功能
"""
from typing import Optional
from datetime import datetime
from fastapi import APIRouter, Depends, Query

from ....models.user import User
from ....core.deps import get_current_user
from ....services.monitor_service import MonitorService

router = APIRouter()

@router.get("/system-metrics")
async def get_system_metrics(current_user: dict = Depends(get_current_user)):
    """获取系统指标"""
    return await MonitorService.get_system_metrics()

@router.get("/performance-metrics")
async def get_performance_metrics(
    start_time: datetime = Query(..., description="开始时间"),
    end_time: datetime = Query(..., description="结束时间"),
    current_user: dict = Depends(get_current_user)
):
    """获取性能指标"""
    return await MonitorService.get_performance_metrics(start_time, end_time)

@router.post("/errors")
async def track_error(
    error_data: dict,
    current_user: dict = Depends(get_current_user)
):
    """记录错误日志"""
    return await MonitorService.track_error(error_data)

@router.get("/errors")
async def get_error_logs(
    start_time: Optional[datetime] = Query(None, description="开始时间"),
    end_time: Optional[datetime] = Query(None, description="结束时间"),
    error_type: Optional[str] = Query(None, description="错误类型"),
    limit: int = Query(100, description="返回记录数量限制"),
    current_user: dict = Depends(get_current_user)
):
    """获取错误日志"""
    return await MonitorService.get_error_logs(
        start_time=start_time,
        end_time=end_time,
        error_type=error_type,
        limit=limit
    )

@router.post("/user-activities")
async def track_user_activity(
    activity_data: dict,
    current_user: dict = Depends(get_current_user)
):
    """记录用户行为"""
    activity_data['user_id'] = str(current_user["id"])
    return await MonitorService.track_user_activity(activity_data)

@router.get("/user-activities")
async def get_user_activities(
    user_id: Optional[str] = Query(None, description="用户ID"),
    action: Optional[str] = Query(None, description="操作类型"),
    start_time: Optional[datetime] = Query(None, description="开始时间"),
    end_time: Optional[datetime] = Query(None, description="结束时间"),
    limit: int = Query(100, description="返回记录数量限制"),
    current_user: dict = Depends(get_current_user)
):
    """获取用户行为记录"""
    return await MonitorService.get_user_activities(
        user_id=user_id,
        action=action,
        start_time=start_time,
        end_time=end_time,
        limit=limit
    ) 