import { request, withRetry } from './api';
import {
  StrategyCardTemplate,
  StrategyCardInstance,
  StrategyGroup,
  StrategySearchParams,
  PaginatedResponse,
  WrappedPaginatedResponse,
  WrappedResponse,
} from '../types/api';

// 策略卡模板相关API
export const strategyTemplateApi = {
  // 获取策略卡模板列表
  getTemplates: withRetry(async (params: StrategySearchParams) => {
    const response = await request<PaginatedResponse<StrategyCardTemplate>>({
      method: 'GET',
      url: '/strategies/templates',
      params: {
        page: params.page || 1,
        pageSize: params.pageSize,
        id: params.id,
        tags: params.tags,
        keyword: params.keyword
      }
    });
    
    console.log('策略模板API响应:', response);
    
    if (!response.items) {
      throw new Error('无效的响应格式');
    }
    
    // 确保每个模板都有正确的stars字段
    const processedItems = response.items.map(item => ({
      ...item,
      stars: typeof item.stars === 'number' ? item.stars : 0
    }));
    
    return {
      ...response,
      items: processedItems
    };
  }),

  // 获取单个策略卡模板详情
  getTemplateById: withRetry(async (id: string) => {
    const response = await request<{ data: StrategyCardTemplate }>({
      method: 'GET',
      url: `/strategies/templates/${id}`,
    });
    return response.data;
  }),
};

// 策略组响应数据类型
interface StrategyDetailResponse {
  group: StrategyGroup;
  templates: StrategyCardTemplate[];
}

// 策略组相关API
export const strategyGroupApi = {
  // 获取策略组列表
  getGroups: withRetry(async (params: StrategySearchParams) => {
    const response = await request<WrappedPaginatedResponse<StrategyGroup>>({
      method: 'GET',
      url: '/strategies/groups',
      params,
    });
    return response;
  }),

  // 获取单个策略组详情
  getGroup: withRetry(async (id: string) => {
    return request<WrappedResponse<StrategyDetailResponse>>({
      method: 'GET',
      url: `/strategies/groups/${id}`,
    });
  }),

  // 获取策略组执行历史
  getGroupExecutionHistory: withRetry(async (id: string) => {
    return request<WrappedResponse<any[]>>({
      method: 'GET',
      url: `/strategies/groups/${id}/execution-history`,
    });
  }),

  // 创建策略组
  createGroup: withRetry(async (data: Partial<StrategyGroup>) => {
    // 确保发送到后端的策略卡只包含template_id引用
    if (data.cards && Array.isArray(data.cards)) {
      data.cards = data.cards.map(card => {
        if (card.template) {
          // 提取需要的信息
          return {
            id: card.id,
            name: card.name || card.template.name,
            description: card.description || card.template.description,
            parameters: card.parameters || {},
            template_id: card.template.id,
            template_name: card.template.name,
            position: card.position
          };
        }
        return card;
      });
    }
    
    return request<StrategyGroup>({
      method: 'POST',
      url: '/strategies/groups',
      data,
    });
  }),

  // 更新策略组
  updateGroup: withRetry(async (id: string, data: Partial<StrategyGroup>) => {
    return request<WrappedResponse<StrategyGroup>>({
      method: 'PUT',
      url: `/strategies/groups/${id}`,
      data,
    });
  }),

  // 删除策略组
  deleteGroup: withRetry(async (id: string) => {
    return request<void>({
      method: 'DELETE',
      url: `/strategies/groups/${id}`,
    });
  }),

  // 启动策略组
  startGroup: withRetry(async (id: string) => {
    return request<any>({
      method: 'POST',
      url: `/strategies/groups/${id}/start`,
    });
  }),

  // 停止策略组
  stopGroup: withRetry(async (id: string) => {
    return request<any>({
      method: 'POST',
      url: `/strategies/groups/${id}/stop`,
    });
  }),

  // 执行策略组
  executeGroup: withRetry(async (id: string, mode: string = "sequential") => {
    return request<any>({
      method: 'POST',
      url: `/strategies/groups/${id}/execute`,
      params: { mode }
    });
  }),

  // 获取策略组性能数据
  getGroupPerformance: withRetry(async (id: string, timeRange: string) => {
    return request<StrategyGroup['performance_metrics']>({
      method: 'GET',
      url: `/strategies/groups/${id}/performance`,
      params: { timeRange },
    });
  }),

  // 验证策略组配置
  validateGroup: withRetry(async (data: Partial<StrategyGroup>) => {
    return request<{ valid: boolean; errors?: Record<string, string[]> }>({
      method: 'POST',
      url: '/strategies/groups/validate',
      data,
    });
  }),

  // 导出策略组合配置
  exportGroup: withRetry(async (id: string) => {
    return request<Blob>({
      method: 'GET',
      url: `/strategies/groups/${id}/export`,
      responseType: 'blob',
    });
  }),

  // 导入策略组合配置
  importGroup: withRetry(async (file: File) => {
    const formData = new FormData();
    formData.append('file', file);
    return request<StrategyGroup>({
      method: 'POST',
      url: '/strategies/groups/import',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  }),
}; 

// 策略调试参数接口
interface DebugStrategyParams {
  id: string;
  parameters: Record<string, any>;
  execution_mode?: string;
  group_type?: 'filter' | 'timing'; // 策略组类型
  timing_symbols?: string; // 择时标的，逗号分隔
  kline_period?: string; // K线周期，仅用于择时模式
  previous_signals?: any[];
  next_cards?: {
    id: string;
    parameters: Record<string, any>;
  }[];
}

// 调试策略
export const debugStrategy = async (params: DebugStrategyParams) => {
  try {
    console.log('调试请求参数:', params);
    
    // 发送请求
    const response = await request({
      url: '/strategies/debug',
      method: 'post',
      data: params
    });
    
    // 处理后端返回的结果
    if (response && response.success) {
      // 确保logs字段始终是字符串数组且位于顶层
      if (!Array.isArray(response.logs) || response.logs.length === 0) {
        // 如果顶层logs为空，尝试使用data.logs
        if (response.data && Array.isArray(response.data.logs) && response.data.logs.length > 0) {
          response.logs = response.data.logs;
          // 避免数据重复
          delete response.data.logs;
        } else {
          // 如果都没有日志，设置一个默认日志
          response.logs = ["策略执行完成，未返回详细日志"];
        }
      }
      
      // 确保data中包含strategy_type字段，与请求中的group_type一致
      if (response.data && !response.data.strategy_type) {
        response.data.strategy_type = params.group_type || 'filter';
      }
      
      // 后端已经整合了行情数据，无需再次请求
      console.log('策略执行成功，信号数量:', response.data?.signals?.length || 0);
      console.log('策略执行日志数量:', response.logs?.length || 0);
    }
  
    return response;
  } catch (error) {
    console.error('调试策略失败:', error);
    throw error;
  }
};

// 标签相关接口
export interface TagConfig {
  name: string;
  color: string;
  updated_at?: string;
}

// 标签相关服务
export const tagService = {
  // 缓存变量
  _tagCache: null as TagConfig[] | null,
  _cacheTimestamp: 0,
  _cacheExpiration: 5 * 60 * 1000, // 缓存有效期：5分钟

  getTags: async (): Promise<TagConfig[]> => {
    try {
      // 检查缓存是否有效
      const now = Date.now();
      if (
        tagService._tagCache && 
        now - tagService._cacheTimestamp < tagService._cacheExpiration
      ) {
        return tagService._tagCache;
      }

      const response = await request({
        method: 'GET',
        url: '/strategies/tags'
      });
      console.log('Tag response:', response);
      
      // 更新缓存
      tagService._tagCache = response.tags || [];
      tagService._cacheTimestamp = now;
      
      return response.tags || [];  // 直接使用tags字段
    } catch (error) {
      console.error('Failed to fetch tags:', error);
      // 如果请求失败但有缓存，返回缓存的数据
      if (tagService._tagCache) {
        return tagService._tagCache;
      }
      return [];
    }
  },

  updateTag: async (
    tagName: string,
    color: string
  ): Promise<void> => {
    try {
      const response = await request({
        method: 'POST',
        url: `/strategies/tags/${tagName}`,
        data: {
          color
        }
      });
      // 更新标签后清除缓存，强制下次重新获取
      tagService._tagCache = null;
      tagService._cacheTimestamp = 0;
      return response.tag;  // 返回更新后的标签数据
    } catch (error) {
      console.error('Failed to update tag:', error);
      throw error;
    }
  }
};