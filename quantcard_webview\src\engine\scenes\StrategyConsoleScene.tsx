/**
 * 📊 策略控制台场景 - 组件化架构
 * 集成策略管理、监控和实验室功能
 */

import React, { useState } from 'react'
import styled from 'styled-components'
import { motion } from 'framer-motion'
import UnifiedMobileNav from '../components/ui/Nav/UnifiedMobileNav'
import { MyStrategiesPage, MonitoringRoomPage, LabPage } from '../components/StrategyConsole'

// 页面类型定义
type ConsolePage = 'strategies' | 'monitoring' | 'lab'

// 样式组件
const Container = styled.div`
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #ffffff;
`

const TabBar = styled.div`
  display: flex;
  justify-content: center;
  margin: 1rem;
  background: #f5f5f5;
  border-radius: 12px;
  padding: 4px;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
`

const TabButton = styled(motion.button)<{ $active: boolean }>`
  flex: 1;
  padding: 0.75rem 1rem;
  border: none;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  background: ${props => props.$active ? '#667eea' : 'transparent'};
  color: ${props => props.$active ? 'white' : '#666'};

  &:hover {
    background: ${props => props.$active ? '#5a67d8' : 'rgba(102, 126, 234, 0.1)'};
  }
`

const ContentArea = styled.div`
  flex: 1;
  padding: 0 1rem 1rem;
  overflow-y: auto;
`

// 策略控制台主组件
function StrategyConsoleScene() {
  const [currentPage, setCurrentPage] = useState<ConsolePage>('strategies')

  // 页面配置
  const pages = [
    { key: 'strategies' as ConsolePage, label: '我的策略', icon: '📊' },
    { key: 'monitoring' as ConsolePage, label: '监控室', icon: '📡' },
    { key: 'lab' as ConsolePage, label: '实验室', icon: '🧪' }
  ]

  console.log('StrategyConsoleScene rendering, currentPage:', currentPage)

  return (
    <UnifiedMobileNav title="策略控制台">
      <Container>
        {/* 标签导航 */}
        <TabBar>
          {pages.map((page) => (
            <TabButton
              key={page.key}
              $active={currentPage === page.key}
              onClick={() => {
                console.log('Switching to page:', page.key)
                setCurrentPage(page.key)
              }}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <span style={{ marginRight: '0.5rem' }}>{page.icon}</span>
              {page.label}
            </TabButton>
          ))}
        </TabBar>

        {/* 内容区域 */}
        <ContentArea>
          {currentPage === 'strategies' && <MyStrategiesPage />}
          {currentPage === 'monitoring' && <MonitoringRoomPage />}
          {currentPage === 'lab' && <LabPage />}
        </ContentArea>
      </Container>
    </UnifiedMobileNav>
  )
}

export default StrategyConsoleScene