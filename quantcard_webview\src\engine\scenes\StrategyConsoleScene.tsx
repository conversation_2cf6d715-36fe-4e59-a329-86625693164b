/**
 * Strategy Console Scene - Component-based Architecture
 * Integrated strategy management, monitoring and lab features
 */

import React, { useState } from 'react'
import styled from 'styled-components'
import { motion } from 'framer-motion'
import UnifiedMobileNav from '../components/ui/Nav/UnifiedMobileNav'
import { MyStrategiesPage, MonitoringRoomPage, LabPage } from '../components/StrategyConsole'

// Page type definition
type ConsolePage = 'strategies' | 'monitoring' | 'lab'

// Styled components
const Container = styled.div`
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #ffffff;
`

const TabBar = styled.div`
  display: flex;
  justify-content: center;
  margin: 1rem;
  background: #f5f5f5;
  border-radius: 12px;
  padding: 4px;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
`

const TabButton = styled(motion.button)<{ $active: boolean }>`
  flex: 1;
  padding: 0.75rem 1rem;
  border: none;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  background: ${props => props.$active ? '#667eea' : 'transparent'};
  color: ${props => props.$active ? 'white' : '#666'};

  &:hover {
    background: ${props => props.$active ? '#5a67d8' : 'rgba(102, 126, 234, 0.1)'};
  }
`

const ContentArea = styled.div`
  flex: 1;
  padding: 0 1rem 1rem;
  overflow-y: auto;
`

// Strategy Console Main Component
function StrategyConsoleScene() {
  const [currentPage, setCurrentPage] = useState<ConsolePage>('strategies')

  // Page configuration
  const pages = [
    { key: 'strategies' as ConsolePage, label: 'My Strategies', icon: '📊' },
    { key: 'monitoring' as ConsolePage, label: 'Monitoring', icon: '📡' },
    { key: 'lab' as ConsolePage, label: 'Lab', icon: '🧪' }
  ]

  console.log('StrategyConsoleScene rendering, currentPage:', currentPage)

  return (
    <UnifiedMobileNav title="Strategy Console">
      <Container>
        {/* Tab Navigation */}
        <TabBar>
          {pages.map((page) => (
            <TabButton
              key={page.key}
              $active={currentPage === page.key}
              onClick={() => {
                console.log('Switching to page:', page.key)
                setCurrentPage(page.key)
              }}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <span style={{ marginRight: '0.5rem' }}>{page.icon}</span>
              {page.label}
            </TabButton>
          ))}
        </TabBar>

        {/* Content Area */}
        <ContentArea>
          {currentPage === 'strategies' && <MyStrategiesPage />}
          {currentPage === 'monitoring' && <MonitoringRoomPage />}
          {currentPage === 'lab' && <LabPage />}
        </ContentArea>
      </Container>
    </UnifiedMobileNav>
  )
}

export default StrategyConsoleScene