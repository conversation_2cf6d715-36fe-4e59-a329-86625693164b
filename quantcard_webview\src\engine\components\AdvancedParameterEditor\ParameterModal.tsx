import React from 'react'
import styled from 'styled-components'
import { motion, AnimatePresence } from 'framer-motion'
import { InlineParameterEditor, type StrategyParameter } from './index'
import type { ParameterGroup } from './types'

const ParameterModal = styled(motion.div)`
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.6);
	backdrop-filter: blur(8px);
	z-index: 1000;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 2rem;
`

const ParameterContent = styled(motion.div)`
	background: rgba(255, 255, 255, 0.98);
	backdrop-filter: blur(20px);
	border-radius: 20px;
	padding: 1.25rem;
	max-width: 720px;
	width: 100%;
	max-height: 80vh;
	overflow-y: auto;
	box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
	`

const Footer = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
  margin-top: 1rem;
`

const Button = styled.button<{ $primary?: boolean }>`
  padding: 0.5rem 0.9rem;
  border-radius: 8px;
  border: 2px solid ${p => p.$primary ? '#3b82f6' : '#94a3b8'};
  background: ${p => p.$primary ? '#3b82f6' : 'transparent'};
  color: ${p => p.$primary ? '#fff' : '#334155'};
  font-weight: 600;
  cursor: pointer;
  &:hover { opacity: 0.9 }
`



interface ParameterModalProps {
	open: boolean
	parameters: Record<string, StrategyParameter>
	parameterGroups?: Record<string, ParameterGroup>
	values: Record<string, any>
	onChange: (values: Record<string, any>) => void
	onClose: () => void
	onConfirm?: (values: Record<string, any>) => void
}

const AdvancedParameterModal: React.FC<ParameterModalProps> = ({ open, parameters, parameterGroups, values, onChange, onClose, onConfirm }) => {
	// 如果没有提供参数定义，根据当前值动态生成简单的参数定义
	const autoParameters: Record<string, StrategyParameter> = React.useMemo(() => {
		if (parameters && Object.keys(parameters).length > 0) return parameters
		const result: Record<string, StrategyParameter> = {}
		Object.keys(values || {}).forEach(key => {
			const val = values[key]
			let type: StrategyParameter['type'] = 'text'
			if (typeof val === 'number') type = 'number'
			else if (typeof val === 'boolean') type = 'boolean'
			result[key] = { type, label: key, default: val }
		})
		return result
	}, [parameters, values])
	return (
		<AnimatePresence>
			{open && (

				<ParameterModal
					initial={{ opacity: 0 }}
					animate={{ opacity: 1 }}
					exit={{ opacity: 0 }}
					onClick={onClose}
				>
					<ParameterContent
						initial={{ scale: 0.95, opacity: 0 }}
						animate={{ scale: 1, opacity: 1 }}
						exit={{ scale: 0.95, opacity: 0 }}
						onClick={(e) => e.stopPropagation()}
					>
						<InlineParameterEditor
							parameters={autoParameters}
							parameterGroups={parameterGroups}
							values={values}
							onChange={onChange}
							layout="vertical"
							compact={false}
						/>
						<Footer>
							<Button onClick={onClose}>取消</Button>
							<Button $primary onClick={() => onConfirm?.(values)}>保存</Button>
						</Footer>
					</ParameterContent>
				</ParameterModal>
			)}
		</AnimatePresence>
	)
}

export default AdvancedParameterModal