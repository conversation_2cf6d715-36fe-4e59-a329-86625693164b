基于全面扫描，我整理了后端所有与策略卡、策略组、库存相关的服务和接口：

🎯 1. 策略模板相关 (Strategy Templates)
📁 模型文件
backend/app/models/strategy_template.py - 策略模板主模型
backend/app/models/strategy.py - 策略卡片模型（包含StrategyCard类）
backend/app/models/strategy_card.py - ⚠️ 冗余 - 与strategy.py中的StrategyCard重复
📁 API端点
backend/app/api/v1/endpoints/card_system.py - 卡牌系统API
GET /cards/codex - 获取卡牌图鉴
POST /cards/draw - 抽卡功能
GET /cards/packs - 获取卡包信息
GET /cards/templates/{template_id} - 获取卡牌详情
📁 数据初始化
backend/app/core/init_data.py - 策略模板数据初始化
backend/app/strategies/templates/ - 策略模板目录
各个策略的template.json配置文件
各个策略的strategy.py实现文件
🎯 2. 策略组相关 (Strategy Groups)
📁 模型文件
backend/app/models/strategy_group.py - 策略组主模型
backend/app/models/strategy.py - ⚠️ 冗余 - 包含另一个StrategyGroup类定义
📁 服务文件
backend/app/services/strategy_service.py - 策略组核心服务
list_strategy_groups() - 获取策略组列表
get_strategy_group() - 获取策略组详情
create_strategy_group() - 创建策略组
execute_strategy_group() - 执行策略组
start_strategy_group() - 启动持续执行
stop_strategy_group() - 停止执行
📁 API端点
backend/app/api/v1/endpoints/strategies.py - 策略组API
GET /strategies/groups - 获取策略组列表
POST /strategies/groups - 创建策略组
GET /strategies/groups/{group_id} - 获取策略组详情
POST /strategies/groups/{group_id}/execute - 执行策略组
POST /strategies/groups/{group_id}/start - 启动持续执行
POST /strategies/groups/{group_id}/stop - 停止执行
📁 Schema文件
backend/app/schemas/strategy.py - 策略相关的请求/响应模型
🎯 3. 库存管理相关 (Inventory)
📁 模型文件
backend/app/models/inventory.py - 库存管理核心模型
InventoryItem - 库存物品模型
InventoryTransaction - 库存交易日志
GameSession - 游戏会话模型
InventoryManager - 库存管理器
📁 服务文件
backend/app/services/inventory_service.py - 库存服务
get_user_inventory() - 获取用户库存
add_cards_to_inventory() - 添加卡牌到库存
consume_cards_from_inventory() - 消耗库存卡牌
create_game_session() - 创建游戏会话
📁 API端点
backend/app/api/v1/endpoints/inventory.py - 库存管理API
GET /inventory/inventory - 获取用户库存
POST /inventory/add - 添加卡牌
POST /inventory/consume - 消耗卡牌
POST /inventory/batch-update - 批量更新
🎯 4. WebSocket通信
backend/app/api/unified_websocket.py - 统一WebSocket处理器
支持库存操作的WebSocket消息处理
实时库存更新推送
🎯 5. 路由配置
backend/app/api/__init__.py - 主API路由器
backend/app/api/v1/__init__.py - v1版本API路由器
⚠️ 发现的冗余和重复问题
🔴 严重冗余
策略卡片模型重复：
backend/app/models/strategy.py 中的 StrategyCard 类
backend/app/models/strategy_card.py 中的 StrategyCard 类
建议：删除 strategy_card.py，统一使用 strategy.py 中的定义
策略组模型重复：
backend/app/models/strategy.py 中的 StrategyGroup 类
backend/app/models/strategy_group.py 中的 StrategyGroup 类
建议：删除 strategy.py 中的 StrategyGroup，统一使用 strategy_group.py
🟡 轻微冗余
交易记录模型重复：
backend/app/models/backtest.py 中的 TradeRecord 类
backend/app/schemas/backtest.py 中的 TradeRecord 类
建议：保留模型中的定义，Schema中引用模型
API路由重复注册：
卡牌和库存API在 api/__init__.py 中重复注册了两次路径
建议：保持当前设计（支持带版本号和不带版本号的路径）