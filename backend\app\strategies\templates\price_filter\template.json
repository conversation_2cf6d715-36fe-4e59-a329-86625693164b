{"template_id": "price_filter", "name": "股价", "description": "根据设定的价格条件筛选股票，支持区间范围筛选。例：价格介于10元和20元。", "version": "2.0.0", "author": "quantcard", "stars": 1, "tags": ["选股", "择时", "基础筛选"], "parameters": {"operator": {"type": "select", "label": "条件", "description": "价格比较条件", "default": "大于", "options": [{"label": "大于", "value": "大于"}, {"label": "小于", "value": "小于"}, {"label": "区间", "value": "区间"}]}, "priceMin": {"type": "number", "label": "下限", "description": "价格下限（元）", "default": 10, "unit": "元", "validation": {"required": true, "min": 0, "max": 10000}}, "priceMax": {"type": "number", "label": "上限", "description": "价格上限（元），仅在'区间'模式下使用", "default": null, "unit": "元", "validation": {"min": 0, "max": 10000}, "visibleWhen": {"param": "operator", "in": ["区间"]}}, "signal_type": {"type": "select", "label": "信号", "description": "择时策略产生的信号类型", "default": "BUY", "options": [{"label": "买入", "value": "BUY"}, {"label": "卖出", "value": "SELL"}, {"label": "观望", "value": "HOLD"}], "visibleWhen": {"param": "@strategy_mode", "eq": "timing"}}}, "parameterGroups": {"price_filter": {"parameters": ["operator", "priceMin", "priceMax"], "displayMode": "inline", "prefix": "价格", "separator": "-", "layout": "horizontal"}, "signal_settings": {"parameters": ["signal_type"], "displayMode": "inline", "prefix": "信号", "separator": "", "layout": "horizontal", "visibleWhen": {"parameter": "@strategy_mode", "value": "timing"}}}, "ui": {"icon": "filter", "color": "#1890ff", "category": "筛选", "order": 1, "form": {"layout": "horizontal", "compact": true, "showDescription": false}}}