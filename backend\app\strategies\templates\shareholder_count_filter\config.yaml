data_sources:
  filter:
    cache:
      enabled: true
      ttl: 120
    database:
      type: postgresql
      table: stock_shareholder_count_data
    fields:
      - 代码
      - 名称
      - 最新价
      - 涨跌幅
      - 股东户数本次
      - 股东户数上次
      - 股东户数增减
      - 股东户数增减比例
      - 区间涨跌幅
      - 股东户数统计截止日本次
      - 股东户数统计截止日上次
      - 户均持股市值
      - 户均持股数量
      - 总市值
      - 总股本
      - 公告日期
      - 数据期数
      - 更新时间
  backtest:
    cache:
      enabled: true
      ttl: 120
    database:
      type: postgresql
      table: stock_shareholder_count_data
    fields:
      - 代码
      - 名称
      - 最新价
      - 涨跌幅
      - 股东户数本次
      - 股东户数上次
      - 股东户数增减
      - 股东户数增减比例
  timing:
    cache:
      enabled: true
      ttl: 60
    database:
      type: postgresql
      table: stock_shareholder_count_data
    fields:
      - 代码
      - 名称
      - 最新价
      - 涨跌幅
      - 股东户数本次
      - 股东户数上次
      - 股东户数增减
      - 股东户数增减比例
  monitor:
    cache:
      enabled: true
      ttl: 60
    database:
      type: postgresql
      table: stock_shareholder_count_data
    fields:
      - 代码
      - 名称
      - 最新价
      - 涨跌幅
      - 股东户数本次
      - 股东户数上次
      - 股东户数增减
      - 股东户数增减比例
execution:
  max_symbols: 3000
  timeout: 60
logging:
  format: '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
  level: info 