// 策略参数类型
export interface StrategyParams {
  name: string;
  description: string;
  code: string;
  parameters: {
    [key: string]: any;
  };
  timeframe: string;
  symbols: string[];
  riskManagement: {
    stopLoss?: number;
    takeProfit?: number;
    maxDrawdown?: number;
    positionSize?: number;
  };
}

// 策略类型
export interface Strategy extends StrategyParams {
  id: string;
  userId: string;
  createdAt: string;
  updatedAt: string;
  status: 'active' | 'inactive' | 'testing';
  performance?: {
    totalReturns: number;
    sharpeRatio: number;
    maxDrawdown: number;
    winRate: number;
  };
}

// 策略信号响应类型
export interface StrategySignalResponse {
  success: boolean;
  data: {
    stocks?: Array<{
      代码?: string;
      名称?: string;
      最新价?: number;
      code?: string;
      name?: string;
      price?: number;
      [key: string]: any;
    }>;
    [key: string]: any;
  };
  message?: string;
}

// 回测结果类型
export interface BacktestResult {
  id: string;
  strategyId: string;
  startDate: string;
  endDate: string;
  initialBalance: number;
  finalBalance: number;
  totalReturns: number;
  trades: Trade[];
  metrics: {
    sharpeRatio: number;
    maxDrawdown: number;
    winRate: number;
    profitFactor: number;
    averageWin: number;
    averageLoss: number;
    totalTrades: number;
    profitableTrades: number;
  };
  equity: {
    timestamp: string;
    value: number;
  }[];
}

// 交易记录类型
export interface Trade {
  id: string;
  timestamp: string;
  symbol: string;
  type: 'buy' | 'sell';
  price: number;
  quantity: number;
  profit: number;
  stopLoss?: number;
  takeProfit?: number;
  status: 'open' | 'closed';
  closedAt?: string;
  closePrice?: number;
}