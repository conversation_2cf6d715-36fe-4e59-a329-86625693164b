name: KDJ黄金交叉择时策略
description: 基于KDJ指标黄金交叉和死叉的专业择时策略，结合J值加速确认
version: 1.0.0
author: QuantCard团队
tags:
  - 择时
  - KDJ交叉
  - 技术指标
  - 随机指标

parameters:
  k_period:
    type: int
    default: 9
    min: 5
    max: 21
    description: K值计算周期
  d_period:
    type: int
    default: 3
    min: 2
    max: 9
    description: D值平滑周期
  j_period:
    type: int
    default: 3
    min: 2
    max: 9
    description: J值计算参数
  oversold_level:
    type: float
    default: 20.0
    min: 10.0
    max: 30.0
    description: KDJ超卖阈值
  overbought_level:
    type: float
    default: 80.0
    min: 70.0
    max: 90.0
    description: KDJ超买阈值
  min_cross_strength:
    type: float
    default: 2.0
    min: 1.0
    max: 10.0
    description: 最小交叉强度
  use_j_confirmation:
    type: boolean
    default: true
    description: 是否使用J值确认信号
  j_acceleration_threshold:
    type: float
    default: 10.0
    min: 5.0
    max: 20.0
    description: J值加速阈值

data_sources:
  timing:
    database:
      type: questdb
      table: kline_1min
    fields:
      - symbol
      - time
      - open
      - high
      - low
      - close
      - volume
      - amount
    time_field: time
  backtest:
    database:
      type: questdb
      table: kline_1min
    fields:
      - symbol
      - time
      - open
      - high
      - low
      - close
      - volume
      - amount
    time_field: time
  monitor:
    database:
      type: questdb
      table: kline_1min
    fields:
      - symbol
      - time
      - open
      - high
      - low
      - close
      - volume
      - amount
    time_field: time