import React, { useState, useEffect } from 'react';
import { Modal, Card, Button, Radio, Input, Form, Select, Tabs, Space, Divider } from 'antd';
import { AppstoreOutlined, AreaChartOutlined, PieChartOutlined, BellOutlined, CodeOutlined } from '@ant-design/icons';
import type { RadioChangeEvent } from 'antd';
import './AddWidgetModal.scss';
import { widgetTemplates } from '../../services/layoutManager';

// 选项定义
const symbols = [
  { value: 'BTC/USDT', label: 'BTC/USDT - 比特币' },
  { value: 'ETH/USDT', label: 'ETH/USDT - 以太坊' },
  { value: '000001.SH', label: '000001.SH - 上证指数' },
  { value: '399001.SZ', label: '399001.SZ - 深证成指' },
  { value: '399006.SZ', label: '399006.SZ - 创业板指' },
  { value: 'HSI', label: 'HSI - 恒生指数' },
];

const intervals = [
  { value: '1m', label: '1分钟' },
  { value: '5m', label: '5分钟' },
  { value: '15m', label: '15分钟' },
  { value: '30m', label: '30分钟' },
  { value: '1h', label: '1小时' },
  { value: '4h', label: '4小时' },
  { value: '1d', label: '日线' },
  { value: '1w', label: '周线' },
];

const marketTypes = [
  { value: 'crypto', label: '加密货币' },
  { value: 'china', label: 'A股市场' },
  { value: 'us', label: '美股市场' },
  { value: 'hk', label: '港股市场' },
];

export interface WidgetTemplate {
  id: string;
  icon: React.ReactNode;
  title: string;
  description: string;
  component: string;
  defaultWidth: number;
  defaultHeight: number;
}

interface AddWidgetModalProps {
  visible: boolean;
  onCancel: () => void;
  onAddWidget: (widgetType: string, title: string, settings?: Record<string, any>) => void;
}

// 小部件模板定义 - 在UI上显示的模板列表
const widgetTemplatesList = [
  {
    id: 'chart',
    icon: <AreaChartOutlined />,
    title: widgetTemplates.chart.defaultTitle,
    description: '展示各种市场的K线图、成交量和均线数据',
    component: 'chart',
    defaultWidth: widgetTemplates.chart.w,
    defaultHeight: widgetTemplates.chart.h,
  },
  {
    id: 'heatmap',
    icon: <PieChartOutlined />,
    title: widgetTemplates.heatmap.defaultTitle,
    description: '展示行业板块的涨跌幅热力图',
    component: 'heatmap',
    defaultWidth: widgetTemplates.heatmap.w,
    defaultHeight: widgetTemplates.heatmap.h,
  },
  {
    id: 'sentiment',
    icon: <AppstoreOutlined />,
    title: widgetTemplates.sentiment.defaultTitle,
    description: '展示市场的多空情绪、涨跌家数统计',
    component: 'sentiment',
    defaultWidth: widgetTemplates.sentiment.w,
    defaultHeight: widgetTemplates.sentiment.h,
  },
  {
    id: 'announcements',
    icon: <BellOutlined />,
    title: widgetTemplates.announcements.defaultTitle,
    description: '展示系统通知和策略执行状态',
    component: 'announcements',
    defaultWidth: widgetTemplates.announcements.w,
    defaultHeight: widgetTemplates.announcements.h,
  },
  {
    id: 'strategy-group',
    icon: <CodeOutlined />,
    title: widgetTemplates['strategy-group'].defaultTitle,
    description: '展示我的量化策略组及其运行情况',
    component: 'strategy-group',
    defaultWidth: widgetTemplates['strategy-group'].w,
    defaultHeight: widgetTemplates['strategy-group'].h,
  },
];

const AddWidgetModal: React.FC<AddWidgetModalProps> = ({ visible, onCancel, onAddWidget }) => {
  const [selectedWidget, setSelectedWidget] = useState<string>('chart');
  const [widgetTitle, setWidgetTitle] = useState<string>('');
  const [form] = Form.useForm();
  const [activeTab, setActiveTab] = useState<string>('basic');

  // 初始化设置状态
  const [settings, setSettings] = useState<Record<string, any>>({
    symbol: 'BTC/USDT',
    interval: '1h',
    marketType: 'crypto',
  });

  // 当选择的小部件改变时，更新标题和设置
  useEffect(() => {
    if (!visible) return; // 只在Modal可见时更新表单
    
    const widget = widgetTemplatesList.find(w => w.id === selectedWidget);
    if (widget) {
      setWidgetTitle(widget.title);
      form.setFieldsValue({ title: widget.title });
      
      // 根据组件类型设置默认配置
      const defaultSettings = widgetTemplates[selectedWidget]?.defaultSettings || {};
      
      setSettings(defaultSettings);
      form.setFieldsValue({ ...defaultSettings });
    }
  }, [selectedWidget, form, visible]);

  // 使用Modal的afterVisibleChange处理表单重置
  const handleVisibleChange = (isVisible: boolean) => {
    if (isVisible) {
      // Modal显示时设置初始值
      const widget = widgetTemplatesList.find(w => w.id === selectedWidget);
      if (widget) {
        setWidgetTitle(widget.title);
        form.setFieldsValue({ 
          title: widget.title,
          ...widgetTemplates[selectedWidget]?.defaultSettings
        });
      }
    }
  };

  const handleWidgetChange = (e: RadioChangeEvent) => {
    setSelectedWidget(e.target.value);
  };

  const handleAdd = () => {
    form.validateFields().then(values => {
      const { title, ...otherValues } = values;
      const finalTitle = title || widgetTitle || widgetTemplatesList.find(w => w.id === selectedWidget)?.title || '';
      
      // 创建设置对象，合并当前设置和表单值
      const widgetSettings = { ...settings, ...otherValues };
      
      if (finalTitle) {
        onAddWidget(selectedWidget, finalTitle, widgetSettings);
        setSelectedWidget('chart');
        setWidgetTitle('');
        form.resetFields();
        onCancel();
      }
    }).catch(err => {
      console.error('表单验证失败:', err);
    });
  };

  // 渲染不同类型组件的配置项
  const renderConfigOptions = () => {
    switch (selectedWidget) {
      case 'chart':
        return (
          <>
            <Form.Item name="symbol" label="交易品种">
              <Select
                options={symbols}
                onChange={(value) => setSettings({ ...settings, symbol: value })}
              />
            </Form.Item>
            <Form.Item name="interval" label="时间周期">
              <Select
                options={intervals}
                onChange={(value) => setSettings({ ...settings, interval: value })}
              />
            </Form.Item>
          </>
        );
      case 'heatmap':
        return (
          <Form.Item name="marketType" label="市场类型">
            <Select
              options={marketTypes}
              onChange={(value) => setSettings({ ...settings, marketType: value })}
            />
          </Form.Item>
        );
      // 其他类型的配置...
      default:
        return null;
    }
  };

  // 构建Tabs所需的items数组
  const tabItems = [
    {
      key: 'basic',
      label: '基本信息',
      children: (
        <>
          <Form.Item
            name="title"
            label="小部件标题"
            rules={[{ required: true, message: '请输入小部件标题' }]}
            initialValue={widgetTemplatesList.find(w => w.id === selectedWidget)?.title || ''}
          >
            <Input 
              placeholder="输入小部件标题" 
              value={widgetTitle} 
              onChange={(e) => setWidgetTitle(e.target.value)}
            />
          </Form.Item>

          <div className="widget-type-title">选择小部件类型</div>
          <Radio.Group onChange={handleWidgetChange} value={selectedWidget} className="widget-templates-container">
            {widgetTemplatesList.map(template => (
              <Radio key={template.id} value={template.id} className="widget-template-radio">
                <Card className="widget-template-card">
                  <div className="widget-template-icon">{template.icon}</div>
                  <div className="widget-template-title">{template.title}</div>
                  <div className="widget-template-description">{template.description}</div>
                </Card>
              </Radio>
            ))}
          </Radio.Group>
        </>
      )
    },
    {
      key: 'config',
      label: '配置选项',
      disabled: !selectedWidget,
      children: (
        <Space direction="vertical" style={{ width: '100%' }}>
          <div className="config-options-title">配置 {widgetTemplatesList.find(w => w.id === selectedWidget)?.title}</div>
          <Divider />
          {renderConfigOptions()}
        </Space>
      )
    }
  ];

  return (
    <Modal
      title="添加小部件"
      open={visible}
      onCancel={onCancel}
      afterOpenChange={handleVisibleChange}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          取消
        </Button>,
        <Button key="add" type="primary" onClick={handleAdd}>
          添加
        </Button>,
      ]}
      width={700}
      className="add-widget-modal"
    >
      <Form form={form} layout="vertical">
        <Tabs activeKey={activeTab} onChange={setActiveTab} centered items={tabItems} />
      </Form>
    </Modal>
  );
};

export default AddWidgetModal; 