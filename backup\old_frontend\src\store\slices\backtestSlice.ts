import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { BacktestResult } from '../../types/strategy';

interface BacktestState {
  backtests: BacktestResult[];
  selectedBacktest: BacktestResult | null;
  loading: boolean;
  error: string | null;
}

const initialState: BacktestState = {
  backtests: [],
  selectedBacktest: null,
  loading: false,
  error: null,
};

const backtestSlice = createSlice({
  name: 'backtest',
  initialState,
  reducers: {
    setBacktests: (state, action: PayloadAction<BacktestResult[]>) => {
      state.backtests = action.payload;
    },
    setSelectedBacktest: (state, action: PayloadAction<BacktestResult>) => {
      state.selectedBacktest = action.payload;
    },
    clearSelectedBacktest: (state) => {
      state.selectedBacktest = null;
    },
    addBacktest: (state, action: PayloadAction<BacktestResult>) => {
      state.backtests.push(action.payload);
    },
    updateBacktest: (state, action: PayloadAction<BacktestResult>) => {
      const index = state.backtests.findIndex(b => b.id === action.payload.id);
      if (index !== -1) {
        state.backtests[index] = action.payload;
      }
    },
    deleteBacktest: (state, action: PayloadAction<string>) => {
      state.backtests = state.backtests.filter(b => b.id !== action.payload);
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
  },
});

export const {
  setBacktests,
  setSelectedBacktest,
  clearSelectedBacktest,
  addBacktest,
  updateBacktest,
  deleteBacktest,
  setLoading,
  setError,
} = backtestSlice.actions;

export default backtestSlice.reducer; 