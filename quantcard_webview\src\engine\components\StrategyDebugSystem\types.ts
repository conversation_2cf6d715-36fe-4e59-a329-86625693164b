/**
 * 🎯 策略调试系统类型定义
 * 基于旧版前端的赛博朋克风格设计
 */

export interface PriceAnalysis {
  average: number;
  highest: number;
  lowest: number;
  deviation: number;
}

export interface Statistics {
  total: number;
  matched: number;
  ratio: string;
  price_analysis?: PriceAnalysis;
}

// 统一的信号接口，匹配后端 Signal 类
export interface Signal {
  // 基础信号字段
  symbol: string;
  name: string;
  direction: string;
  confidence: number;
  type: string;
  trigger_condition: string;

  // 市场数据字段
  price: number;
  change_pct: number;
  volume: number;
  market_cap?: number;

  // 择时信号专用字段
  signal_strength?: number;

  // 内部使用字段
  id: string;
  strategy_id: string;
  group_id: string;
  timestamp: string;

  // 元数据字段
  metadata: Record<string, any>;
}

// 简化的股票数据接口，直接使用顶层字段
export interface Stock {
  symbol: string;
  name: string;
  price: number;
  change_pct: number;
  market_cap: number;
  industry: string;
}

// 简化的择时信号接口，直接使用顶层字段
export interface TimingSignal {
  symbol: string;
  name: string;
  direction: 'BUY' | 'SELL' | 'UNKNOWN';
  trigger_condition: string;
  latest_price: number;
  ma_diff_pct: number;
  industry: string;
}

export interface DebugResult {
  success: boolean;
  message: string;
  logs?: string[];
  data: {
    signals: Signal[];
    statistics: Statistics;
    filter_condition: string;
    execution_time: string;
    strategy_type?: 'filter' | 'timing';
    logs?: string[];
  } | null;
}

export interface DebugConsoleProps {
  result: DebugResult | null;
  onClose?: () => void;
  strategyType?: 'filter' | 'timing';
  loading?: boolean;
}

export interface LogDisplayProps {
  logs: string[];
  expanded?: boolean;
  onToggleExpanded?: (expanded: boolean) => void;
}

export interface SignalTableProps {
  signals: Signal[];
  strategyType: 'filter' | 'timing';
  loading?: boolean;
}