"""
服务模块，包含各种数据服务
"""

# 导入基础服务，这些不会造成循环引用
from .realtime_quote_service import RealTimeQuoteService
from .minute_kline_service import MinuteKlineService
from .daily_kline_service import DailyKlineService
from .market_data_service import MarketDataService
from .trading_calendar import TradingCalendar, trading_calendar
from .timing_data_manager import TimingDataManager
# 代理服务暂时注释，保留未来使用可能性
# from .proxy_service import ProxyService

# 延迟导入策略服务，避免循环导入
# from .strategy_service import StrategyService

# 只导出不引起循环导入的服务
__all__ = [
    'RealTimeQuoteService',
    'MinuteKlineService',
    'DailyKlineService',
    # 'StrategyService',  # 移除策略服务的直接导入
    'MarketDataService',
    'TradingCalendar',
    'trading_calendar',  # 添加回全局实例
    'TimingDataManager',
    # 代理服务暂时注释，保留未来使用可能性
    # 'ProxyService'
] 