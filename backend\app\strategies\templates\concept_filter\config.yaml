data_sources:
  backtest:
    database:
      name: stock_base_info
      schema: public
      table: stock_concept_list_data_rt
      type: postgresql
    fields:
    - 股票代码
    - 股票名称
    - 概念名称
    frequency: 1d
    window_size: 1
  filter:
    database:
      name: stock_base_info
      schema: public
      table: stock_concept_list_data_rt
      type: postgresql
    fields:
    - 股票代码
    - 股票名称
    - 概念名称
    frequency: tick
    window_size: 1
  monitor: &id001
    database:
      name: stock_base_info
      schema: public
      table: stock_concept_list_data_rt
      type: postgresql
    fields:
    - 股票代码
    - 股票名称
    - 概念名称
    frequency: 1m
    window_size: 2
  timing: *id001
description: 根据概念名称中包含的关键字进行筛选
name: 概念名称筛选策略
outputs:
  backtest:
    fields:
    - 股票代码
    - 股票名称
    - 概念名称
    - 命中时间
    - 信号类型
    type: backtest_result
  filter:
    fields:
    - 股票代码
    - 股票名称
    - 概念名称
    type: stock_list
  monitor: &id002
    fields:
    - 股票代码
    - 股票名称
    - 概念名称
    - 触发条件
    - 触发时间
    type: alert
  timing: *id002
strategy_class_name: ConceptFilterStrategy
version: 1.0.0
