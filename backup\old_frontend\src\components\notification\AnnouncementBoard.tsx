import React, { useState, useEffect } from 'react';
import { Card, List, Tag, Typography, Space, Badge, Tooltip } from 'antd';
import { 
  NotificationOutlined,
  CheckCircleOutlined,
  WarningOutlined,
  InfoCircleOutlined,
  ClockCircleOutlined
} from '@ant-design/icons';
import './AnnouncementBoard.scss';

const { Text, Paragraph } = Typography;

interface Announcement {
  id: string;
  type: 'success' | 'warning' | 'info';
  title: string;
  content: string;
  timestamp: string;
  strategy?: string;
  read?: boolean;
}

interface AnnouncementBoardProps {
  announcements: Announcement[];
  onAnnouncementClick?: (announcement: Announcement) => void;
}

const AnnouncementBoard: React.FC<AnnouncementBoardProps> = ({
  announcements,
  onAnnouncementClick
}) => {
  const [unreadCount, setUnreadCount] = useState(0);

  useEffect(() => {
    const count = announcements.filter(a => !a.read).length;
    setUnreadCount(count);
  }, [announcements]);

  const getIcon = (type: string) => {
    switch (type) {
      case 'success':
        return <CheckCircleOutlined style={{ color: '#50fa7b' }} />;
      case 'warning':
        return <WarningOutlined style={{ color: '#ffb86c' }} />;
      case 'info':
      default:
        return <InfoCircleOutlined style={{ color: '#8be9fd' }} />;
    }
  };

  const getTimeAgo = (timestamp: string) => {
    const now = new Date();
    const time = new Date(timestamp);
    const diff = now.getTime() - time.getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) return `${days}天前`;
    if (hours > 0) return `${hours}小时前`;
    if (minutes > 0) return `${minutes}分钟前`;
    return '刚刚';
  };

  return (
    <Card
      className="announcement-board"
      title={
        <Space>
          <NotificationOutlined />
          <span>系统公告</span>
          {unreadCount > 0 && (
            <Badge count={unreadCount} style={{ backgroundColor: '#ff79c6' }} />
          )}
        </Space>
      }
    >
      <List
        dataSource={announcements}
        renderItem={item => (
          <List.Item
            className={`announcement-item ${!item.read ? 'unread' : ''}`}
            onClick={() => onAnnouncementClick?.(item)}
          >
            <List.Item.Meta
              avatar={getIcon(item.type)}
              title={
                <Space>
                  <Text strong>{item.title}</Text>
                  {item.strategy && (
                    <Tag color="purple">{item.strategy}</Tag>
                  )}
                </Space>
              }
              description={
                <div className="announcement-content">
                  <Paragraph ellipsis={{ rows: 2 }}>
                    {item.content}
                  </Paragraph>
                  <div className="announcement-footer">
                    <Tooltip title={new Date(item.timestamp).toLocaleString()}>
                      <Space>
                        <ClockCircleOutlined />
                        <span>{getTimeAgo(item.timestamp)}</span>
                      </Space>
                    </Tooltip>
                  </div>
                </div>
              }
            />
          </List.Item>
        )}
      />
    </Card>
  );
};

export default AnnouncementBoard; 