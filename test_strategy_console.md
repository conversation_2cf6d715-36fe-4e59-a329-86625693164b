# 策略控制台测试指南

## 测试目标
验证新创建的策略控制台场景功能完整性，包括页面切换、移动端交互和各个子页面的功能。

## 测试步骤

### 1. 场景入口测试
**步骤：**
1. 启动应用，进入世界地图
2. 点击"策略控制台"地标（🎮图标）
3. 验证是否正确跳转到策略控制台场景

**预期结果：**
- 地标显示为"策略控制台"而不是"策略控制中心"
- 点击后显示"进入策略控制台"通知
- 成功跳转到策略控制台场景

### 2. 页面切换测试
**步骤：**
1. 在策略控制台中，点击顶部的三个标签页
2. 验证页面切换动画和内容加载
3. 测试手势滑动切换（移动端）

**预期结果：**
- 三个标签页：📊我的策略、📡监控室、🧪实验室
- 页面切换流畅，有滑动动画效果
- 移动端支持左右滑动切换页面

### 3. 我的策略页面测试
**步骤：**
1. 进入"我的策略"页面
2. 验证策略组列表加载
3. 测试筛选功能（全部、运行中、已停止、择时、选股）
4. 点击"创建策略"按钮

**预期结果：**
- 正确显示用户的策略组列表
- 筛选功能正常工作
- 策略卡片显示完整信息（名称、状态、类型、卡片数量）
- 点击"创建策略"跳转到策略创建场景

### 4. 监控室页面测试
**步骤：**
1. 切换到"监控室"页面
2. 验证实时数据更新
3. 检查统计概览和策略监控列表

**预期结果：**
- 显示总策略数、运行中数量、今日信号、成功率
- 状态指示器显示在线状态（绿色闪烁）
- 运行中的策略显示实时性能数据和日志

### 5. 实验室页面测试
**步骤：**
1. 切换到"实验室"页面
2. 查看可用功能列表
3. 点击可用功能（策略优化器、回测引擎）

**预期结果：**
- 显示6个实验室功能卡片
- 可用功能可以点击，未开放功能显示"即将推出"
- 快速操作区域功能正常

### 6. 移动端交互测试
**步骤：**
1. 在移动设备或浏览器开发者工具的移动模式下测试
2. 验证触摸操作、滑动手势
3. 检查响应式布局

**预期结果：**
- 所有按钮符合44px最小触摸目标
- 左右滑动可以切换页面
- 布局在不同屏幕尺寸下正常显示

## 功能验证清单

### 场景路由
- [ ] 世界地图地标名称更新为"策略控制台"
- [ ] 地标点击正确跳转到Strategy场景
- [ ] SceneRouter正确映射Strategy到StrategyConsoleScene

### 策略控制台主场景
- [ ] 页面标题显示"策略控制台"
- [ ] 三个标签页正确显示和切换
- [ ] 页面切换动画流畅
- [ ] 手势滑动切换功能正常

### 我的策略页面
- [ ] 策略组列表正确加载和显示
- [ ] 筛选功能正常工作
- [ ] 策略卡片信息完整显示
- [ ] 创建策略按钮跳转正确
- [ ] 执行/停止按钮功能正常

### 监控室页面
- [ ] 统计数据正确显示
- [ ] 实时更新功能正常
- [ ] 策略监控列表显示运行中策略
- [ ] 性能指标和日志正确显示

### 实验室页面
- [ ] 功能卡片正确显示
- [ ] 可用/不可用状态正确标识
- [ ] 快速操作按钮功能正常
- [ ] 即将推出标识正确显示

### 移动端优化
- [ ] 触摸目标大小符合标准
- [ ] 滑动手势响应正常
- [ ] 响应式布局适配不同屏幕
- [ ] 动画性能良好

## 已知问题和改进点

### 当前限制
1. 监控室数据为模拟数据，需要连接真实API
2. 实验室功能大部分为占位符，需要后续实现
3. 策略执行API调用为模拟，需要连接后端

### 后续优化方向
1. 添加下拉刷新功能
2. 实现策略组的编辑和删除功能
3. 添加策略性能图表展示
4. 优化加载状态和错误处理
5. 添加搜索和排序功能

## 测试环境要求
- 前端开发服务器运行正常
- 后端API服务可访问
- 用户已登录且有策略组数据
- 移动端测试环境或浏览器开发者工具
