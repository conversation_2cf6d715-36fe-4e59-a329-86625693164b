data_sources:
  backtest:
    cache:
      enabled: true
      ttl: 120
    database:
      table: performance_forecast_rt
      type: postgresql
    fields:
    - 股票代码
    - 预测指标
    - 业绩变动幅度
    - 预告类型
  filter:
    cache:
      enabled: true
      ttl: 120
    database:
      table: performance_forecast_rt
      type: postgresql
    fields:
    - 股票代码
    - 预测指标
    - 业绩变动幅度
    - 预告类型
  monitor: &id001
    cache:
      enabled: true
      ttl: 60
    database:
      table: performance_forecast_rt
      type: postgresql
    fields:
    - 股票代码
    - 预测指标
    - 业绩变动幅度
    - 预告类型
  timing: *id001
execution:
  max_symbols: 3000
  timeout: 60
logging:
  format: '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
  level: info
