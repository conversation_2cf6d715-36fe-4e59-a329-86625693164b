import React, { useRef, useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useUIState } from '../../store/hooks';
import './AdventureScene.scss';

// 关卡难度类型
type Difficulty = 'tutorial' | 'easy' | 'medium' | 'hard' | 'expert' | 'legendary';

// 关卡数据接口
interface AdventureLevel {
  id: string;
  name: string;
  description: string;
  difficulty: Difficulty;
  prerequisites: string[];
  position: { x: number; y: number };
  icon: string;
  rewards: {
    cards: string[];
    coins: number;
    experience: number;
  };
  stars: 0 | 1 | 2 | 3;
  unlocked: boolean;
  completed: boolean;
  category: 'tutorial' | 'strategy' | 'analysis' | 'battle' | 'challenge';
}

// 关卡路径连接
interface LevelConnection {
  from: string;
  to: string;
  unlocked: boolean;
}

/**
 * 冒险闯关场景 - 渐进式策略学习系�? */
export default function AdventureScene({ sceneData }: { sceneData?: any }) {
  const { switchScene } = useUIState();
  const sceneRef = useRef<HTMLDivElement>(null);
  const [selectedLevel, setSelectedLevel] = useState<AdventureLevel | null>(null);
  const [playerProgress, setPlayerProgress] = useState({
    totalStars: 12,
    completedLevels: 8,
    currentLevel: 'strategy_basics_2'
  });

  // 关卡数据
  const [levels] = useState<AdventureLevel[]>([
    // 教学关卡
    {
      id: 'tutorial_start',
      name: '交易初体验',
      description: '学习基本的交易概念和平台操作',
      difficulty: 'tutorial',
      prerequisites: [],
      position: { x: 50, y: 85 },
      icon: '📚',
      rewards: { cards: ['基础K线'], coins: 100, experience: 50 },
      stars: 3,
      unlocked: true,
      completed: true,
      category: 'tutorial'
    },
    {
      id: 'tutorial_cards',
      name: '策略卡牌入门',
      description: '了解策略卡牌的使用方法',
      difficulty: 'tutorial', 
      prerequisites: ['tutorial_start'],
      position: { x: 35, y: 75 },
      icon: '🃏',
      rewards: { cards: ['移动平均'], coins: 150, experience: 75 },
      stars: 3,
      unlocked: true,
      completed: true,
      category: 'tutorial'
    },

    // 策略基础关卡
    {
      id: 'strategy_basics_1',
      name: '趋势跟踪',
      description: '掌握趋势跟踪策略的核心思想',
      difficulty: 'easy',
      prerequisites: ['tutorial_cards'],
      position: { x: 25, y: 60 },
      icon: '📈',
      rewards: { cards: ['MACD指标'], coins: 200, experience: 100 },
      stars: 2,
      unlocked: true,
      completed: true,
      category: 'strategy'
    },
    {
      id: 'strategy_basics_2',
      name: '均值回归',
      description: '学习均值回归策略的应用场景',
      difficulty: 'easy',
      prerequisites: ['strategy_basics_1'],
      position: { x: 40, y: 50 },
      icon: '🎯',
      rewards: { cards: ['RSI震荡'], coins: 200, experience: 100 },
      stars: 1,
      unlocked: true,
      completed: false,
      category: 'strategy'
    },

    // 技术分析关卡
    {
      id: 'analysis_advanced',
      name: '高级技术分析',
      description: '结合多个指标进行综合分析',
      difficulty: 'medium',
      prerequisites: ['strategy_basics_2'],
      position: { x: 60, y: 45 },
      icon: '🔬',
      rewards: { cards: ['布林带策略'], coins: 300, experience: 150 },
      stars: 0,
      unlocked: true,
      completed: false,
      category: 'analysis'
    },
    {
      id: 'pattern_recognition',
      name: '图形识别大师',
      description: '识别经典的技术分析图形',
      difficulty: 'medium',
      prerequisites: ['analysis_advanced'],
      position: { x: 75, y: 35 },
      icon: '🧩',
      rewards: { cards: ['头肩顶判断'], coins: 350, experience: 175 },
      stars: 0,
      unlocked: false,
      completed: false,
      category: 'analysis'
    },

    // 对战挑战关卡
    {
      id: 'ai_battle_1',
      name: 'AI初级挑战',
      description: '与AI进行策略对战',
      difficulty: 'medium',
      prerequisites: ['strategy_basics_2'],
      position: { x: 20, y: 40 },
      icon: '🤖',
      rewards: { cards: ['动量策略'], coins: 400, experience: 200 },
      stars: 0,
      unlocked: false,
      completed: false,
      category: 'battle'
    },
    {
      id: 'market_crisis',
      name: '市场危机应对',
      description: '在极端市场条件下测试策略',
      difficulty: 'hard',
      prerequisites: ['ai_battle_1', 'pattern_recognition'],
      position: { x: 50, y: 25 },
      icon: '🌪️',
      rewards: { cards: ['避险策略'], coins: 500, experience: 300 },
      stars: 0,
      unlocked: false,
      completed: false,
      category: 'challenge'
    },

    // 终极挑战
    {
      id: 'final_trial',
      name: '量化大师试炼',
      description: '终极挑战：构建完整的量化交易系统',
      difficulty: 'legendary',
      prerequisites: ['market_crisis'],
      position: { x: 50, y: 10 },
      icon: '👑',
      rewards: { cards: ['大师级策略包'], coins: 1000, experience: 500 },
      stars: 0,
      unlocked: false,
      completed: false,
      category: 'challenge'
    }
  ]);

  // 关卡连接路径
  const connections: LevelConnection[] = [
    { from: 'tutorial_start', to: 'tutorial_cards', unlocked: true },
    { from: 'tutorial_cards', to: 'strategy_basics_1', unlocked: true },
    { from: 'strategy_basics_1', to: 'strategy_basics_2', unlocked: true },
    { from: 'strategy_basics_2', to: 'analysis_advanced', unlocked: true },
    { from: 'strategy_basics_2', to: 'ai_battle_1', unlocked: false },
    { from: 'analysis_advanced', to: 'pattern_recognition', unlocked: false },
    { from: 'ai_battle_1', to: 'market_crisis', unlocked: false },
    { from: 'pattern_recognition', to: 'market_crisis', unlocked: false },
    { from: 'market_crisis', to: 'final_trial', unlocked: false }
  ];

  useEffect(() => {
    console.log('Adventure Scene initialized:', sceneData);
  }, [sceneData]);

  /**
   * 处理关卡点击
   */
  const handleLevelClick = (level: AdventureLevel) => {
    if (!level.unlocked) {
      return;
    }

    setSelectedLevel(level);

    // 如果是已完成关卡，显示详情；否则开始关卡
    setTimeout(() => {
      if (level.completed) {
        console.log(`查看关卡详情: ${level.name}`);
      } else {
        startLevel(level);
      }
    }, 300);
  };

  /**
   * 开始关卡   */
  const startLevel = (level: AdventureLevel) => {
    console.log(`开始关卡 ${level.name}`);
    
    // 根据关卡类型跳转到不同场景
    switch (level.category) {
      case 'tutorial':
      case 'strategy':
        switchScene('Strategy');
        break;
      case 'analysis':
        // 跳转到图表分析场景
        console.log('跳转到分析场景');
        break;
      case 'battle':
        switchScene('Battle');
        break;
      case 'challenge':
        // 特殊挑战场景
        console.log('跳转到挑战场景');
        break;
    }
  };

  /**
   * 获取难度颜色
   */
  const getDifficultyColor = (difficulty: Difficulty) => {
    const colors = {
      tutorial: '#10b981',
      easy: '#3b82f6',
      medium: '#f59e0b',
      hard: '#ef4444',
      expert: '#8b5cf6',
      legendary: '#f59e0b'
    };
    return colors[difficulty];
  };

  /**
   * 渲染星级
   */
  const renderStars = (stars: number) => {
    return Array.from({ length: 3 }).map((_, i) => (
      <span 
        key={i}
        className={`star ${i < stars ? 'filled' : 'empty'}`}
      >
        ★
      </span>
    ));
  };

  return (
    <div className="adventure-scene" ref={sceneRef}>
      {/* 背景 */}
      <div className="adventure-background">
        <div className="bg-gradient" />
        <div className="bg-particles" />
        <div className="bg-grid" />
      </div>

      {/* 标题区域 */}
      <div className="adventure-header">
        <motion.div
          className="header-content"
          initial={{ opacity: 0, y: -30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
        >
          <h1 className="adventure-title">
            <span className="title-icon">⚔️</span>
            策略冒险之旅
          </h1>
          <div className="progress-info">
            <div className="progress-item">
              <span className="label">总星数</span>
              <span className="value">{playerProgress.totalStars}/27</span>
            </div>
            <div className="progress-item">
              <span className="label">完成关卡</span>
              <span className="value">{playerProgress.completedLevels}/9</span>
            </div>
          </div>
        </motion.div>
      </div>

      {/* 关卡地图 */}
      <div className="level-map">
        {/* 连接路径 */}
        <svg className="connection-paths" viewBox="0 0 100 100" preserveAspectRatio="none">
          {connections.map((conn, index) => {
            const fromLevel = levels.find(l => l.id === conn.from);
            const toLevel = levels.find(l => l.id === conn.to);
            if (!fromLevel || !toLevel) return null;

            return (
              <motion.line
                key={`${conn.from}-${conn.to}`}
                x1={fromLevel.position.x}
                y1={fromLevel.position.y}
                x2={toLevel.position.x}
                y2={toLevel.position.y}
                className={`connection-line ${conn.unlocked ? 'unlocked' : 'locked'}`}
                initial={{ pathLength: 0 }}
                animate={{ pathLength: conn.unlocked ? 1 : 0.3 }}
                transition={{ duration: 1, delay: index * 0.1 }}
              />
            );
          })}
        </svg>

        {/* 关卡节点 */}
        {levels.map((level, index) => (
          <motion.div
            key={level.id}
            className={`level-node ${level.difficulty} ${
              level.completed ? 'completed' : level.unlocked ? 'unlocked' : 'locked'
            } ${selectedLevel?.id === level.id ? 'selected' : ''}`}
            style={{
              left: `${level.position.x}%`,
              top: `${level.position.y}%`,
              '--difficulty-color': getDifficultyColor(level.difficulty)
            } as any}
            onClick={() => handleLevelClick(level)}
            initial={{ scale: 0, opacity: 0 }}
            animate={{ 
              scale: 1, 
              opacity: 1,
              y: selectedLevel?.id === level.id ? -10 : 0
            }}
            transition={{ 
              duration: 0.5, 
              delay: index * 0.1,
              y: { duration: 0.3 }
            }}
            whileHover={{ 
              scale: level.unlocked ? 1.1 : 1,
              y: level.unlocked ? -5 : 0
            }}
            whileTap={{ scale: level.unlocked ? 0.95 : 1 }}
          >
            {/* 节点主体 */}
            <div className="node-core">
              <div className="node-icon">{level.icon}</div>
              {level.completed && <div className="completion-check">✓</div>}
              {!level.unlocked && <div className="lock-icon">🔒</div>}
            </div>

            {/* 发光环效果 */}
            <div className="node-glow" />

            {/* 脉冲效果 */}
            {level.id === playerProgress.currentLevel && (
              <div className="node-pulse" />
            )}

            {/* 标签 */}
            <div className="node-label">
              <div className="level-name">{level.name}</div>
              <div className="level-stars">{renderStars(level.stars)}</div>
            </div>
          </motion.div>
        ))}
      </div>

      {/* 关卡详情面板 */}
      <AnimatePresence>
        {selectedLevel && (
          <motion.div
            className="level-details-panel"
            initial={{ opacity: 0, x: 300 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 300 }}
            transition={{ duration: 0.4 }}
          >
            <div className="panel-header">
              <div className="level-icon">{selectedLevel.icon}</div>
              <div className="level-info">
                <h3 className="level-title">{selectedLevel.name}</h3>
                <div className="level-meta">
                  <span className={`difficulty ${selectedLevel.difficulty}`}>
                    {selectedLevel.difficulty.toUpperCase()}
                  </span>
                  <div className="stars">{renderStars(selectedLevel.stars)}</div>
                </div>
              </div>
            </div>

            <div className="panel-content">
              <p className="level-description">{selectedLevel.description}</p>

              <div className="rewards-section">
                <h4>关卡奖励</h4>
                <div className="rewards-list">
                  <div className="reward-item">
                    <span className="reward-icon">🃏</span>
                    <span>{selectedLevel.rewards.cards.join(', ')}</span>
                  </div>
                  <div className="reward-item">
                    <span className="reward-icon">💰</span>
                    <span>{selectedLevel.rewards.coins} 金币</span>
                  </div>
                  <div className="reward-item">
                    <span className="reward-icon">⚡</span>
                    <span>{selectedLevel.rewards.experience} 经验</span>
                  </div>
                </div>
              </div>

              <div className="panel-actions">
                {selectedLevel.unlocked ? (
                  <button 
                    className={`action-btn ${selectedLevel.completed ? 'replay' : 'start'}`}
                    onClick={() => startLevel(selectedLevel)}
                  >
                    {selectedLevel.completed ? '重新挑战' : '开始关卡'}
                  </button>
                ) : (
                  <button className="action-btn locked" disabled>
                    需要完成前置关卡
                  </button>
                )}
              </div>
            </div>

            <button 
              className="panel-close"
              onClick={() => setSelectedLevel(null)}
            >
              ×
            </button>
          </motion.div>
        )}
      </AnimatePresence>

      {/* 底部控制栏 */}
      <div className="adventure-controls">
        <button 
          className="control-btn back"
          onClick={() => switchScene('WorldMap')}
        >
          <span className="btn-icon">🌍</span>
          <span className="btn-text">世界地图</span>
        </button>

        <button 
          className="control-btn collection"
          onClick={() => switchScene('Codex')}
        >
          <span className="btn-icon">🃏</span>
          <span className="btn-text">卡牌收藏</span>
        </button>

        <button 
          className="control-btn profile"
          onClick={() => switchScene('Profile')}
        >
          <span className="btn-icon">👤</span>
          <span className="btn-text">个人资料</span>
        </button>
      </div>
    </div>
  );
}
