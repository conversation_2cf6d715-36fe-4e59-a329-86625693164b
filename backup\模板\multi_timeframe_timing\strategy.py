"""
多时间框架共振择时策略
通过多个时间周期的技术指标共振确认交易信号
"""
import logging
import pandas as pd
import numpy as np
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import yaml
import json
from pathlib import Path

from app.core.strategy.base import UnifiedStrategyCard
from app.core.runtime.types import RuntimeContext, Signal, SignalType, StrategyMode
from app.services.minute_kline_service import MinuteKlineService

logger = logging.getLogger(__name__)

# 从 template.json 加载配置
template_path = Path(__file__).parent / "template.json"
with open(template_path, "r", encoding="utf-8") as f:
    template = json.load(f)

class MultiTimeframeTiming(UnifiedStrategyCard):
    """多时间框架共振择时策略：综合多周期技术指标进行择时分析"""
    
    def __init__(self, context: Optional[RuntimeContext] = None):
        """初始化策略"""
        super().__init__(context)
        
        # 加载配置文件
        config_path = Path(__file__).parent / "config.yaml"
        with open(config_path, "r", encoding="utf-8") as f:
            self.config = yaml.safe_load(f)
        
        # 初始化分钟K线服务
        self.kline_service = MinuteKlineService.get_instance()

    async def prepare_data(self, context: RuntimeContext) -> Dict[str, pd.DataFrame]:
        """准备数据"""
        try:
            # 检查串行执行模式
            if not self._check_sequential_mode(context):
                return {}
            
            # 从context.data_cache获取K线数据
            kline_data = context.data_cache.get("kline_data", {})
            if not kline_data:
                self._log("未能从上下文中获取K线数据", "warning")
                return {}
            
            # 处理每只股票的数据
            for symbol, df in kline_data.items():
                # 确保数据按时间排序
                df.sort_values(by='time', inplace=True)
                
                # 构造多时间框架数据
                df = self._create_multiple_timeframes(df, context.parameters)
                
                # 计算各时间框架的技术指标
                df = self._calculate_multi_timeframe_indicators(df, context.parameters)
                
                # 分析共振信号
                df = self._analyze_resonance_signals(df, context.parameters)
                
                # 打印最近的共振数据用于调试
                last_rows = min(10, len(df))
                if last_rows > 0:
                    self._log(f"股票{symbol}最近{last_rows}条共振数据:")
                    recent_data = df.tail(last_rows).copy()
                    recent_data['formatted_time'] = recent_data['time'].dt.strftime('%Y-%m-%d %H:%M:%S')
                    debug_cols = ['formatted_time', 'close', 'trend_1m', 'trend_5m', 'trend_15m', 'resonance_score', 'resonance_signal']
                    available_cols = [col for col in debug_cols if col in recent_data.columns]
                    self._log(recent_data[available_cols].to_string())
            
            self._log(f"成功获取{len(kline_data)}只股票的多时间框架数据")
            return kline_data
            
        except Exception as e:
            self._log(f"准备数据失败: {str(e)}", "error")
            return {}
    
    def _create_multiple_timeframes(self, df: pd.DataFrame, params: Dict[str, Any]) -> pd.DataFrame:
        """创建多时间框架数据"""
        try:
            # 基础1分钟数据已有，需要构造5分钟和15分钟数据
            
            # 5分钟数据重采样
            df_5m = self._resample_data(df, '5Min')
            df_15m = self._resample_data(df, '15Min')
            
            # 将高周期数据映射回1分钟数据
            df = self._map_higher_timeframe_to_base(df, df_5m, '5m')
            df = self._map_higher_timeframe_to_base(df, df_15m, '15m')
            
            return df
            
        except Exception as e:
            self._log(f"创建多时间框架数据失败: {str(e)}", "error")
            return df
    
    def _resample_data(self, df: pd.DataFrame, freq: str) -> pd.DataFrame:
        """重采样数据到指定频率"""
        try:
            df_resampled = df.set_index('time').resample(freq).agg({
                'open': 'first',
                'high': 'max',
                'low': 'min',
                'close': 'last',
                'volume': 'sum',
                'amount': 'sum'
            }).dropna()
            
            return df_resampled.reset_index()
            
        except Exception as e:
            self._log(f"重采样数据失败: {str(e)}", "error")
            return pd.DataFrame()
    
    def _map_higher_timeframe_to_base(self, base_df: pd.DataFrame, higher_df: pd.DataFrame, suffix: str) -> pd.DataFrame:
        """将高时间框架数据映射到基础时间框架"""
        try:
            if higher_df.empty:
                return base_df
            
            # 为高时间框架数据添加后缀
            higher_df_renamed = higher_df.copy()
            for col in ['open', 'high', 'low', 'close', 'volume', 'amount']:
                if col in higher_df_renamed.columns:
                    higher_df_renamed[f'{col}_{suffix}'] = higher_df_renamed[col]
            
            # 按时间合并，使用前向填充
            base_df = pd.merge_asof(
                base_df.sort_values('time'), 
                higher_df_renamed[['time'] + [col for col in higher_df_renamed.columns if col.endswith(suffix)]].sort_values('time'),
                on='time',
                direction='backward'
            )
            
            return base_df
            
        except Exception as e:
            self._log(f"映射高时间框架数据失败: {str(e)}", "error")
            return base_df
    
    def _calculate_multi_timeframe_indicators(self, df: pd.DataFrame, params: Dict[str, Any]) -> pd.DataFrame:
        """计算多时间框架技术指标"""
        try:
            ma_short = int(params.get("ma_short", 5))
            ma_long = int(params.get("ma_long", 20))
            rsi_period = int(params.get("rsi_period", 14))
            
            # 1分钟指标
            df['ma_short_1m'] = df['close'].rolling(window=ma_short).mean()
            df['ma_long_1m'] = df['close'].rolling(window=ma_long).mean()
            df['rsi_1m'] = self._calculate_rsi(df['close'], rsi_period)
            df['trend_1m'] = np.where(df['ma_short_1m'] > df['ma_long_1m'], 'up', 'down')
            
            # 5分钟指标（如果有5分钟数据）
            if f'close_5m' in df.columns:
                df['ma_short_5m'] = df['close_5m'].rolling(window=max(1, ma_short//5)).mean()
                df['ma_long_5m'] = df['close_5m'].rolling(window=max(1, ma_long//5)).mean()
                df['rsi_5m'] = self._calculate_rsi(df['close_5m'], max(1, rsi_period//5))
                df['trend_5m'] = np.where(df['ma_short_5m'] > df['ma_long_5m'], 'up', 'down')
            else:
                df['trend_5m'] = 'neutral'
                df['rsi_5m'] = 50
            
            # 15分钟指标（如果有15分钟数据）
            if f'close_15m' in df.columns:
                df['ma_short_15m'] = df['close_15m'].rolling(window=max(1, ma_short//15)).mean()
                df['ma_long_15m'] = df['close_15m'].rolling(window=max(1, ma_long//15)).mean()
                df['rsi_15m'] = self._calculate_rsi(df['close_15m'], max(1, rsi_period//15))
                df['trend_15m'] = np.where(df['ma_short_15m'] > df['ma_long_15m'], 'up', 'down')
            else:
                df['trend_15m'] = 'neutral'
                df['rsi_15m'] = 50
            
            return df
            
        except Exception as e:
            self._log(f"计算多时间框架指标失败: {str(e)}", "error")
            return df
    
    def _calculate_rsi(self, prices: pd.Series, period: int) -> pd.Series:
        """计算RSI指标"""
        try:
            if period <= 0:
                return pd.Series([50] * len(prices))
                
            delta = prices.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            return rsi.fillna(50)
        except Exception as e:
            self._log(f"计算RSI失败: {str(e)}", "error")
            return pd.Series([50] * len(prices))
    
    def _analyze_resonance_signals(self, df: pd.DataFrame, params: Dict[str, Any]) -> pd.DataFrame:
        """分析共振信号"""
        try:
            min_resonance_score = float(params.get("min_resonance_score", 2.0))
            rsi_oversold = float(params.get("rsi_oversold", 30))
            rsi_overbought = float(params.get("rsi_overbought", 70))
            
            df['resonance_score'] = 0.0
            df['resonance_signal'] = 'HOLD'
            df['resonance_strength'] = 0.0
            
            for i in range(len(df)):
                score = 0.0
                buy_signals = 0
                sell_signals = 0
                
                # 趋势共振评分
                if df.iloc[i]['trend_1m'] == 'up':
                    buy_signals += 1
                    score += 1
                elif df.iloc[i]['trend_1m'] == 'down':
                    sell_signals += 1
                    score -= 1
                
                if df.iloc[i]['trend_5m'] == 'up':
                    buy_signals += 1
                    score += 1.5  # 高时间框架权重更大
                elif df.iloc[i]['trend_5m'] == 'down':
                    sell_signals += 1
                    score -= 1.5
                
                if df.iloc[i]['trend_15m'] == 'up':
                    buy_signals += 1
                    score += 2  # 最高时间框架权重最大
                elif df.iloc[i]['trend_15m'] == 'down':
                    sell_signals += 1
                    score -= 2
                
                # RSI共振评分
                rsi_1m = df.iloc[i]['rsi_1m']
                rsi_5m = df.iloc[i]['rsi_5m']
                rsi_15m = df.iloc[i]['rsi_15m']
                
                # RSI超卖共振
                oversold_count = sum([
                    rsi_1m < rsi_oversold,
                    rsi_5m < rsi_oversold,
                    rsi_15m < rsi_oversold
                ])
                
                # RSI超买共振
                overbought_count = sum([
                    rsi_1m > rsi_overbought,
                    rsi_5m > rsi_overbought,
                    rsi_15m > rsi_overbought
                ])
                
                if oversold_count >= 2:  # 至少两个时间框架超卖
                    score += oversold_count * 0.5
                    buy_signals += oversold_count
                
                if overbought_count >= 2:  # 至少两个时间框架超买
                    score -= overbought_count * 0.5
                    sell_signals += overbought_count
                
                df.iloc[i, df.columns.get_loc('resonance_score')] = abs(score)
                
                # 生成共振信号
                if score >= min_resonance_score and buy_signals >= 3:
                    df.iloc[i, df.columns.get_loc('resonance_signal')] = 'BUY'
                    strength = min(0.9, 0.5 + (score - min_resonance_score) / 10)
                    df.iloc[i, df.columns.get_loc('resonance_strength')] = strength
                
                elif score <= -min_resonance_score and sell_signals >= 3:
                    df.iloc[i, df.columns.get_loc('resonance_signal')] = 'SELL'
                    strength = min(0.9, 0.5 + (abs(score) - min_resonance_score) / 10)
                    df.iloc[i, df.columns.get_loc('resonance_strength')] = strength
                
                else:
                    df.iloc[i, df.columns.get_loc('resonance_signal')] = 'HOLD'
                    df.iloc[i, df.columns.get_loc('resonance_strength')] = 0.3
            
            return df
            
        except Exception as e:
            self._log(f"分析共振信号失败: {str(e)}", "error")
            return df
    
    async def generate_signal(self, data: Dict[str, pd.DataFrame], params: Dict[str, Any]) -> List[Signal]:
        """生成信号"""
        try:
            if not data:
                return []
            
            signals = []
            for symbol, df in data.items():
                if df.empty or len(df) < 30:
                    self._log(f"股票{symbol}数据不足，无法生成共振信号")
                    continue
                
                # 获取最近的共振信号
                recent_signals = df[df['resonance_signal'] != 'HOLD'].tail(3)
                
                # 获取当前共振状态
                latest = df.iloc[-1]
                current_price = latest['close']
                resonance_score = latest['resonance_score']
                
                if not recent_signals.empty:
                    # 处理交易信号
                    for _, row in recent_signals.iterrows():
                        direction = row['resonance_signal']
                        confidence = row['resonance_strength']
                        
                        # 构建触发条件描述
                        trend_summary = f"{row['trend_1m']}/{row['trend_5m']}/{row['trend_15m']}"
                        condition = f"多周期共振_{trend_summary}_评分{row['resonance_score']:.1f}"
                        
                        signals.append(
                            self.create_signal(
                                symbol=symbol,
                                name=symbol,
                                direction=direction,
                                signal_type="technical",
                                confidence=confidence,
                                trigger_condition=condition,
                                current_price=float(current_price),
                                resonance_score=float(row['resonance_score']),
                                trend_1m=str(row['trend_1m']),
                                trend_5m=str(row['trend_5m']),
                                trend_15m=str(row['trend_15m']),
                                rsi_1m=float(row['rsi_1m']) if pd.notna(row['rsi_1m']) else 50,
                                rsi_5m=float(row['rsi_5m']) if pd.notna(row['rsi_5m']) else 50,
                                rsi_15m=float(row['rsi_15m']) if pd.notna(row['rsi_15m']) else 50
                            )
                        )
                else:
                    # 生成状态信号
                    trend_summary = f"{latest['trend_1m']}/{latest['trend_5m']}/{latest['trend_15m']}"
                    
                    if resonance_score >= 2.0:
                        condition = f"强共振状态_{trend_summary}_评分{resonance_score:.1f}"
                        confidence = 0.5
                    elif resonance_score >= 1.0:
                        condition = f"中等共振_{trend_summary}_评分{resonance_score:.1f}"
                        confidence = 0.4
                    else:
                        condition = f"弱共振状态_{trend_summary}_评分{resonance_score:.1f}"
                        confidence = 0.3
                    
                    signals.append(
                        self.create_signal(
                            symbol=symbol,
                            name=symbol,
                            direction="HOLD",
                            signal_type="technical",
                            confidence=confidence,
                            trigger_condition=condition,
                            current_price=float(current_price),
                            resonance_score=float(resonance_score),
                            trend_summary=trend_summary
                        )
                    )
            
            self._log(f"共生成{len(signals)}个多时间框架共振信号")
            return signals
            
        except Exception as e:
            self._log(f"生成信号失败: {str(e)}", "error")
            return []
    
    async def execute(self, context: RuntimeContext) -> List[Signal]:
        """执行策略"""
        try:
            # 添加执行模式日志
            self._log(f"执行多时间框架共振择时策略，模式: {context.mode}")
            
            # 准备数据
            data = await self.prepare_data(context)
            
            # 生成信号
            signals = await self.generate_signal(data, context.parameters)
            
            # 记录结果
            buy_signals = len([s for s in signals if s.direction == "BUY"])
            sell_signals = len([s for s in signals if s.direction == "SELL"])
            hold_signals = len([s for s in signals if s.direction == "HOLD"])
            
            self._log(f"多时间框架策略执行完成: 买入信号{buy_signals}个, 卖出信号{sell_signals}个, 观望信号{hold_signals}个")
            
            return signals
            
        except Exception as e:
            self._log(f"策略执行失败: {str(e)}", "error")
            return []