{"template_id": "ma_cross_timing", "name": "MA交叉择时策略", "description": "使用短期均线和长期均线的交叉来产生交易信号", "version": "2.0.0", "author": "QuantCard团队", "tags": ["择时", "均线", "技术指标"], "createTime": "2023-10-01T00:00:00Z", "updateTime": "2023-10-01T00:00:00Z", "parameters": {"short_period": {"label": "短期均线周期", "type": "number", "default": 5, "validation": {"required": true, "min": 2, "max": 60}, "placeholder": "请输入短期均线周期", "description": "短期均线周期，建议5-10", "unit": "日"}, "long_period": {"label": "长期均线周期", "type": "number", "default": 20, "validation": {"required": true, "min": 5, "max": 120}, "placeholder": "请输入长期均线周期", "description": "长期均线周期，建议20-60", "unit": "日"}, "lookback_days": {"label": "回溯天数", "type": "number", "default": 5, "validation": {"required": true, "min": 1, "max": 30}, "placeholder": "请输入回溯天数", "description": "回溯天数，影响数据加载量", "unit": "天"}, "signal_threshold": {"label": "信号阈值", "type": "number", "default": 0.01, "validation": {"required": true, "min": 0.001, "max": 10.0}, "placeholder": "请输入信号阈值", "description": "信号阈值（均线差距百分比）", "unit": "%"}}, "outputs": [{"name": "signals", "type": "signal", "description": "交易信号", "fields": ["symbol", "name", "direction", "trigger_condition", "latest_price", "ma_diff_pct"]}], "modes": [{"name": "timing", "label": "择时模式", "description": "实时监控市场数据，生成交易信号"}, {"name": "backtest", "label": "回测模式", "description": "使用历史数据进行回测"}], "supportedDataSources": ["questdb"], "examples": [{"name": "金叉买入示例", "description": "当5日均线上穿20日均线，可能是上涨信号", "parameters": {"short_period": 5, "long_period": 20, "lookback_days": 3, "signal_threshold": 0.02}}, {"name": "死叉卖出示例", "description": "当5日均线下穿20日均线，可能是下跌信号", "parameters": {"short_period": 5, "long_period": 20, "lookback_days": 3, "signal_threshold": 0.02}}], "ui": {"icon": "lineChart", "color": "#1890ff", "category": "择时", "order": 1, "form": {"layout": "horizontal", "compact": true, "showDescription": true}}}