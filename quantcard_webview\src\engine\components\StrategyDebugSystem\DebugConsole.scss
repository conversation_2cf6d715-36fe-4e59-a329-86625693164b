// 遮罩层
.debug-console-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

// 赛博朋克风格调试控制台样式
.strategy-debug-console {
  background-color: #282a36;
  border-radius: 12px;
  box-shadow: 0 0 20px rgba(189, 147, 249, 0.1),
              0 0 40px rgba(139, 233, 253, 0.1);
  color: #f8f8f2;
  overflow: hidden;
  border: 1px solid rgba(189, 147, 249, 0.2);
  display: flex;
  flex-direction: column;
  width: 90vw;
  max-width: 1200px;
  max-height: 90vh;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, 
      #ff79c6 0%,
      #bd93f9 33%,
      #8be9fd 66%,
      #50fa7b 100%
    );
    opacity: 0.8;
  }
  
  .debug-header {
    flex: 0 0 auto;
    background-color: #1e1f29;
    padding: 12px 16px;
    border-bottom: 1px solid rgba(98, 114, 164, 0.2);
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .header-left {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 8px;
      
      .header-main {
        display: flex;
        align-items: center;
        gap: 12px;
        
        .status-tag {
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 4px 12px;
          border-radius: 8px;
          font-weight: 500;
          font-size: 0.9rem;
          border: none;
          text-shadow: 0 0 8px rgba(189, 147, 249, 0.5);
          
          &.success {
            background: rgba(189, 147, 249, 0.2);
            color: #bd93f9;
          }
          
          &.error {
            background: rgba(255, 85, 85, 0.2);
            color: #ff5555;
          }
          
          .status-icon {
            margin-right: 4px;
          }
        }
      }
      
      .header-sub {
        font-size: 0.9rem;
        opacity: 0.9;
      }
    }
    
    .close-button {
      background: none;
      border: none;
      color: #6272a4;
      cursor: pointer;
      padding: 4px;
      transition: all 0.3s ease;
      font-size: 16px;
      
      &:hover {
        color: #ff79c6;
        transform: rotate(90deg);
      }
    }
  }
  
  .debug-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    min-height: 0;
    
    .debug-logs {
      flex: 0 0 auto;
      border-bottom: 1px solid rgba(98, 114, 164, 0.2);
      
      .logs-wrapper {
        background-color: #1e1f29;
        
        &.expanded {
          .logs-detail {
            max-height: 300px;
          }
        }
        
        .logs-header {
          display: flex;
          align-items: center;
          padding: 8px 16px;
          background-color: rgba(40, 42, 54, 0.6);
          gap: 12px;
          height: 40px;
          
          .logs-title {
            flex: 0 0 auto;
            display: flex;
            align-items: center;
            gap: 8px;
            
            .realtime-tag {
              background: #50fa7b;
              color: #1a1a2e;
              padding: 2px 8px;
              border-radius: 4px;
              font-size: 0.7rem;
              font-weight: 700;
            }
          }
          
          .logs-content {
            flex: 1;
            overflow: hidden;
            margin: 0 12px;
            
            .log-content {
              display: flex;
              gap: 8px;
              white-space: nowrap;
              font-size: 12px;
              line-height: 1.4;
              font-family: 'Fira Code', monospace;
              
              .message {
                color: #f8f8f2;
                overflow: hidden;
                text-overflow: ellipsis;
              }
            }
          }
          
          .expand-button {
            flex: 0 0 auto;
            background: none;
            border: none;
            color: #6272a4;
            cursor: pointer;
            padding: 4px 8px;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 4px;
            
            &:hover {
              color: #ff79c6;
              background-color: rgba(255, 121, 198, 0.1);
            }
          }
        }
        
        .logs-detail {
          max-height: 0;
          overflow-y: auto;
          transition: max-height 0.3s ease;
          background-color: #282a36;
          border-top: 1px solid rgba(98, 114, 164, 0.2);
          
          .log-content {
            padding: 6px 16px;
            display: flex;
            gap: 8px;
            white-space: nowrap;
            font-size: 12px;
            line-height: 1.4;
            font-family: 'Fira Code', monospace;
            
            &:hover {
              background-color: rgba(40, 42, 54, 0.6);
            }
            
            .message {
              color: #f8f8f2;
            }
          }
        }
      }
      
      .empty-logs {
        height: 40px;
        padding: 8px 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #1e1f29;
        font-size: 12px;
      }
    }
    
    .debug-results {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;
      min-height: 0;
      
      .loading-placeholder {
        padding: 24px;
        text-align: center;
        color: #6272a4;
        background: rgba(40, 42, 54, 0.6);
        border-radius: 8px;
        border: 1px solid rgba(98, 114, 164, 0.2);
        font-size: 14px;
        margin: 16px;
      }
    }
  }
  
  .debug-info, .debug-error {
    padding: 24px;
    text-align: center;
    border-radius: 8px;
    border: 1px solid rgba(98, 114, 164, 0.2);
    font-size: 14px;
    margin: 16px;
  }
  
  .debug-info {
    color: #50fa7b;
    background: rgba(80, 250, 123, 0.1);
  }
  
  .debug-error {
    color: #ff5555;
    background: rgba(255, 85, 85, 0.1);
  }
  
  // 自定义滚动条样式
  ::-webkit-scrollbar {
    width: 4px;
    height: 4px;
  }
  
  ::-webkit-scrollbar-track {
    background: #1e1f29;
  }
  
  ::-webkit-scrollbar-thumb {
    background: #6272a4;
    border-radius: 2px;
    
    &:hover {
      background: #ff79c6;
    }
  }
}


