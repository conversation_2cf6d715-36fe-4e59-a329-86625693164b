.strategy-market {
  padding: 24px;

  .market-header {
    margin-bottom: 24px;

    .search-bar {
      display: flex;
      align-items: center;
      gap: 16px;
      margin-bottom: 16px;

      .ant-input-affix-wrapper {
        flex: 1;
      }

      .filter-actions {
        display: flex;
        gap: 8px;
      }
    }

    .tag-cloud {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
    }
  }

  .market-content {
    .ant-row {
      margin-bottom: 16px;
    }
  }

  .empty-state {
    margin-top: 48px;
  }
}

// 动画效果
.strategy-card {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 参数配置动画
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.param-config-wrapper {
  animation: slideDown 0.3s ease-out;
}

// 添加 Tooltip 样式
:global(.ant-tooltip) {
  .ant-tooltip-inner {
    background-color: rgba(0, 0, 0, 0.75);
    backdrop-filter: blur(4px);
    padding: 6px 12px;
    font-size: 12px;
    border-radius: 4px;
  }
} 