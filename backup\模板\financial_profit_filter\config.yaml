data_sources:
  backtest:
    cache:
      enabled: true
      ttl: 120
    database:
      table: stock_financial_report_quick_em
      type: postgresql
    fields:
    - 股票代码
    - 股票简称
    - 营业总收入_营业总收入
    - 营业总收入_同比增长
    - 净利润_净利润
    - 净利润_同比增长
    - 销售毛利率
  filter:
    cache:
      enabled: true
      ttl: 120
    database:
      table: stock_financial_report_quick_em
      type: postgresql
    fields:
    - 股票代码
    - 股票简称
    - 营业总收入_营业总收入
    - 营业总收入_同比增长
    - 净利润_净利润
    - 净利润_同比增长
    - 销售毛利率
  monitor: &id001
    cache:
      enabled: true
      ttl: 60
    database:
      table: stock_financial_report_quick_em
      type: postgresql
    fields:
    - 股票代码
    - 股票简称
    - 营业总收入_营业总收入
    - 营业总收入_同比增长
    - 净利润_净利润
    - 净利润_同比增长
    - 销售毛利率
  timing: *id001
execution:
  max_symbols: 3000
  timeout: 60
logging:
  format: '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
  level: info 