import { request } from './api';

// 缓存对象
const cache = {
  boardData: {
    industry: null as any,
    concept: null as any,
    timestamp: 0,
    ttl: 5 * 60 * 1000, // 5分钟缓存
  }
};

/**
 * 板块数据接口
 */
export interface BoardData {
  name: string;
  code: string;
  price: number;
  change: number;
  change_percent: number;
  turnover_rate: number;
  amount: number;
  volume: number;
  rise_count: number;
  fall_count: number;
  leading_stock: string;
  leading_stock_change: number;
  market_cap: number;
  price_strength?: number;
}

/**
 * 获取所有板块最新数据
 * 
 * @param boardType 板块类型：industry(行业)或concept(概念)
 * @returns 板块数据列表
 */
export async function getAllBoardsLatest(boardType: 'industry' | 'concept' = 'industry'): Promise<BoardData[]> {
  try {
    const now = Date.now();
    // 检查缓存是否有效
    if (
      cache.boardData[boardType] && 
      now - cache.boardData.timestamp < cache.boardData.ttl
    ) {
      console.log(`使用缓存的${boardType}板块数据`);
      return cache.boardData[boardType];
    }

    // 调用后端接口获取板块数据
    const response = await request({
      url: `/market-data/boards/${boardType}`,
      method: 'GET',
    });
    
    if (response && Array.isArray(response)) {
      // 处理数据，计算量价强度
      const processedData = response.map((item: BoardData) => {
        // 处理可能的无效数值
        const change_percent = Number(item.change_percent);
        const turnover_rate = Number(item.turnover_rate);
        const amount = Number(item.amount);
        const volume = Number(item.volume);
        const market_cap = Number(item.market_cap);
        const price = Number(item.price);
        const change = Number(item.change);
        
        return {
          ...item,
          name: item.name || '',
          code: item.code || '',
          price: isNaN(price) ? 0 : price,
          change: isNaN(change) ? 0 : change,
          change_percent: isNaN(change_percent) ? 0 : change_percent,
          turnover_rate: isNaN(turnover_rate) ? 0 : turnover_rate,
          amount: isNaN(amount) ? 0 : amount,
          volume: isNaN(volume) ? 0 : volume,
          market_cap: isNaN(market_cap) ? 0 : market_cap,
          rise_count: Number(item.rise_count) || 0,
          fall_count: Number(item.fall_count) || 0,
          leading_stock: item.leading_stock || '',
          leading_stock_change: Number(item.leading_stock_change) || 0,
          // 计算量价强度 (涨跌幅 * 换手率)
          price_strength: isNaN(change_percent) || isNaN(turnover_rate) ? 0 : change_percent * turnover_rate
        };
      });
      
      console.log(`处理后的${boardType}板块数据:`, processedData); // 添加调试日志
      
      // 更新缓存
      cache.boardData[boardType] = processedData;
      cache.boardData.timestamp = now;
      
      return processedData;
    }
    
    throw new Error(`获取${boardType}板块数据失败`);
  } catch (error) {
    console.error(`获取${boardType}板块数据失败:`, error);
    // 如果有缓存，在出错时返回缓存数据
    if (cache.boardData[boardType]) {
      console.log(`使用缓存的${boardType}板块数据（出错回退）`);
      return cache.boardData[boardType];
    }
    throw error;
  }
}

/**
 * 获取K线数据
 * 
 * @param symbol 股票代码
 * @param period 周期：1m, 5m, 15m, 30m, 60m, 1d, 1w, 1M
 * @param count 获取条数
 * @returns K线数据
 */
export async function getKlineData(symbol: string, period: string = '1d', count: number = 100) {
  try {
    const response = await request({
      url: '/market/kline',
      method: 'GET',
      params: {
        symbol,
        period,
        count
      }
    });
    
    if (response && response.data) {
      return response.data;
    }
    
    throw new Error(response.message || '获取K线数据失败');
  } catch (error) {
    console.error('获取K线数据失败:', error);
    throw error;
  }
}

/**
 * 获取市场情绪数据
 * 
 * @returns 市场情绪数据
 */
export async function getMarketSentiment() {
  try {
    const response = await request({
      url: '/market/sentiment',
      method: 'GET'
    });
    
    if (response && response.data) {
      return response.data;
    }
    
    throw new Error(response.message || '获取市场情绪数据失败');
  } catch (error) {
    console.error('获取市场情绪数据失败:', error);
    throw error;
  }
}

export default {
  getAllBoardsLatest,
  getKlineData,
  getMarketSentiment
};
