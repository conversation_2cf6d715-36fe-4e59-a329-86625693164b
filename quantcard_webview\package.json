{"name": "quantcard_webview", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "electron": "electron .", "electron-dev": "concurrently \"npm run dev\" \"wait-on http://localhost:5173 && electron .\"", "build-electron": "npm run build && electron-builder", "dist": "npm run build && electron-builder --publish=never"}, "dependencies": {"@react-three/drei": "^10.6.1", "@react-three/fiber": "^9.3.0", "@types/styled-components": "^5.1.34", "@types/three": "^0.179.0", "@use-gesture/react": "^10.3.1", "camera-controls": "^3.1.0", "echarts": "^5.6.0", "framer-motion": "^12.23.12", "lottie-react": "^2.4.1", "react": "^19.1.1", "react-dom": "^19.1.1", "react-router-dom": "^7.8.0", "react-window": "^1.8.8", "recharts": "^3.1.2", "styled-components": "^6.1.19", "three": "^0.179.1", "zustand": "^5.0.7"}, "devDependencies": {"@eslint/js": "^9.32.0", "@types/react": "^19.1.9", "@types/react-dom": "^19.1.7", "@vitejs/plugin-react": "^4.7.0", "eslint": "^9.32.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "sass-embedded": "^1.90.0", "typescript": "~5.8.3", "typescript-eslint": "^8.39.0", "vite": "^7.1.0"}}