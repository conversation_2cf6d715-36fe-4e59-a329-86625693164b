"""
股东户数增减比例筛选策略
"""
import logging
import pandas as pd
from typing import Dict, Any, List, Optional
from datetime import datetime
import yaml
import json
from pathlib import Path

from app.core.strategy.base import UnifiedStrategyCard
from app.core.runtime.types import RuntimeContext, Signal, SignalType
from app.core.runtime.types import StrategyMode
from app.core.data.db.base import db_manager
from sqlalchemy import text

logger = logging.getLogger(__name__)

# 从 template.json 加载配置
template_path = Path(__file__).parent / "template.json"
with open(template_path, "r", encoding="utf-8") as f:
    template = json.load(f)

class ShareholderCountFilterStrategy(UnifiedStrategyCard):
    """股东户数增减比例筛选策略"""
    
    def __init__(self, context: Optional[RuntimeContext] = None):
        """初始化策略"""
        super().__init__(context)
        
        # 加载配置文件
        config_path = Path(__file__).parent / "config.yaml"
        try:
            with open(config_path, "r", encoding="utf-8") as f:
                self.config = yaml.safe_load(f)
                
            # 确保配置加载成功且有必要的字段
            if not self.config or "data_sources" not in self.config:
                raise ValueError("配置文件格式错误或data_sources字段缺失")
                
            self._log(f"成功加载配置文件: {config_path}")
                
        except Exception as e:
            self._log(f"加载配置文件失败: {str(e)}", "error")
            # 使用默认配置
            self.config = {
                "data_sources": {
                    "filter": {
                        "database": {
                            "type": "postgresql",
                            "table": "stock_shareholder_count_data"
                        },
                        "fields": ["代码", "名称", "股东户数增减比例"]
                    }
                }
            }

    async def prepare_data(self, context: RuntimeContext) -> pd.DataFrame:
        """准备数据"""
        try:
            # 检查串行执行模式
            if not self._check_sequential_mode(context):
                return pd.DataFrame()
            
            # 获取模式配置 - 直接使用执行模式作为数据源配置
            mode = context.mode.value if context.mode else "filter"
            self._log(f"当前执行模式: {mode}")
            
            if "data_sources" not in self.config:
                self._log("配置文件中未找到data_sources字段", "error")
                return pd.DataFrame()
                
            if mode not in self.config["data_sources"]:
                self._log(f"配置文件中未找到{mode}模式配置，将使用filter模式", "warning")
                mode = "filter"
                
            mode_config = self.config["data_sources"].get(mode, self.config["data_sources"]["filter"])
            self._log(f"数据源配置: {json.dumps(mode_config, ensure_ascii=False)}")
            
            # 获取股票代码列表
            symbols = self._get_stock_symbols(context)
            if symbols:
                self._log(f"处理股票列表: {symbols[:5]}等 {len(symbols)}只")
            else:
                self._log("未指定股票代码，将返回所有数据")
            
            # 检查缓存
            cache_key = f"{self._get_cache_key(context)}_{mode}"
            cached_data = self._data_cache.get(cache_key)
            if cached_data is not None:
                if symbols and not cached_data.empty:
                    filtered_cache = cached_data[cached_data["代码"].isin(symbols)]
                    self._log(f"从缓存获取: {len(filtered_cache)}只股票")
                    return filtered_cache
                return cached_data
            
            # 获取数据
            if "database" not in mode_config:
                self._log("配置文件中未找到database配置", "error")
                return pd.DataFrame()
                
            db_type = mode_config["database"].get("type")
            if not db_type:
                self._log("数据库类型未指定", "error")
                return pd.DataFrame()
                
            if db_type == "postgresql":
                self._log("开始从PostgreSQL获取数据")
                data = await self._get_data_from_postgresql(mode_config, symbols)
                self._log(f"获取到{len(data)}条数据记录")
            else:
                self._log(f"不支持的数据库类型: {db_type}", "warning")
                return pd.DataFrame()
            
            if data.empty:
                self._log("未获取到任何数据", "warning")
                return pd.DataFrame()
            
            # 输出数据列名，以便调试
            self._log(f"数据列名: {list(data.columns)}")
            
            # 缓存数据
            self._data_cache.set(cache_key, data, ttl=60)
            return data
            
        except Exception as e:
            self._log(f"准备数据失败: {str(e)}", "error")
            import traceback
            self._log(f"错误详情: {traceback.format_exc()}", "error")
            return pd.DataFrame()
    
    async def _get_data_from_postgresql(self, mode_config: Dict[str, Any], symbols: List[str]) -> pd.DataFrame:
        """从PostgreSQL获取数据"""
        try:
            if "database" not in mode_config or "table" not in mode_config["database"]:
                raise ValueError("配置错误：数据源未定义表名")
            
            table = mode_config["database"]["table"]
            fields = mode_config.get("fields", ["代码", "名称", "股东户数增减比例"])
            
            fields_str = ", ".join([f'"{field}"' for field in fields])
            sql_query = f'SELECT {fields_str} FROM "{table}"'
            
            if symbols:
                placeholders = ", ".join([f"'{symbol}'" for symbol in symbols])
                sql_query += f' WHERE "代码" IN ({placeholders})'
            
            self._log(f"执行SQL查询: {sql_query}")
            
            async with db_manager.get_session() as session:
                result = await session.execute(text(sql_query))
                rows = result.fetchall()
                if not rows:
                    self._log("查询结果为空")
                    return pd.DataFrame()
                    
                self._log(f"获取到{len(rows)}条数据记录")
                
                # 创建DataFrame
                df = pd.DataFrame(rows, columns=fields)
                return df
                
        except Exception as e:
            self._log(f"从PostgreSQL获取数据失败: {str(e)}", "error")
            import traceback
            self._log(f"错误详情: {traceback.format_exc()}", "error")
            return pd.DataFrame()
            
    async def generate_signal(self, data: pd.DataFrame, params: Dict[str, Any]) -> List[Signal]:
        """生成信号"""
        try:
            if data.empty:
                self._log("数据为空，无法生成信号", "warning")
                return []
                
            # 检查必要的列是否存在
            if "股东户数增减比例" not in data.columns:
                self._log(f"数据中不存在'股东户数增减比例'列，可用列: {list(data.columns)}", "error")
                return []
            
            # 获取参数
            operator = params.get("operator", "大于")
            percentage = params.get("percentage", 5)
            
            self._log(f"股东户数增减比例筛选条件: {operator} {percentage}%")
            
            # 确保股东户数增减比例列的数据类型为数值型
            data["股东户数增减比例"] = pd.to_numeric(data["股东户数增减比例"], errors="coerce")
            
            # 处理NaN值
            nan_count = data["股东户数增减比例"].isna().sum()
            if nan_count > 0:
                self._log(f"警告: 存在{nan_count}条数据的股东户数增减比例为NaN值", "warning")
                data = data.dropna(subset=["股东户数增减比例"])
            
            # 根据条件筛选
            if operator == "大于":
                filtered_data = data[data["股东户数增减比例"] > percentage]
            elif operator == "小于":
                filtered_data = data[data["股东户数增减比例"] < percentage]
            else:
                self._log(f"不支持的操作符: {operator}", "warning")
                return []
            
            self._log(f"筛选出 {len(filtered_data)}只股票")
            
            # 使用统一的create_signal方法生成信号
            signals = [
                self.create_signal(
                    symbol=row["代码"],
                    name=row["名称"] if "名称" in row else "",
                    direction="BUY" if operator == "大于" else "SELL",
                    signal_type="fundamental",
                    confidence=0.75,
                    trigger_condition=f"股东户数增减比例{operator}_{percentage}%",
                    shareholder_count_change_percentage=float(row["股东户数增减比例"])
                )
                for _, row in filtered_data.iterrows()
            ]
            
            return signals
            
        except Exception as e:
            self._log(f"生成信号失败: {str(e)}", "error")
            import traceback
            self._log(f"错误详情: {traceback.format_exc()}", "error")
            return [] 