"""
主力资金追踪策略
基于大单资金流向、成交量价关系和主力行为模式分析的智能资金追踪策略
"""
import logging
import pandas as pd
import numpy as np
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import yaml
import json
from pathlib import Path
import talib as ta

from app.core.strategy.base import UnifiedStrategyCard
from app.core.runtime.types import RuntimeContext, Signal, SignalType, StrategyMode
from app.services.minute_kline_service import MinuteKlineService

logger = logging.getLogger(__name__)

class SmartMoneyTrackerStrategy(UnifiedStrategyCard):
    """主力资金追踪策略
    
    该策略通过以下维度追踪主力资金：
    1. 大单净流入/流出分析
    2. 价量配合度分析
    3. 主力建仓/减仓模式识别
    4. 资金流入强度和持续性评估
    5. 异常交易量检测
    6. 主力拉升/打压信号识别
    """
    
    def __init__(self, context: Optional[RuntimeContext] = None):
        """初始化策略"""
        super().__init__(context)
        
        # 加载配置文件
        config_path = Path(__file__).parent / "config.yaml"
        with open(config_path, "r", encoding="utf-8") as f:
            self.config = yaml.safe_load(f)
        
        # 初始化分钟K线服务
        self.kline_service = MinuteKlineService.get_instance()

    async def prepare_data(self, context: RuntimeContext) -> Dict[str, pd.DataFrame]:
        """准备数据"""
        try:
            # 检查串行执行模式
            if not self._check_sequential_mode(context):
                return {}
            
            # 从context.data_cache获取K线数据
            kline_data = context.data_cache.get("kline_data", {})
            if not kline_data:
                self._log("未能从上下文中获取K线数据", "warning")
                return {}
            
            # 处理每只股票的数据
            processed_data = {}
            for symbol, df in kline_data.items():
                try:
                    processed_df = await self._process_kline_data(df, context.parameters)
                    if not processed_df.empty:
                        processed_data[symbol] = processed_df
                        self._log(f"成功处理股票 {symbol} 的资金数据，共{len(processed_df)}条记录")
                    else:
                        self._log(f"股票 {symbol} 数据处理后为空", "warning")
                except Exception as e:
                    self._log(f"处理股票 {symbol} 数据失败: {str(e)}", "error")
            
            return processed_data
            
        except Exception as e:
            self._log(f"准备数据失败: {str(e)}", "error")
            return {}
    
    async def _process_kline_data(self, df: pd.DataFrame, params: Dict[str, Any]) -> pd.DataFrame:
        """处理K线数据，计算资金流向相关指标"""
        try:
            if df.empty:
                return pd.DataFrame()
            
            # 确保数据按时间排序
            df = df.sort_values('time').copy()
            
            # 计算基础指标
            df = self._calculate_basic_indicators(df)
            
            # 模拟大单资金流向（实际项目中应该有真实的大单数据）
            df = self._simulate_smart_money_flow(df, params)
            
            # 计算资金流向指标
            df = self._calculate_money_flow_indicators(df, params)
            
            # 识别主力行为模式
            df = self._identify_smart_money_patterns(df, params)
            
            # 生成资金信号
            df = self._generate_money_signals(df, params)
            
            return df
            
        except Exception as e:
            self._log(f"处理K线数据失败: {str(e)}", "error")
            return pd.DataFrame()
    
    def _calculate_basic_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算基础指标"""
        try:
            # 价格变化
            df['price_change'] = df['close'].pct_change()
            df['price_change_abs'] = df['price_change'].abs()
            
            # 成交量指标
            df['volume_ma'] = df['volume'].rolling(window=20).mean()
            df['volume_ratio'] = df['volume'] / df['volume_ma']
            df['volume_std'] = df['volume'].rolling(window=20).std()
            
            # 成交额
            df['turnover'] = df['close'] * df['volume']
            df['turnover_ma'] = df['turnover'].rolling(window=20).mean()
            df['turnover_ratio'] = df['turnover'] / df['turnover_ma']
            
            # VWAP (成交量加权平均价)
            df['vwap'] = (df['turnover'].cumsum()) / (df['volume'].cumsum())
            df['vwap_deviation'] = (df['close'] - df['vwap']) / df['vwap']
            
            return df
            
        except Exception as e:
            self._log(f"计算基础指标失败: {str(e)}", "error")
            return df
    
    def _simulate_smart_money_flow(self, df: pd.DataFrame, params: Dict[str, Any]) -> pd.DataFrame:
        """模拟大单资金流向
        注意：这是模拟数据，实际应用中应该使用真实的大单数据
        """
        try:
            # 基于价量关系模拟大单流向
            large_order_threshold = float(params.get("large_order_threshold", 2.0))
            
            # 识别大单交易（成交量显著高于平均水平）
            df['is_large_volume'] = df['volume_ratio'] > large_order_threshold
            
            # 模拟大单净流入
            # 上涨+大成交量 = 大单流入，下跌+大成交量 = 大单流出
            conditions = [
                (df['price_change'] > 0) & (df['is_large_volume']),  # 流入
                (df['price_change'] < 0) & (df['is_large_volume']),  # 流出
            ]
            choices = [1, -1]
            df['large_order_direction'] = np.select(conditions, choices, default=0)
            
            # 大单金额（模拟）
            df['large_order_amount'] = np.where(
                df['is_large_volume'],
                df['turnover'] * df['volume_ratio'] * 0.3,  # 假设大单占30%
                0
            )
            
            # 大单净流入金额
            df['net_large_inflow'] = df['large_order_amount'] * df['large_order_direction']
            
            return df
            
        except Exception as e:
            self._log(f"模拟大单流向失败: {str(e)}", "error")
            return df
    
    def _calculate_money_flow_indicators(self, df: pd.DataFrame, params: Dict[str, Any]) -> pd.DataFrame:
        """计算资金流向指标"""
        try:
            flow_window = int(params.get("flow_window", 10))
            
            # 累积资金流向
            df['cumulative_flow'] = df['net_large_inflow'].cumsum()
            df['flow_ma'] = df['net_large_inflow'].rolling(window=flow_window).mean()
            
            # 资金流向强度
            df['flow_strength'] = df['net_large_inflow'] / df['turnover_ma']
            df['flow_strength_ma'] = df['flow_strength'].rolling(window=flow_window).mean()
            
            # 主力控盘度（模拟）
            total_turnover = df['turnover'].rolling(window=flow_window).sum()
            large_order_turnover = df['large_order_amount'].rolling(window=flow_window).sum()
            df['control_ratio'] = large_order_turnover / total_turnover
            
            # 资金流入持续性
            df['flow_persistence'] = self._calculate_flow_persistence(df['net_large_inflow'], flow_window)
            
            # 异常资金流入检测
            flow_mean = df['net_large_inflow'].rolling(window=20).mean()
            flow_std = df['net_large_inflow'].rolling(window=20).std()
            df['flow_zscore'] = (df['net_large_inflow'] - flow_mean) / flow_std
            df['abnormal_inflow'] = df['flow_zscore'].abs() > 2
            
            return df
            
        except Exception as e:
            self._log(f"计算资金流向指标失败: {str(e)}", "error")
            return df
    
    def _calculate_flow_persistence(self, flow_series: pd.Series, window: int) -> pd.Series:
        """计算资金流向持续性"""
        try:
            def calc_persistence(x):
                if len(x) == 0:
                    return 0
                # 计算同向流动的比例
                positive = (x > 0).sum()
                negative = (x < 0).sum()
                total = len(x)
                return max(positive, negative) / total if total > 0 else 0
            
            return flow_series.rolling(window=window).apply(calc_persistence, raw=False)
            
        except Exception as e:
            self._log(f"计算流向持续性失败: {str(e)}", "error")
            return pd.Series([0] * len(flow_series))
    
    def _identify_smart_money_patterns(self, df: pd.DataFrame, params: Dict[str, Any]) -> pd.DataFrame:
        """识别主力行为模式"""
        try:
            # 获取参数
            accumulation_threshold = float(params.get("accumulation_threshold", 0.6))
            distribution_threshold = float(params.get("distribution_threshold", 0.6))
            
            # 主力建仓模式：持续小幅流入 + 价格稳定
            building_condition = (
                (df['flow_persistence'] > accumulation_threshold) &
                (df['flow_ma'] > 0) &
                (df['price_change_abs'] < df['price_change_abs'].rolling(20).mean())
            )
            df['smart_money_building'] = building_condition
            
            # 主力减仓模式：持续流出 + 价格下跌
            distributing_condition = (
                (df['flow_persistence'] > distribution_threshold) &
                (df['flow_ma'] < 0) &
                (df['price_change'] < 0)
            )
            df['smart_money_distributing'] = distributing_condition
            
            # 主力拉升模式：大量流入 + 价格快速上涨
            pulling_condition = (
                (df['net_large_inflow'] > df['net_large_inflow'].rolling(20).quantile(0.8)) &
                (df['price_change'] > df['price_change'].rolling(20).quantile(0.8)) &
                (df['volume_ratio'] > 1.5)
            )
            df['smart_money_pulling'] = pulling_condition
            
            # 主力打压模式：大量流出 + 价格快速下跌
            suppressing_condition = (
                (df['net_large_inflow'] < df['net_large_inflow'].rolling(20).quantile(0.2)) &
                (df['price_change'] < df['price_change'].rolling(20).quantile(0.2)) &
                (df['volume_ratio'] > 1.5)
            )
            df['smart_money_suppressing'] = suppressing_condition
            
            # 主力洗盘模式：震荡 + 净流入为正
            washing_condition = (
                (df['price_change_abs'] > df['price_change_abs'].rolling(20).mean()) &
                (df['flow_ma'] > 0) &
                (~df['smart_money_pulling'])
            )
            df['smart_money_washing'] = washing_condition
            
            return df
            
        except Exception as e:
            self._log(f"识别主力模式失败: {str(e)}", "error")
            return df
    
    def _generate_money_signals(self, df: pd.DataFrame, params: Dict[str, Any]) -> pd.DataFrame:
        """生成资金信号"""
        try:
            # 获取参数
            min_flow_strength = float(params.get("min_flow_strength", 0.1))
            min_control_ratio = float(params.get("min_control_ratio", 0.2))
            
            # 初始化信号列
            df['signal'] = 'HOLD'
            df['signal_strength'] = 0.0
            df['signal_reason'] = 'no_signal'
            
            for i in range(10, len(df)):  # 需要足够的历史数据
                current = df.iloc[i]
                
                # 强买入信号：主力建仓完成后开始拉升
                if (current['smart_money_pulling'] and 
                    df.iloc[i-5:i]['smart_money_building'].any() and
                    current['flow_strength'] > min_flow_strength and
                    current['control_ratio'] > min_control_ratio):
                    
                    signal_strength = min(0.9, 0.6 + current['flow_strength'] * 2)
                    df.iloc[i, df.columns.get_loc('signal')] = 'BUY'
                    df.iloc[i, df.columns.get_loc('signal_strength')] = signal_strength
                    df.iloc[i, df.columns.get_loc('signal_reason')] = 'building_to_pulling'
                
                # 买入信号：主力持续建仓
                elif (current['smart_money_building'] and 
                      current['flow_strength'] > min_flow_strength * 0.5 and
                      current['control_ratio'] > min_control_ratio * 0.8):
                    
                    signal_strength = min(0.7, 0.4 + current['flow_persistence'] * 0.3)
                    df.iloc[i, df.columns.get_loc('signal')] = 'BUY'
                    df.iloc[i, df.columns.get_loc('signal_strength')] = signal_strength
                    df.iloc[i, df.columns.get_loc('signal_reason')] = 'smart_building'
                
                # 卖出信号：主力开始减仓
                elif (current['smart_money_distributing'] and 
                      current['flow_strength'] < -min_flow_strength * 0.5):
                    
                    signal_strength = min(0.8, 0.5 + abs(current['flow_strength']) * 2)
                    df.iloc[i, df.columns.get_loc('signal')] = 'SELL'
                    df.iloc[i, df.columns.get_loc('signal_strength')] = signal_strength
                    df.iloc[i, df.columns.get_loc('signal_reason')] = 'smart_distributing'
                
                # 强卖出信号：主力打压
                elif (current['smart_money_suppressing'] and
                      current['flow_strength'] < -min_flow_strength):
                    
                    signal_strength = min(0.9, 0.7 + abs(current['flow_strength']) * 2)
                    df.iloc[i, df.columns.get_loc('signal')] = 'SELL'
                    df.iloc[i, df.columns.get_loc('signal_strength')] = signal_strength
                    df.iloc[i, df.columns.get_loc('signal_reason')] = 'smart_suppressing'
                
                # 观望信号：主力洗盘
                elif current['smart_money_washing']:
                    df.iloc[i, df.columns.get_loc('signal')] = 'HOLD'
                    df.iloc[i, df.columns.get_loc('signal_strength')] = 0.3
                    df.iloc[i, df.columns.get_loc('signal_reason')] = 'smart_washing'
                
                # 异常流入关注
                elif current['abnormal_inflow'] and current['net_large_inflow'] > 0:
                    df.iloc[i, df.columns.get_loc('signal')] = 'BUY'
                    df.iloc[i, df.columns.get_loc('signal_strength')] = 0.5
                    df.iloc[i, df.columns.get_loc('signal_reason')] = 'abnormal_inflow'
            
            return df
            
        except Exception as e:
            self._log(f"生成资金信号失败: {str(e)}", "error")
            return df

    async def generate_signal(self, data: Dict[str, pd.DataFrame], params: Dict[str, Any]) -> List[Signal]:
        """生成信号"""
        try:
            if not data:
                return []
            
            signals = []
            
            for symbol, df in data.items():
                if df.empty:
                    continue
                
                # 获取最新的资金信号
                recent_signals = df[df['signal'] != 'HOLD'].tail(3)
                
                if recent_signals.empty:
                    # 生成市场状态信号
                    latest = df.iloc[-1]
                    
                    # 判断当前资金状态
                    if latest['net_large_inflow'] > 0:
                        if latest['smart_money_building']:
                            market_state = "主力建仓中"
                            confidence = 0.4
                        elif latest['smart_money_washing']:
                            market_state = "主力洗盘中"
                            confidence = 0.3
                        else:
                            market_state = "资金净流入"
                            confidence = 0.5
                    elif latest['net_large_inflow'] < 0:
                        if latest['smart_money_distributing']:
                            market_state = "主力减仓中"
                            confidence = 0.6
                        else:
                            market_state = "资金净流出"
                            confidence = 0.5
                    else:
                        market_state = "资金流向中性"
                        confidence = 0.3
                    
                    signals.append(
                        self.create_signal(
                            symbol=symbol,
                            name=symbol,
                            direction="HOLD",
                            signal_type="money_flow",
                            confidence=confidence,
                            trigger_condition=market_state,
                            current_price=float(latest['close']),
                            net_inflow=float(latest['net_large_inflow']),
                            flow_strength=float(latest['flow_strength']) if pd.notna(latest['flow_strength']) else 0,
                            control_ratio=float(latest['control_ratio']) if pd.notna(latest['control_ratio']) else 0,
                            flow_persistence=float(latest['flow_persistence']) if pd.notna(latest['flow_persistence']) else 0
                        )
                    )
                else:
                    # 处理交易信号
                    for _, row in recent_signals.iterrows():
                        direction = row['signal']
                        confidence = row['signal_strength']
                        signal_reason = row['signal_reason']
                        
                        # 构建触发条件描述
                        reason_map = {
                            'building_to_pulling': '主力建仓转拉升',
                            'smart_building': '主力持续建仓',
                            'smart_distributing': '主力开始减仓',
                            'smart_suppressing': '主力大幅打压',
                            'smart_washing': '主力洗盘震荡',
                            'abnormal_inflow': '异常大单流入'
                        }
                        condition = reason_map.get(signal_reason, '资金流向信号')
                        
                        signal = self.create_signal(
                            symbol=symbol,
                            name=symbol,
                            direction=direction,
                            signal_type="money_flow",
                            confidence=confidence,
                            trigger_condition=condition,
                            current_price=float(row['close']),
                            net_inflow=float(row['net_large_inflow']),
                            flow_strength=float(row['flow_strength']) if pd.notna(row['flow_strength']) else 0,
                            control_ratio=float(row['control_ratio']) if pd.notna(row['control_ratio']) else 0,
                            flow_persistence=float(row['flow_persistence']) if pd.notna(row['flow_persistence']) else 0,
                            volume_ratio=float(row['volume_ratio']) if pd.notna(row['volume_ratio']) else 0,
                            signal_reason=signal_reason,
                            smart_building=bool(row.get('smart_money_building', False)),
                            smart_distributing=bool(row.get('smart_money_distributing', False)),
                            smart_pulling=bool(row.get('smart_money_pulling', False)),
                            smart_suppressing=bool(row.get('smart_money_suppressing', False))
                        )
                        signals.append(signal)
            
            self._log(f"主力资金追踪策略生成{len(signals)}个信号")
            return signals
            
        except Exception as e:
            self._log(f"生成信号失败: {str(e)}", "error")
            return []

    async def execute(self, context: RuntimeContext) -> List[Signal]:
        """执行策略"""
        try:
            self._log("开始执行主力资金追踪策略")
            
            # 获取参数信息
            large_order_threshold = context.parameters.get("large_order_threshold", 2.0)
            min_flow_strength = context.parameters.get("min_flow_strength", 0.1)
            flow_window = context.parameters.get("flow_window", 10)
            
            self._log(f"策略参数: 大单阈值={large_order_threshold}倍, "
                     f"最小流向强度={min_flow_strength}, 分析窗口={flow_window}分钟")
            
            # 准备数据
            data = await self.prepare_data(context)
            
            if not data:
                self._log("未获取到有效数据", "warning")
                return []
            
            # 生成信号
            signals = await self.generate_signal(data, context.parameters)
            
            # 统计信号类型
            building_signals = len([s for s in signals if 'building' in s.metadata.get('signal_reason', '')])
            distributing_signals = len([s for s in signals if 'distributing' in s.metadata.get('signal_reason', '')])
            pulling_signals = len([s for s in signals if 'pulling' in s.metadata.get('signal_reason', '')])
            other_signals = len(signals) - building_signals - distributing_signals - pulling_signals
            
            self._log(f"策略执行完成: 建仓信号{building_signals}个, 减仓信号{distributing_signals}个, "
                     f"拉升信号{pulling_signals}个, 其他信号{other_signals}个")
            
            return signals
            
        except Exception as e:
            self._log(f"策略执行失败: {str(e)}", "error")
            return []