"""
游戏会话模型
"""
from datetime import datetime
from typing import Dict, Any, Optional, List
from pydantic import Field
from app.core.base_model import BaseDBModel

class GameSession(BaseDBModel):
    """游戏会话状态模型"""
    user_id: str = Field(..., description="用户ID")
    session_type: str = Field(..., description="会话类型: battle|adventure|strategy_creation")
    session_data: Dict[str, Any] = Field(default_factory=dict, description="会话数据")
    participants: List[str] = Field(default_factory=list, description="多人场景参与者")
    status: str = Field(default="active", description="会话状态: active|paused|completed|expired")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.utcnow, description="更新时间")
    expires_at: Optional[datetime] = Field(None, description="过期时间")

    class Config:
        arbitrary_types_allowed = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

    @classmethod
    def get_collection_name(cls) -> str:
        return "game_sessions"

    @classmethod
    async def get_active_session(cls, user_id: str, session_type: str = None) -> Optional["GameSession"]:
        """获取用户活跃会话"""
        filter_dict = {"user_id": user_id, "status": "active"}
        if session_type:
            filter_dict["session_type"] = session_type
            
        return await cls.find_one(filter_dict)

    async def update_session_data(self, data: Dict[str, Any]) -> "GameSession":
        """更新会话数据"""
        self.session_data.update(data)
        self.updated_at = datetime.utcnow()
        await self.save()
        return self

    async def expire_session(self) -> "GameSession":
        """使会话过期"""
        self.status = "expired"
        self.updated_at = datetime.utcnow()
        await self.save()
        return self 