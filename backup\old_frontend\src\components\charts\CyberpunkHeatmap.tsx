import React, { useState, useEffect, useMemo, useCallback, useRef } from 'react';
import { Radio, Select, Button, Space, Tooltip, Spin, Typography } from 'antd';
import { 
  FullscreenOutlined, 
  FullscreenExitOutlined, 
  ReloadOutlined,
  ZoomInOutlined,
  ZoomOutOutlined
} from '@ant-design/icons';
import Plot from 'react-plotly.js';
import { motion } from 'framer-motion';
import './CyberpunkHeatmap.scss';

const { Text } = Typography;
const { Option } = Select;

// 简化的板块数据接口
export interface BoardData {
  name: string;           // 板块名称
  code: string;          // 板块代码
  price: number;         // 最新价
  change: number;        // 涨跌额
  change_percent: number; // 涨跌幅
  turnover_rate: number; // 换手率
  market_cap: number;    // 总市值
  rise_count: number;    // 上涨家数
  fall_count: number;    // 下跌家数
  leading_stock: string; // 领涨股票
  leading_stock_change: number; // 领涨股票涨跌幅
  amount: number;        // 成交额
  prev_amount?: number;  // 前一日成交额
}

interface CyberpunkHeatmapProps {
  data: BoardData[];
  loading?: boolean;
  title?: string;
  showTitle?: boolean;
  height?: number;
  boardType?: 'industry' | 'concept';
  onBoardTypeChange?: (type: 'industry' | 'concept') => void;
  onRefresh?: () => void;
  style?: React.CSSProperties;
}

// 添加类型声明扩展
declare global {
  interface HTMLDivElement {
    _hasHoverListeners?: boolean;
    on?: (event: string, callback: (data: any) => void) => void;
  }
}

const CyberpunkHeatmap: React.FC<CyberpunkHeatmapProps> = ({
  data,
  loading = false,
  title = '板块热力图',
  showTitle = true,
  height = 500,
  boardType = 'industry',
  onBoardTypeChange,
  onRefresh,
  style,
}) => {
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [colorMetric, setColorMetric] = useState<string>('change_percent');
  const [sizeMetric, setSizeMetric] = useState<string>('market_cap');
  const [zoomLevel, setZoomLevel] = useState(1);
  const chartContainerRef = useRef<HTMLDivElement>(null);
  const chartContentRef = useRef<HTMLDivElement>(null);
  const plotRef = useRef<any>(null);

  // 处理全屏切换
  const handleFullscreen = useCallback(() => {
    const willBeFullscreen = !isFullscreen;
    setIsFullscreen(willBeFullscreen);
    setZoomLevel(1);
    
    if (willBeFullscreen && chartContainerRef.current?.requestFullscreen) {
        chartContainerRef.current.requestFullscreen().catch(err => {
          console.error(`全屏切换失败: ${err.message}`);
        });
    } else if (document.fullscreenElement && document.exitFullscreen) {
        document.exitFullscreen().catch(err => {
          console.error(`退出全屏失败: ${err.message}`);
        });
      }
  }, [isFullscreen]);

  // 处理缩放
  const handleZoomIn = useCallback(() => {
    setZoomLevel(prev => Math.min(prev + 0.1, 2));
  }, []);

  const handleZoomOut = useCallback(() => {
    setZoomLevel(prev => Math.max(prev - 0.1, 0.6));
  }, []);

  // 监听窗口尺寸变化和全屏状态变化
  useEffect(() => {
    const handleResize = () => {
      if (plotRef.current && plotRef.current.resizeHandler) {
        plotRef.current.resizeHandler();
      }
    };

    const resizeObserver = new ResizeObserver(handleResize);
    
    if (chartContentRef.current) {
      resizeObserver.observe(chartContentRef.current);
    }
    
    const handleFullscreenChange = () => {
      const isDocFullscreen = document.fullscreenElement !== null;
      if (isDocFullscreen !== isFullscreen) {
        setIsFullscreen(isDocFullscreen);
        setZoomLevel(1);
        
        // 全屏状态变化后强制更新图表大小
        setTimeout(handleResize, 100);
      }
    };
    
    document.addEventListener('fullscreenchange', handleFullscreenChange);
    window.addEventListener('resize', handleResize);
    
    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
      window.removeEventListener('resize', handleResize);
      resizeObserver.disconnect();
    };
  }, [isFullscreen]);

  // 计算专业指标
  const calculateMetrics = useCallback((item: BoardData) => {
    const price_strength = item.change_percent * item.turnover_rate;
    const rise_fall_ratio = item.rise_count / (item.fall_count || 1);
    const amount_change = item.prev_amount ? 
      ((item.amount - item.prev_amount) / item.prev_amount * 100) : 0;
    
    return { price_strength, rise_fall_ratio, amount_change };
  }, []);

  // 获取颜色方案
  const getColorScale = (metric: string) => {
    switch (metric) {
      case 'change_percent':
        return [
          [0, 'rgb(0, 255, 163)'],      // 下跌 - 绿色
          [0.5, 'rgb(248, 248, 242)'],  // 持平 - 白色
          [1, 'rgb(255, 51, 88)']       // 上涨 - 红色
        ];
      case 'price_strength':
        return [
          [0, 'rgb(139, 233, 253)'],    // 低 - 蓝色
          [0.5, 'rgb(189, 147, 249)'],  // 中 - 紫色
          [1, 'rgb(255, 121, 198)']     // 高 - 粉色
        ];
      default:
        return [
          [0, 'rgb(139, 233, 253)'],    // 低 - 蓝色
          [1, 'rgb(189, 147, 249)']     // 高 - 紫色
        ];
    }
  };

  // 获取颜色范围
  const getColorRange = (metric: string) => {
    switch (metric) {
      case 'change_percent':
        return { min: -5, mid: 0, max: 5 };
      case 'price_strength':
        return { min: -10, mid: 0, max: 10 };
      case 'rise_fall_ratio':
        return { min: 0, mid: 1, max: 3 };
      case 'amount_change':
        return { min: -20, mid: 0, max: 20 };
      default:
        return { min: 0, mid: 1, max: 2 };
    }
  };

  // 获取大小值
  const getSize = useCallback((item: BoardData) => {
    // 根据所选指标计算大小
    const metrics = calculateMetrics(item);
    switch (sizeMetric) {
      case 'market_cap':
        return Number(item.market_cap || 1) / 1e8; // 亿为单位
      case 'turnover_rate':
        return Number(item.turnover_rate || 0.1) * 10; // 放大换手率
      case 'amount':
        return Number(item.amount || 1) / 1e8; // 成交额
      case 'price_strength':
        return Math.abs(metrics.price_strength || 0.1) * 5; // 放大量价强度
      default:
        return Number(item.market_cap || 1) / 1e8; // 默认用市值
    }
  }, [sizeMetric, calculateMetrics]);

  // 获取提示文本
  const getTooltipText = useCallback((item: BoardData) => {
    const metrics = calculateMetrics(item);
    const changePrefix = item.change_percent >= 0 ? '+' : '';
    return `
      <b>${item.name}</b><br>
      涨跌幅: <span style="color:${item.change_percent >= 0 ? '#ff5555' : '#50fa7b'}">${changePrefix}${item.change_percent.toFixed(2)}%</span><br>
      换手率: ${item.turnover_rate?.toFixed(2)}%<br>
      量价强度: ${metrics.price_strength.toFixed(2)}<br>
      市值: ${(item.market_cap / 1e8)?.toFixed(2)}亿<br>
      涨/跌家数: <span style="color:#50fa7b">${item.rise_count}</span>/<span style="color:#ff5555">${item.fall_count}</span><br>
      领涨: ${item.leading_stock} <span style="color:${item.leading_stock_change >= 0 ? '#50fa7b' : '#ff5555'}">${item.leading_stock_change.toFixed(2)}%</span>
    `;
  }, [calculateMetrics]);

  // 获取区块显示文本 - 修复重复文字问题
  const getBlockText = useCallback((item: BoardData, index: number) => {
    // 获取市值来判断块的大小
    const marketCap = Number(item.market_cap || 1) / 1e8;
    const isLargeBlock = marketCap > 200; // 市值大于200亿的视为大块
    const isMediumBlock = marketCap > 100; // 市值大于100亿的视为中等块
    
    const changePrefix = item.change_percent >= 0 ? '+' : '';
    const changeText = `${changePrefix}${item.change_percent.toFixed(1)}%`;
    
    // 只返回额外信息，不包含板块名称
    if (isLargeBlock || isMediumBlock) {
      // 中大块显示涨跌幅
      return changeText;
    } else {
      // 小块不显示文字
      return '';
    }
  }, []);

  // 获取颜色值
  const getColor = useCallback((item: BoardData, metric: string) => {
      const metrics = calculateMetrics(item);
    switch (metric) {
        case 'change_percent':
          return Number(item.change_percent);
        case 'turnover_rate':
          return Number(item.turnover_rate || 0);
        case 'price_strength':
          return metrics.price_strength;
        case 'rise_fall_ratio':
          return metrics.rise_fall_ratio;
        default:
        return item.change_percent;
    }
  }, [calculateMetrics]);

  // 创建热力图数据
  const heatmapData = useMemo(() => {
    if (!data || data.length === 0) return [];
    
    return [{
      type: 'treemap',
      labels: data.map(d => d.name), // 板块名称作为标签
      parents: data.map(() => ''),
      values: data.map(d => getSize(d)),
      text: data.map((d, i) => getBlockText(d, i)), // 文本内容不再包含名称
      hovertext: data.map(d => getTooltipText(d)),
      hoverinfo: 'text',
      textposition: 'middle center',
      textfont: {
        family: 'JetBrains Mono, sans-serif',
        size: 10,
        color: '#f8f8f2'
      },
      marker: {
        colors: data.map(d => getColor(d, colorMetric)),
        colorscale: getColorScale(colorMetric),
        cmin: getColorRange(colorMetric).min,
        cmid: getColorRange(colorMetric).mid,
        cmax: getColorRange(colorMetric).max,
        line: { width: 1, color: 'rgba(43, 45, 58, 0.8)' },
        showscale: true,
        colorbar: {
          title: colorMetric === 'change_percent' ? '涨跌幅' : 
                 colorMetric === 'price_strength' ? '量价强度' :
                 colorMetric === 'turnover_rate' ? '换手率' : '涨跌比',
          titleside: 'top',
          tickfont: { family: 'JetBrains Mono', size: 10, color: '#6272a4' },
          titlefont: { family: 'JetBrains Mono', size: 12, color: '#bd93f9' },
          ticksuffix: colorMetric === 'change_percent' ? '%' : '',
          outlinewidth: 0,
          thickness: 15,
          len: 0.5,
          y: 0.5,
        }
      },
      hoverlabel: {
        bgcolor: '#282a36',
        bordercolor: '#44475a',
        font: { family: 'JetBrains Mono', size: 12, color: '#f8f8f2' }
      },
      branchvalues: 'total', // 确保值按总量比例分配
      pathbar: { visible: false }
    }];
  }, [data, colorMetric, sizeMetric, getSize, getTooltipText, getBlockText, getColor]);

  // 创建热力图布局
  const layout = useMemo(() => ({
    margin: { l: 0, r: 0, b: 0, t: 0, pad: 2 },
    paper_bgcolor: 'rgba(0,0,0,0)',
    plot_bgcolor: 'rgba(0,0,0,0)',
    autosize: true,
    font: {
      family: 'JetBrains Mono, sans-serif',
      size: 11,
      color: '#f8f8f2'
    },
    uniformtext: {
      minsize: 9,
      mode: 'hide' // 当文字无法适应区域时隐藏
    },
    treemapcolorway: [
      'rgb(0, 255, 163)',  // 绿色
      'rgb(248, 248, 242)', // 白色 
      'rgb(255, 51, 88)'   // 红色
    ],
    hoverlabel: {
      bgcolor: '#282a36',
      bordercolor: '#44475a',
      font: { family: 'JetBrains Mono', size: 12, color: '#f8f8f2' }
    }
  }), []);

  // 处理鼠标悬停显示自定义提示框
  const handleHover = useCallback(() => {
    // 空函数，使用Plotly原生悬停功能
  }, []);
    
    return (
      <div 
      ref={chartContainerRef}
      className={`cyberpunk-heatmap ${isFullscreen ? 'fullscreen' : ''}`}
      style={{ height: isFullscreen ? '100vh' : height, ...style }}
    >
      <div className="heatmap-header">
        {showTitle && title ? (
          <div className="heatmap-title">
            <Text strong style={{ fontSize: '14px' }}>{title}</Text>
        </div>
        ) : <div className="heatmap-title-placeholder"></div>}
        
        <Space className="heatmap-controls">
            <Radio.Group 
              value={boardType} 
            onChange={(e) => onBoardTypeChange?.(e.target.value)}
            size="small"
            >
            <Radio.Button value="industry">行业</Radio.Button>
            <Radio.Button value="concept">概念</Radio.Button>
            </Radio.Group>
            
          <Select
            value={colorMetric}
            onChange={setColorMetric}
            style={{ width: 80 }}
            size="small"
            popupClassName="cyberpunk-dropdown"
            dropdownAlign={{ offset: [0, 4] }}
            popupMatchSelectWidth={false}
            transitionName=""
            options={[
              { value: 'change_percent', label: '涨跌幅' },
              { value: 'turnover_rate', label: '换手率' },
              { value: 'price_strength', label: '强度' },
              { value: 'rise_fall_ratio', label: '涨跌比' }
            ]}
            placeholder="颜色"
          />
          
          <Select
            value={sizeMetric}
            onChange={setSizeMetric}
            style={{ width: 80 }}
            size="small"
            popupClassName="cyberpunk-dropdown"
            dropdownAlign={{ offset: [0, 4] }}
            popupMatchSelectWidth={false}
            transitionName=""
            options={[
              { value: 'market_cap', label: '市值' },
              { value: 'turnover_rate', label: '换手率' },
              { value: 'amount', label: '成交额' },
              { value: 'price_strength', label: '强度' }
            ]}
            placeholder="大小"
          />
          
          <Tooltip title="放大">
            <Button 
              icon={<ZoomInOutlined />} 
              onClick={handleZoomIn}
              type="text"
              size="small"
            />
          </Tooltip>
          
          <Tooltip title="缩小">
              <Button
              icon={<ZoomOutOutlined />} 
              onClick={handleZoomOut}
                type="text"
              size="small"
            />
          </Tooltip>
          
          {onRefresh && (
            <Tooltip title="刷新">
              <Button 
                icon={<ReloadOutlined />}
                onClick={onRefresh}
                type="text"
                size="small"
              />
            </Tooltip>
          )}
            
          <Tooltip title={isFullscreen ? "退出全屏" : "全屏"}>
              <Button
                icon={isFullscreen ? <FullscreenExitOutlined /> : <FullscreenOutlined />}
                onClick={handleFullscreen}
              type="text"
              size="small"
              />
            </Tooltip>
        </Space>
      </div>
      
      <div 
        ref={chartContentRef}
        className="heatmap-content"
        style={{ 
          height: `calc(100% - ${showTitle ? '34px' : '32px'})`,
          transform: `scale(${zoomLevel})`,
          transformOrigin: 'top left',
          position: 'relative'
        }}
      >
        {loading ? (
          <div className="loading-container">
            <Spin size="large" />
          </div>
        ) : (
          <Plot
            ref={plotRef}
            data={heatmapData}
            layout={layout}
            config={{
              displayModeBar: false,
              responsive: true,
              doubleClick: false
            }}
            style={{
              width: '100%',
              height: '100%'
            }}
            onUpdate={(figure) => {
              // 不再添加自定义hover处理器
            }}
          />
          )}
        </div>
      </div>
  );
};

export default CyberpunkHeatmap;
