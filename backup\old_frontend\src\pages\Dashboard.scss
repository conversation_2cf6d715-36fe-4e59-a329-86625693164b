.quantum-dashboard {
  // 全局变量
  --primary-color: #bd93f9;
  --secondary-color: #6272a4;
  --success-color: #50fa7b;
  --warning-color: #ffb86c;
  --error-color: #ff5555;
  --info-color: #8be9fd;
  --text-primary: #f8f8f2;
  --text-secondary: #6272a4;
  --bg-primary: #282a36;
  --bg-secondary: #44475a;
  --card-bg: #282a36;
  --border-color: #44475a;
  --font-mono: 'JetBrains Mono', 'Fira Code', monospace;

  // 全局样式
  background-color: var(--bg-primary);
  min-height: 100vh;
  padding: 24px;
  color: var(--text-primary);

  // 卡片样式
  .quantum-card {
    background-color: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 24px rgba(189, 147, 249, 0.1);
    }

    .ant-card-head {
      border-bottom: 1px solid var(--border-color);
      min-height: 48px;

      .ant-card-head-title {
        color: var(--text-primary);
      }
    }

    .ant-card-body {
      padding: 20px;
    }

    // 统计数字样式
    .ant-statistic {
      .ant-statistic-title {
        color: var(--text-secondary);
        font-size: 14px;
        margin-bottom: 8px;
      }

      .ant-statistic-content {
        font-family: var(--font-mono);
        
        .ant-statistic-content-value {
          font-size: 24px;
          font-weight: 600;
        }

        .ant-statistic-content-suffix {
          font-size: 16px;
          margin-left: 4px;
        }
      }
    }

    // 卡片页脚
    .card-footer {
      margin-top: 12px;
      padding-top: 12px;
      border-top: 1px dashed var(--border-color);
      font-size: 12px;
      color: var(--text-secondary);
    }
  }

  // 表格样式
  .quantum-table {
    .ant-table {
      background: transparent;

      .ant-table-thead > tr > th {
        background: var(--bg-secondary);
        border-bottom: 1px solid var(--border-color);
        color: var(--text-primary);
        font-family: var(--font-mono);
        font-size: 12px;
        padding: 8px 16px;

        &::before {
          display: none;
        }
      }

      .ant-table-tbody > tr {
        &:hover {
          td {
            background: var(--bg-secondary) !important;
          }
        }

        > td {
          background: transparent;
          border-bottom: 1px solid var(--border-color);
          transition: all 0.3s ease;
          padding: 8px 16px;
        }

        &:last-child > td {
          border-bottom: none;
        }
      }
    }
  }

  // 图表容器
  .ant-card-body {
    .g2-tooltip {
      background: var(--bg-secondary) !important;
      border: 1px solid var(--border-color) !important;
      border-radius: 4px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      
      .g2-tooltip-title {
        color: var(--text-primary) !important;
        font-family: var(--font-mono);
      }
      
      .g2-tooltip-list-item {
        color: var(--text-secondary) !important;
        font-family: var(--font-mono);
        
        .g2-tooltip-list-item-value {
          color: var(--primary-color) !important;
        }
      }
    }
  }

  // 动画效果
  .ant-card {
    position: relative;
    overflow: hidden;

    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(
        90deg,
        transparent,
        rgba(189, 147, 249, 0.1),
        transparent
      );
      animation: shine 3s infinite;
    }
  }

  @keyframes shine {
    0% {
      left: -100%;
    }
    20% {
      left: 100%;
    }
    100% {
      left: 100%;
    }
  }

  // 响应式调整
  @media (max-width: 768px) {
    padding: 16px;

    .quantum-card {
      .ant-statistic {
        .ant-statistic-content {
          .ant-statistic-content-value {
            font-size: 20px;
          }
        }
      }
    }
  }
}

.dashboard-container {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;

  // GridStack容器样式
  .grid-stack-container {
    margin-top: 10px;
    // 防止网格项内容溢出
    padding-bottom: 20px;
  }

  // 工具栏样式
  .dashboard-tools {
    margin-bottom: 16px;
    display: flex;
    gap: 10px;
    
    .add-widget-btn {
      background: #bd93f9;
      border-color: #bd93f9;
      
      &:hover {
        background: #a881fb;
        border-color: #a881fb;
      }
    }
    
    .save-layout-btn {
      background: #50fa7b;
      border-color: #50fa7b;
      color: #282a36;
      
      &:hover {
        background: #3fe064;
        border-color: #3fe064;
        color: #282a36;
      }
    }
  }

  // GridStack组件样式
  .grid-stack {
    background: #f5f5f5;
    border-radius: 5px;
    padding: 10px;
    min-height: 600px;
    border: 1px solid #f5f5f5;
    box-shadow: none;
  }

  .grid-stack-item {
    border: 1px solid #f5f5f5;
    border-radius: 5px;
    overflow: hidden;
    
    &:hover {
      .grid-stack-item-title-buttons {
        opacity: 1;
      }
    }
    
    // 深色主题（图表、热力图）
    &.theme-dark {
      .grid-stack-item-content {
        background-color: #282a36;
        
        .grid-stack-item-title {
          background-color: #44475a;
          color: #f8f8f2;
        }
      }
    }
    
    // 浅色主题（情绪、公告、策略组）
    &.theme-light {
      .grid-stack-item-content {
        background-color: #ffffff;
        
        .grid-stack-item-title {
          background-color: #f7f7f9;
          color: #333333;
        }
      }
    }
    
    .grid-stack-item-content {
      border-radius: 4px;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
      padding: 0;
      
      .grid-stack-item-title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 12px;
        font-weight: 600;
        user-select: none;
        
        .grid-stack-item-title-buttons {
          opacity: 0.6;
          transition: opacity 0.2s;
          
          .grid-stack-item-delete {
            cursor: pointer;
            color: #ff79c6;
            font-size: 16px;
            font-weight: bold;
            margin-left: 8px;
            
            &:hover {
              color: #ff5555;
            }
          }
        }
      }
      
      .grid-stack-item-body {
        height: calc(100% - 36px);
        overflow: hidden;
        padding-top: 10px;
        
        // 移除组件卡片的多余边距和样式
        .ant-card {
          height: 100%;
          margin: 0;
          border: none;
          border-radius: 0;
          background: transparent;
          box-shadow: none;

          // 隐藏组件卡片标题，避免与GridStack标题重复
          .ant-card-head {
            display: none;
          }

          // 调整卡片内容区域，使其填满整个容器
          .ant-card-body {
            padding: 0 12px 0 15px;
            height: 100%;
            overflow: auto;
          }
        }

        // 特定组件的样式调整
        #candlestick-chart {
          height: 100% !important;
        }
      }
    }
  }
  
  // 浅色主题组件样式 - 使用更兼容的选择器
  .grid-stack-item-body#sentiment-container, 
  .grid-stack-item-body#announcements-container, 
  .grid-stack-item-body#strategy-group-container {
    & ~ .grid-stack-item-title {
      background-color: #f0f0f0;
      color: #333333;
    }
    
    .ant-card {
      background-color: #ffffff;
    }
  }
  
  // 为浅色主题组件的父容器添加特殊类
  .grid-stack-item:has(#sentiment-container),
  .grid-stack-item:has(#announcements-container),
  .grid-stack-item:has(#strategy-group-container) {
    .grid-stack-item-content {
      background-color: #ffffff;
    }
  }

  // 策略组组件专用样式 - 更精确和强制的样式覆盖
  #strategy-group-container {
    padding: 0 !important;
    
    .strategy-group-stat-card {
      width: 100% !important;
      margin: 0 !important;
      
      // 强制覆盖内部样式
      .ant-card-body {
        padding: 16px 0 24px 15px !important;
        width: 100% !important;
      }
      
      // 强制Row撑满宽度
      .stat-overview.ant-row {
        width: 100% !important;
        margin: 0 !important;
        
        // 列调整
        .ant-col {
          &:last-child {
            padding-right: 0 !important;
          }
        }
        
        // 统计数字调整
        .ant-statistic {
          text-align: left !important;
          
          // 强制不换行显示
          .ant-statistic-content-value,
          .ant-statistic-content-suffix {
            display: inline-block !important;
            line-height: 1 !important;
            white-space: nowrap !important;
          }
        }
      }
      
      // 强制"查看全部"按钮靠右对齐
      .view-all-wrapper {
        padding-right: 5px !important;
        margin-right: 0 !important;
      }
    }
  }

  // 深色主题组件样式
  .chart-card {
    .grid-stack-item-body {
      background: var(--quantum-bg-dark);
      padding: 0;
    }
  }

  // 响应式调整
  @media (max-width: 1400px) {
    .grid-stack-item {
      &.w6, &.w8 {
        min-width: 100%;
      }
    }
  }

  @media (max-width: 768px) {
    padding: 16px;
    
    .grid-stack-container {
      margin-top: 16px;
    }
    
    .dashboard-tools {
      flex-direction: column;
      align-items: stretch;
    }
  }

  // 动画效果
  .grid-stack-item {
    animation: fadeIn 0.3s ease-out;
  }
}

// 动画定义
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 量子主题变量
:root {
  --quantum-primary: #bd93f9;
  --quantum-secondary: #6272a4;
  --quantum-success: #50fa7b;
  --quantum-warning: #ffb86c;
  --quantum-error: #ff5555;
  --quantum-info: #8be9fd;
  --quantum-bg-dark: #282a36;
  --quantum-bg-darker: #1a1b24;
  --quantum-border: #44475a;
  --quantum-text-light: #f8f8f2;
  --quantum-text-dark: #282a36;
  --quantum-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  --quantum-shadow-hover: 0 8px 24px rgba(0, 0, 0, 0.12);
}

// GridStack自定义样式覆盖
.grid-stack > .grid-stack-item > .ui-resizable-handle {
  background-color: var(--quantum-primary);
  border-radius: 50%;
  width: 12px;
  height: 12px;
  opacity: 0.5;
  transition: opacity 0.3s;
  
  &:hover {
    opacity: 1;
  }
}

// 自定义调整大小句柄位置
.grid-stack > .grid-stack-item > .ui-resizable-se {
  right: 3px;
  bottom: 3px;
}

.grid-stack > .grid-stack-item > .ui-resizable-sw {
  left: 3px;
  bottom: 3px;
}

.grid-stack > .grid-stack-item > .ui-resizable-ne {
  right: 3px;
  top: 3px;
}

.grid-stack > .grid-stack-item > .ui-resizable-nw {
  left: 3px;
  top: 3px;
}

// 自定义GridStack占位符样式
.grid-stack .grid-stack-placeholder > .placeholder-content {
  background: rgba(189, 147, 249, 0.2);
  border: 2px dashed var(--quantum-primary);
  border-radius: 5px;
} 