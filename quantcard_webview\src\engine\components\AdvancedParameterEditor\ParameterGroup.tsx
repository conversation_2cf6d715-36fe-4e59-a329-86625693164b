/**
 * 🎮 参数分组组件
 * 支持参数分组、条件显示和区间处理
 */

import React, { memo } from 'react';
import styled from 'styled-components';
import ParameterField from './ParameterField';
import { shouldShowParameter } from './utils';
import type { ParameterGroup, StrategyParameter } from './types';

const SentenceContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: wrap;
  padding: 0.75rem 1rem;
  background: rgba(59, 130, 246, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(59, 130, 246, 0.2);
  font-family: "Inter", sans-serif;
`;

const SentenceText = styled.span`
  font-size: 0.9rem;
  color: #374151;
  font-weight: 500;
`;

const SentenceField = styled.div`
  display: inline-flex;
  align-items: center;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  padding: 0.25rem 0.5rem;
  margin: 0 0.125rem;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);

  &:focus-within {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }
`;

interface ParameterGroupProps {
  group: ParameterGroup;
  parameters: Record<string, StrategyParameter>;
  values: Record<string, any>;
  onChange: (name: string, value: any) => void;
  readOnly?: boolean;
}

const ParameterGroupComponent: React.FC<ParameterGroupProps> = memo(({
  group,
  parameters,
  values,
  onChange,
  readOnly = false
}) => {
  const { parameters: paramNames, prefix } = group;

  // 获取可见的参数（使用统一的工具函数）
  const visibleParameters = paramNames
    .filter(name => parameters[name] && shouldShowParameter(parameters[name], values, parameters))
    .map(name => ({ name, ...parameters[name] }));

  if (visibleParameters.length === 0) return null;

  // 统一使用sentence模式渲染
  return (
    <SentenceContainer>
      {prefix && <SentenceText>{prefix}</SentenceText>}
      {visibleParameters.map((param, index) => (
        <React.Fragment key={param.name}>
          <SentenceField>
            <ParameterField
              name={param.name}
              parameter={param}
              value={values[param.name] ?? param.default}
              onChange={(value) => onChange(param.name, value)}
              compact={true}
              readOnly={readOnly}
            />
          </SentenceField>
          {index < visibleParameters.length - 1 && param.unit && (
            <SentenceText>{param.unit}</SentenceText>
          )}
        </React.Fragment>
      ))}
    </SentenceContainer>
  );
});

ParameterGroupComponent.displayName = 'ParameterGroup';

export default ParameterGroupComponent; 