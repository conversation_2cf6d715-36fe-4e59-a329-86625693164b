from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
from datetime import datetime

class BacktestConfigBase(BaseModel):
    start_date: datetime = Field(..., description="开始日期")
    end_date: datetime = Field(..., description="结束日期")
    initial_capital: float = Field(1000000.0, description="初始资金")
    transaction_fee: float = Field(0.0003, description="交易费率")
    slippage: float = Field(0.0001, description="滑点")
    position_size: float = Field(0.1, description="仓位大小")
    stop_loss: Optional[float] = Field(None, description="止损比例")
    take_profit: Optional[float] = Field(None, description="止盈比例")

class BacktestBase(BaseModel):
    name: str = Field(..., description="回测名称")
    description: str = Field("", description="回测描述")
    strategy_group_id: str = Field(..., description="策略组ID")
    config: BacktestConfigBase = Field(..., description="回测配置")

class BacktestCreate(BacktestBase):
    pass

class BacktestUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    config: Optional[BacktestConfigBase] = None

class TradeRecord(BaseModel):
    timestamp: datetime = Field(..., description="交易时间")
    symbol: str = Field(..., description="交易标的")
    direction: str = Field(..., description="交易方向")
    price: float = Field(..., description="交易价格")
    volume: float = Field(..., description="交易数量")
    commission: float = Field(..., description="手续费")
    slippage_cost: float = Field(..., description="滑点成本")

class BacktestResult(BaseModel):
    total_returns: float = Field(..., description="总收益率")
    annual_returns: float = Field(..., description="年化收益率")
    max_drawdown: float = Field(..., description="最大回撤")
    sharpe_ratio: float = Field(..., description="夏普比率")
    volatility: float = Field(..., description="波动率")
    win_rate: float = Field(..., description="胜率")
    profit_factor: float = Field(..., description="盈亏比")
    total_trades: int = Field(..., description="总交易次数")
    daily_stats: Dict[str, Any] = Field(default_factory=dict, description="每日统计") 