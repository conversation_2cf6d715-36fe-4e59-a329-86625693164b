import React, { useState, useMemo, memo, useEffect } from 'react';
import { Input, Card, Tag, Empty, Row, Col, Dropdown, Space, Button, Tooltip, message } from 'antd';
import { SearchOutlined, SortAscendingOutlined, AppstoreOutlined, PlusOutlined } from '@ant-design/icons';
import { StrategyCardTemplate } from '../../types/api';
import QuantumButton from '../common/QuantumButton';
import { tagService, TagConfig } from '../../services/strategyService';
import './StrategyMarket.scss';
import InlineParameterEditor from '../strategy/InlineParameterEditor';
import StrategyCard from '../strategy/StrategyCard';

interface StrategyMarketProps {
  templates: StrategyCardTemplate[];
  selectedCards: string[];
  mode: 'simple' | 'canvas';
  groupType?: 'filter' | 'timing';  // 添加策略组类型参数
  onAddToWorkspace: (card: StrategyCardTemplate, mode: 'simple' | 'canvas') => void;
  loading?: boolean;       // 是否正在加载
  hasMore?: boolean;       // 是否还有更多数据
  onLoadMore?: () => void; // 加载更多回调
}

const StrategyMarket: React.FC<StrategyMarketProps> = memo(({
  templates,
  selectedCards,
  mode,
  groupType = 'filter',  // 默认为选股模式
  onAddToWorkspace,
  loading: isPageLoading,
  hasMore,
  onLoadMore,
}) => {
  const [searchText, setSearchText] = useState('');
  const [activeTag, setActiveTag] = useState<string>('all');
  const [sortBy, setSortBy] = useState<'name' | 'popularity' | 'newest'>('popularity');
  const [cardLoading, setCardLoading] = useState<Record<string, boolean>>({});
  // 添加标签配置状态
  const [tagConfigs, setTagConfigs] = useState<TagConfig[]>([]);

  // 获取所有唯一的标签
  const { allTags } = useMemo(() => {
    const tagSet = new Set(templates.flatMap(t => t.tags || []));
    return {
      allTags: ['all', ...Array.from(tagSet)]
    };
  }, [templates]);

  // 添加获取标签配置的Effect - 这不涉及模板数据，保留
  useEffect(() => {
    const fetchTags = async () => {
      try {
        const configs = await tagService.getTags();
        setTagConfigs(configs);
      } catch (error) {
        console.error('Failed to fetch tag configs:', error);
        message.error('获取标签配置失败');
      }
    };
    fetchTags();
  }, []);

  // 获取tag颜色
  const getTagColor = (tag: string) => {
    const tagConfig = tagConfigs.find(t => t.name === tag);
    return tagConfig?.color;
  };

  // 处理添加策略卡片
  const handleAddToWorkspace = async (template: StrategyCardTemplate) => {
    try {
      setCardLoading(prev => ({ ...prev, [template.id]: true }));
      // 调用父组件传入的添加方法
      onAddToWorkspace(template, mode);
      // 使用延迟来模拟添加过程，实际项目中应该是真实的API调用
      setTimeout(() => {
        setCardLoading(prev => ({ ...prev, [template.id]: false }));
      }, 500);
    } catch (error) {
      console.error('添加策略卡失败:', error);
      setCardLoading(prev => ({ ...prev, [template.id]: false }));
    }
  };

  // 过滤和排序模板
  const filteredTemplates = useMemo(() => {
    return templates
      .filter(template => {
        const matchesSearch = searchText
          ? template.name.toLowerCase().includes(searchText.toLowerCase()) ||
            template.description.toLowerCase().includes(searchText.toLowerCase()) ||
            template.tags.some(tag => tag.toLowerCase().includes(searchText.toLowerCase()))
          : true;
        
        // 只按tag过滤，因为策略类型已经在API调用时筛选
        const matchesTag = activeTag === 'all' || template.tags.includes(activeTag);

        return matchesSearch && matchesTag;
      })
      .sort((a, b) => {
        switch (sortBy) {
          case 'name':
            return a.name.localeCompare(b.name);
          case 'newest':
            return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
          case 'popularity':
          default:
            return (b.usage_count || 0) - (a.usage_count || 0);
        }
      });
  }, [templates, searchText, activeTag, sortBy]);

  const sortOptions = [
    { label: '按名称排序', value: 'name' },
    { label: '按热度排序', value: 'popularity' },
    { label: '按最新排序', value: 'newest' }
  ];

  return (
    <div className="strategy-market">
      <div className="market-header">
        <div className="search-section">
          <Input
            prefix={<SearchOutlined />}
            placeholder="搜索策略卡..."
            value={searchText}
            onChange={e => setSearchText(e.target.value)}
            className="search-input"
            allowClear
          />
          <Space className="filter-buttons">
            <Tooltip title="标签筛选">
              <div>
                <Dropdown
                  menu={{
                    items: allTags.map(tag => ({
                      key: tag,
                      label: tag === 'all' ? '全部标签' : tag,
                      onClick: () => setActiveTag(tag)
                    }))
                  }}
                >
                  <Button>
                    {activeTag === 'all' ? '全部标签' : activeTag}
                  </Button>
                </Dropdown>
              </div>
            </Tooltip>
            <Tooltip title="排序方式">
              <div>
                <Dropdown
                  menu={{
                    items: sortOptions.map(option => ({
                      key: option.value,
                      label: option.label,
                      onClick: () => setSortBy(option.value as typeof sortBy)
                    }))
                  }}
                >
                  <Button icon={<SortAscendingOutlined />} />
                </Dropdown>
              </div>
            </Tooltip>
          </Space>
        </div>

        <div className="tag-cloud">
          {allTags.filter(tag => tag !== 'all').map(tag => {
            const tagColor = activeTag === tag ? getTagColor(tag) || 'blue' : undefined;
            return (
              <Tag
                key={tag}
                color={tagColor}
                style={tagColor ? { 
                  cursor: 'pointer',
                  backgroundColor: 'transparent',
                  color: tagColor,
                  borderColor: tagColor
                } : { cursor: 'pointer' }}
                onClick={() => setActiveTag(tag === activeTag ? 'all' : tag)}
              >
                {tag}
              </Tag>
            );
          })}
        </div>
      </div>

      <div className="market-content">
        {filteredTemplates.length > 0 ? (
          <div>
            <Row gutter={[16, 16]}>
              {filteredTemplates.map(template => (
                <Col key={template.id} span={24}>
                  <StrategyCard
                    template={template}
                    displayMode="market"
                    loading={cardLoading[template.id]}
                    onAddToWorkspace={handleAddToWorkspace}
                    tagConfigs={tagConfigs} // 传递标签配置给StrategyCard
                  />
                </Col>
              ))}
            </Row>
            
            {/* 添加加载更多按钮 */}
            {hasMore && (
              <div className="load-more-container" style={{ textAlign: 'center', margin: '20px 0' }}>
                <Button 
                  onClick={onLoadMore} 
                  loading={isPageLoading} 
                  type="primary" 
                  ghost
                >
                  加载更多策略卡
                </Button>
              </div>
            )}
          </div>
        ) : (
          <Empty description="没有找到匹配的策略" />
        )}
      </div>
    </div>
  );
});

export default StrategyMarket; 