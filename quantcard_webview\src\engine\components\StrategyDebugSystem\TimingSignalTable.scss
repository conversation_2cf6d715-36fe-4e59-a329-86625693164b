// 择时信号表格赛博朋克风格样式
.timing-signal-table-container {
  background-color: #282a36;
  border-radius: 8px;
  box-shadow: 0 0 20px rgba(189, 147, 249, 0.1),
              0 0 40px rgba(139, 233, 253, 0.1);
  border: 1px solid rgba(189, 147, 249, 0.2);
  overflow: hidden;
  margin: 16px;
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, 
      #ff79c6 0%,
      #bd93f9 33%,
      #8be9fd 66%,
      #50fa7b 100%
    );
    opacity: 0.8;
  }
  
  .results-header {
    background-color: #1e1f29;
    padding: 8px 16px;
    border-bottom: 1px solid rgba(98, 114, 164, 0.2);
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .results-title {
      color: #ff79c6;
      font-weight: 500;
      font-size: 14px;
    }
    
    .results-count {
      background: #ff79c6;
      color: #282a36;
      padding: 2px 8px;
      border-radius: 4px;
      font-weight: bold;
      font-size: 12px;
    }
  }
  
  .signals-table {
    .custom-table {
      width: 100%;
      border-collapse: collapse;
      background-color: #282a36;
      color: #f8f8f2;
      
      thead {
        tr {
          th {
            background-color: #1e1f29;
            color: #6272a4;
            border-bottom: 1px solid rgba(98, 114, 164, 0.2);
            font-weight: 500;
            font-size: 12px;
            text-transform: uppercase;
            padding: 8px 12px;
            text-align: left;
          }
        }
      }
      
      tbody {
        tr {
          transition: background-color 0.3s ease;
          
          &:hover {
            background-color: #44475a;
          }
          
          td {
            border-bottom: 1px solid rgba(98, 114, 164, 0.1);
            padding: 8px 12px;
            font-size: 13px;
            
            .stock-code {
              color: #ff79c6;
              font-family: monospace;
              font-weight: 500;
            }
            
            .stock-name {
              color: #f8f8f2;
            }
            
            .direction-badge {
              padding: 2px 8px;
              border-radius: 4px;
              font-size: 11px;
              font-weight: 600;
              text-transform: uppercase;
              display: inline-block;
              min-width: 40px;
              text-align: center;
            }
            
            .stock-price {
              color: #8be9fd;
              font-family: monospace;
            }
            
            .ma-diff {
              font-family: monospace;
              font-weight: 500;
            }
            
            .trigger-condition {
              color: #f8f8f2;
              font-size: 12px;
              max-width: 200px;
              display: inline-block;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
            
            .stock-industry {
              color: #bd93f9;
              font-size: 12px;
            }
          }
        }
      }
    }
    
    .table-pagination {
      background-color: #1e1f29;
      padding: 8px 16px;
      border-top: 1px solid rgba(98, 114, 164, 0.2);
      text-align: right;
      font-size: 12px;
      color: #6272a4;
    }
  }
}

// 空结果状态
.empty-result {
  padding: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #282a36;
  
  .empty-text {
    color: #6272a4;
    font-size: 14px;
  }
}
