import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import { AnimatePresence, motion } from 'framer-motion';
import { StrategySaveManager, type SaveResult } from './StrategySaveManager';
import type { StrategyGroup } from '../../types/game';
import type { StrategyTypeConfig } from './StrategyTypeSelector/types';

interface SaveStrategyPanelProps {
	open: boolean;
	initialName?: string;
	initialDescription?: string;
	currentGroup: StrategyGroup | null;
	strategyTypeConfig: StrategyTypeConfig;
	templateCache: Record<string, any>;
	onCancel: () => void;
	onSaveComplete: (result: SaveResult) => void;
}

const Overlay = styled(motion.div)`
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(15, 23, 42, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1000;
	backdrop-filter: blur(4px);
`;

const Panel = styled(motion.div)`
	width: min(92vw, 520px);
	background: linear-gradient(180deg, #ffffff 0%, #f8fafc 100%);
	border: 1px solid rgba(59, 130, 246, 0.2);
	border-radius: 16px;
	box-shadow: 0 12px 40px rgba(2, 6, 23, 0.25);
	overflow: hidden;
`;

const Header = styled.div`
	padding: 16px 20px;
	background: rgba(59, 130, 246, 0.08);
	border-bottom: 1px solid rgba(59, 130, 246, 0.2);
	font-weight: 800;
	font-size: 16px;
	color: #0f172a;
`;

const Body = styled.div`
	padding: 16px 20px 4px;
	display: grid;
	grid-template-columns: 1fr;
	gap: 12px;
`;

const Field = styled.label`
	display: grid;
	gap: 8px;
	font-size: 12px;
	font-weight: 700;
	color: #334155;
`;

const Input = styled.input`
	appearance: none;
	width: 100%;
	padding: 10px 12px;
	border-radius: 10px;
	border: 1px solid rgba(100, 116, 139, 0.35);
	background: rgba(255, 255, 255, 0.9);
	color: #0f172a;
	font-size: 14px;
	outline: none;
	transition: box-shadow 0.2s ease, border-color 0.2s ease;
	&:focus {
		border-color: #3b82f6;
		box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
	}
`;

const Textarea = styled.textarea`
	appearance: none;
	width: 100%;
	min-height: 92px;
	padding: 10px 12px;
	border-radius: 10px;
	border: 1px solid rgba(100, 116, 139, 0.35);
	background: rgba(255, 255, 255, 0.9);
	color: #0f172a;
	font-size: 14px;
	outline: none;
	resize: vertical;
	transition: box-shadow 0.2s ease, border-color 0.2s ease;
	&:focus {
		border-color: #3b82f6;
		box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
	}
`;

const Footer = styled.div`
	display: flex;
	gap: 10px;
	justify-content: flex-end;
	padding: 12px 20px 16px;
	border-top: 1px solid rgba(100, 116, 139, 0.15);
	background: rgba(248, 250, 252, 0.7);
`;

const ErrorMessage = styled.div`
	background: rgba(239, 68, 68, 0.1);
	border: 1px solid rgba(239, 68, 68, 0.3);
	color: #dc2626;
	padding: 0.75rem;
	border-radius: 8px;
	font-size: 0.875rem;
	margin-bottom: 1rem;
`;

const Button = styled(motion.button)<{ $variant?: 'primary' | 'secondary' }>`
	padding: 10px 16px;
	border-radius: 10px;
	font-weight: 700;
	font-size: 14px;
	border: 2px solid
		${props => (props.$variant === 'primary' ? '#3b82f6' : 'rgba(100,116,139,0.8)')};
	background: ${props => (props.$variant === 'primary' ? '#3b82f6' : 'transparent')};
	color: ${props => (props.$variant === 'primary' ? '#fff' : '#475569')};
	cursor: pointer;
	&:disabled {
		opacity: 0.6;
		cursor: not-allowed;
	}
`;

export default function SaveStrategyPanel({
	open,
	initialName,
	initialDescription,
	currentGroup,
	strategyTypeConfig,
	templateCache,
	onCancel,
	onSaveComplete
}: SaveStrategyPanelProps) {
	const [name, setName] = useState<string>(initialName ?? '');
	const [description, setDescription] = useState<string>(initialDescription ?? '');

	useEffect(() => {
		if (open) {
			setName(initialName ?? '');
			setDescription(initialDescription ?? '');
		}
	}, [open, initialName, initialDescription]);

	const handleSaveComplete = (result: SaveResult) => {
		if (result.success) {
			onCancel(); // 关闭面板
		}
		onSaveComplete(result);
	};

	return (
		<StrategySaveManager
			currentGroup={currentGroup}
			strategyTypeConfig={strategyTypeConfig}
			templateCache={templateCache}
			onSaveComplete={handleSaveComplete}
		>
			{({ handleSave, isSaving, saveError }) => (
				<AnimatePresence>
					{open && (
						<Overlay
							initial={{ opacity: 0 }}
							animate={{ opacity: 1 }}
							exit={{ opacity: 0 }}
						>
							<Panel
								initial={{ scale: 0.96, y: 12, opacity: 0 }}
								animate={{ scale: 1, y: 0, opacity: 1 }}
								exit={{ scale: 0.96, y: 12, opacity: 0 }}
								transition={{ type: 'spring', stiffness: 320, damping: 26 }}
							>
								<Header>保存策略组</Header>
								<Body>
									{saveError && (
										<ErrorMessage>
											❌ {saveError}
										</ErrorMessage>
									)}
									<Field>
										<span>策略组名称</span>
										<Input
											placeholder="如：趋势突破·V1"
											value={name}
											onChange={e => setName(e.target.value)}
											maxLength={60}
											disabled={isSaving}
										/>
									</Field>
									<Field>
										<span>描述</span>
										<Textarea
											placeholder="为该策略组添加一个简短描述，便于后续识别与分类"
											value={description}
											onChange={e => setDescription(e.target.value)}
											maxLength={300}
											disabled={isSaving}
										/>
									</Field>
								</Body>
								<Footer>
									<Button
										$variant="secondary"
										onClick={onCancel}
										whileTap={{ scale: 0.98 }}
										disabled={isSaving}
									>取消</Button>
									<Button
										$variant="primary"
										disabled={!name.trim() || isSaving}
										onClick={() => handleSave(name.trim(), description.trim())}
										whileTap={{ scale: 0.98 }}
									>
										{isSaving ? '保存中...' : '保存'}
									</Button>
								</Footer>
							</Panel>
						</Overlay>
					)}
				</AnimatePresence>
			)}
		</StrategySaveManager>
	);
} 
