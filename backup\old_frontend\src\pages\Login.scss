.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, #1890ff 0%, #722ed1 100%);
}

.animated-background {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 1;
  overflow: hidden;
}

.wave {
  position: absolute;
  width: 200%;
  height: 200%;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 45%;
  animation: wave 20s infinite linear;
  transform-origin: 50% 50%;

  &:nth-child(1) {
    animation-duration: 25s;
  }

  &:nth-child(2) {
    animation-duration: 30s;
    opacity: 0.3;
  }

  &:nth-child(3) {
    animation-duration: 35s;
    opacity: 0.2;
  }
}

@keyframes wave {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.content-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 80px;
  z-index: 2;
  padding: 20px;
  max-width: 1200px;
  width: 100%;

  @media (max-width: 1024px) {
    flex-direction: column;
    gap: 40px;
  }
}

.welcome-section {
  color: white;
  flex: 1;
  max-width: 400px;

  .logo {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 24px;

    .logo-icon {
      font-size: 36px;
    }

    .logo-text {
      font-size: 32px;
      font-weight: bold;
    }
  }

  .welcome-text {
    font-size: 36px;
    margin-bottom: 16px;
  }

  .slogan {
    font-size: 18px;
    opacity: 0.8;
  }
}

.login-card-wrapper {
  flex: 1;
  max-width: 400px;
  width: 100%;
}

.login-card {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95) !important;
  border-radius: 16px !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  padding: 32px !important;

  .card-title {
    text-align: center;
    margin-bottom: 32px;
  }
}

.login-form {
  .ant-input-affix-wrapper {
    border-radius: 8px;
    height: 44px;
    
    input {
      height: 42px;
    }
  }

  .form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
  }

  .forgot-link {
    color: #1890ff;
    
    &:hover {
      color: #40a9ff;
    }
  }

  .login-button {
    height: 44px;
    border-radius: 8px;
    background: linear-gradient(45deg, #1890ff, #722ed1);
    border: none;
    font-size: 16px;
    
    &:hover {
      opacity: 0.9;
    }
  }
}

.other-options {
  .divider {
    margin: 32px 0;
  }

  .social-login {
    display: flex;
    justify-content: center;
    margin-bottom: 24px;

    .social-button {
      font-size: 24px;
      width: 48px;
      height: 48px;
      border-radius: 50%;
      
      &:hover {
        background: rgba(0, 0, 0, 0.05);
      }
    }
  }

  .register-hint {
    text-align: center;

    .register-link {
      color: #1890ff;
      
      &:hover {
        color: #40a9ff;
      }
    }
  }
} 