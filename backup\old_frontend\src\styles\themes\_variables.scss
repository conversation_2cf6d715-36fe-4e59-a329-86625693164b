@use "sass:map";

// 颜色变量
$color-primary: #6366f1;
$color-primary-light: (
  100: #eef2ff,
  200: #e0e7ff,
  300: #c7d2fe,
  400: #a5b4fc,
  500: #818cf8
);
$color-primary-dark: (
  600: #4f46e5,
  700: #4338ca,
  800: #3730a3,
  900: #312e81
);

// 中性色
$color-neutral: (
  white: #ffffff,
  gray-50: #f8fafc,
  gray-100: #f1f5f9,
  gray-200: #e2e8f0,
  gray-300: #cbd5e1,
  gray-400: #94a3b8,
  gray-500: #64748b,
  gray-600: #475569,
  gray-700: #334155,
  gray-800: #1e293b,
  gray-900: #0f172a
);

// 功能色
$color-functional: (
  success: #10b981,
  warning: #f59e0b,
  error: #ef4444,
  info: #3b82f6
);

// 字体
$font-family-base: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
$font-family-mono: "JetBrains Mono", monospace;

// 字体大小
$font-size: (
  xs: 0.75rem,
  sm: 0.875rem,
  base: 1rem,
  lg: 1.125rem,
  xl: 1.25rem,
  "2xl": 1.5rem,
  "3xl": 1.875rem,
  "4xl": 2.25rem,
  "5xl": 3rem
);

// 间距
$spacing: (
  0: 0,
  1: 0.25rem,
  2: 0.5rem,
  3: 0.75rem,
  4: 1rem,
  5: 1.25rem,
  6: 1.5rem,
  8: 2rem,
  10: 2.5rem,
  12: 3rem
);

// 圆角
$border-radius: (
  none: 0,
  sm: 0.125rem,
  md: 0.375rem,
  lg: 0.5rem,
  xl: 0.75rem,
  full: 9999px
);

// 阴影
$shadow: (
  sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05),
  md: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
  lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
  xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1)
);

// 过渡
$transition: (
  base: 0.2s ease-in-out,
  slow: 0.3s ease-in-out,
  slower: 0.5s ease-in-out
);

// 动画曲线
$ease: (
  in-out: cubic-bezier(0.4, 0, 0.2, 1),
  out: cubic-bezier(0.0, 0, 0.2, 1),
  in: cubic-bezier(0.4, 0, 1, 1)
);

// 动画时长
$duration: (
  shorter: 0.15s,
  short: 0.2s,
  base: 0.3s,
  long: 0.375s,
  longer: 0.5s
);

// Z-index层级
$z-index: (
  drawer: 1000,
  modal: 1050,
  popover: 1060,
  tooltip: 1070
);

// 新增主题色
$theme-colors: (
  primary: $color-primary,
  secondary: #6c757d,
  success: map.get($color-functional, success),
  warning: map.get($color-functional, warning),
  danger: map.get($color-functional, error),
  info: map.get($color-functional, info),
  light: map.get($color-neutral, gray-100),
  dark: map.get($color-neutral, gray-900)
);

// 菜单动画
$menu-transition: (
  transform: transform 0.3s map.get($ease, out),
  width: width 0.3s map.get($ease, in-out),
  background: background-color 0.3s map.get($ease, in-out),
  all: all 0.3s map.get($ease, in-out)
);

// 悬停效果
$hover-effect: (
  lift: translateY(-2px),
  scale: scale(1.02),
  glow: 0 0 20px rgba(99, 102, 241, 0.4)
); 