import { ThemeConfig } from 'antd';

export const theme = {
  colors: {
    primary: '#6366f1',
    primaryLight: ['#eef2ff', '#e0e7ff', '#c7d2fe', '#a5b4fc', '#818cf8'],
    primaryDark: ['#4f46e5', '#4338ca', '#3730a3', '#312e81'],
    neutral: {
      white: '#ffffff',
      gray: ['#f8fafc', '#f1f5f9', '#e2e8f0', '#cbd5e1',
             '#94a3b8', '#64748b', '#475569',
             '#334155', '#1e293b', '#0f172a'],
    },
    functional: {
      success: '#10b981',
      warning: '#f59e0b',
      error: '#ef4444',
      info: '#3b82f6',
    }
  },
  typography: {
    fontFamily: {
      base: '-apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif',
      mono: '"JetBrains Mono", monospace',
    },
    fontSize: {
      xs: '0.75rem',
      sm: '0.875rem',
      base: '1rem',
      lg: '1.125rem',
      xl: '1.25rem',
      '2xl': '1.5rem',
      '3xl': '1.875rem',
      '4xl': '2.25rem',
      '5xl': '3rem',
    }
  },
  spacing: {
    0: '0',
    1: '0.25rem',
    2: '0.5rem',
    3: '0.75rem',
    4: '1rem',
    5: '1.25rem',
    6: '1.5rem',
    8: '2rem',
    10: '2.5rem',
    12: '3rem',
  },
  borderRadius: {
    none: '0',
    sm: '0.125rem',
    md: '0.375rem',
    lg: '0.5rem',
    xl: '0.75rem',
    full: '9999px',
  },
  shadows: {
    sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
    md: '0 4px 6px -1px rgb(0 0 0 / 0.1)',
    lg: '0 10px 15px -3px rgb(0 0 0 / 0.1)',
    xl: '0 20px 25px -5px rgb(0 0 0 / 0.1)',
  },
  transitions: {
    base: '0.2s ease-in-out',
    slow: '0.3s ease-in-out',
    slower: '0.5s ease-in-out',
  },
};

// Ant Design主题配置
export const antdTheme: ThemeConfig = {
  token: {
    colorPrimary: theme.colors.primary,
    colorSuccess: theme.colors.functional.success,
    colorWarning: theme.colors.functional.warning,
    colorError: theme.colors.functional.error,
    colorInfo: theme.colors.functional.info,
    borderRadius: parseInt(theme.borderRadius.md),
    fontFamily: theme.typography.fontFamily.base,
  },
  components: {
    Button: {
      borderRadius: parseInt(theme.borderRadius.md),
      controlHeight: 40,
      fontSize: parseInt(theme.typography.fontSize.base),
    },
    Card: {
      borderRadius: parseInt(theme.borderRadius.lg),
    },
    Input: {
      borderRadius: parseInt(theme.borderRadius.md),
    },
  },
}; 