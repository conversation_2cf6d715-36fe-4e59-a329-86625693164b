"""
交易日历服务

提供交易日和交易时间的查询服务，支持中国A股市场
直接从数据库获取交易日历信息
"""
import logging
import asyncio
from datetime import datetime, time, timedelta
import pandas as pd
from typing import Optional, List, Dict, Set
from functools import lru_cache
from sqlalchemy import text

from ..core.data.db.base import db_manager

logger = logging.getLogger(__name__)

class TradingCalendar:
    """交易日历服务，提供交易日和交易时间的查询"""
    
    _instance = None
    
    # 中国A股交易时间段定义（北京时间）
    MORNING_START = time(9, 30, 0)  # 上午开盘时间
    MORNING_END = time(11, 30, 0)   # 上午收盘时间
    AFTERNOON_START = time(13, 0, 0)  # 下午开盘时间
    AFTERNOON_END = time(15, 0, 0)    # 下午收盘时间
    
    # 中国A股周末休市（0: 周一, 6: 周日）
    WEEKEND_DAYS = {5, 6}  # 周六和周日
    
    @classmethod
    def get_instance(cls):
        """获取单例实例"""
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance
    
    def __init__(self):
        """初始化交易日历服务"""
        self._trading_days: Set[str] = set()  # 交易日集合，格式 'YYYY-MM-DD'
        self._calendar_loaded = False
        self._calendar_loading = False
        self._lock = asyncio.Lock()
        
        # 自动加载交易日历将在应用启动时进行，而不是在构造函数中
        # 避免在同步上下文中创建异步任务
        
    async def load_calendar(self, year: Optional[int] = None) -> bool:
        """
        从数据库加载交易日历
        
        Args:
            year: 年份，默认为当前年份和前后一年
            
        Returns:
            bool: 是否成功加载
        """
        # 如果正在加载，等待完成
        if self._calendar_loading:
            while self._calendar_loading:
                await asyncio.sleep(0.2)
            return self._calendar_loaded
            
        async with self._lock:
            try:
                self._calendar_loading = True
                
                # 设置年份
                if not year:
                    current_year = datetime.now().year
                    years = [current_year - 1, current_year, current_year + 1]
                else:
                    years = [year]
                
                # 从数据库获取交易日数据
                async with db_manager.get_session() as session:
                    # 构建SQL查询，获取指定年份的交易日
                    # 注意：trade_calendar 表中所有日期都是交易日，无需 is_trading_day 字段
                    years_condition = ",".join([f"'{y}'" for y in years])
                    sql_query = f"""
                    SELECT trade_date FROM trade_calendar 
                    WHERE EXTRACT(YEAR FROM trade_date) IN ({years_condition})
                    ORDER BY trade_date
                    """
                    
                    result = await session.execute(text(sql_query))
                    trading_dates = result.fetchall()
                    
                    # 清空现有数据
                    self._trading_days.clear()
                    
                    # 解析结果并添加到交易日集合
                    for row in trading_dates:
                        date_str = row[0].strftime('%Y-%m-%d')
                        self._trading_days.add(date_str)
                    
                self._calendar_loaded = True
                logger.info(f"交易日历从数据库加载完成，共{len(self._trading_days)}个交易日")
                return True
                
            except Exception as e:
                logger.error(f"从数据库加载交易日历失败: {str(e)}")
                return False
            finally:
                self._calendar_loading = False
        
    def is_trading_day(self, date: Optional[datetime] = None) -> bool:
        """
        判断给定日期是否为交易日
        
        Args:
            date: 要判断的日期，默认为当前日期
            
        Returns:
            bool: 是否为交易日
        """
        if not date:
            date = datetime.now()
            
        date_str = date.strftime('%Y-%m-%d')
        
        # 如果日历未加载，使用简单规则判断
        if not self._calendar_loaded:
            # 如果是周末，必然不是交易日
            if date.weekday() in self.WEEKEND_DAYS:
                return False
            # 否则暂时假设为交易日
            return True
            
        return date_str in self._trading_days
        
    def is_trading_hours(self, dt: Optional[datetime] = None) -> bool:
        """
        判断给定时间是否为交易时间
        
        Args:
            dt: 要判断的时间，默认为当前时间
            
        Returns:
            bool: 是否为交易时间
        """
        if not dt:
            dt = datetime.now()
            
        # 首先判断是否为交易日
        if not self.is_trading_day(dt):
            return False
            
        # 判断是否在交易时段
        t = dt.time()
        return ((self.MORNING_START <= t <= self.MORNING_END) or 
                (self.AFTERNOON_START <= t <= self.AFTERNOON_END))
                
    def get_next_trading_time(self, dt: Optional[datetime] = None) -> datetime:
        """
        获取下一个交易时间点
        
        Args:
            dt: 参考时间，默认为当前时间
            
        Returns:
            datetime: 下一个交易时间点
        """
        if not dt:
            dt = datetime.now()
            
        current_date = dt.date()
        current_time = dt.time()
        
        # 当天情况处理
        if self.is_trading_day(dt):
            # 如果早于上午开盘
            if current_time < self.MORNING_START:
                return datetime.combine(current_date, self.MORNING_START)
                
            # 如果在上午收盘和下午开盘之间
            if self.MORNING_END < current_time < self.AFTERNOON_START:
                return datetime.combine(current_date, self.AFTERNOON_START)
                
            # 如果已经过了下午收盘
            if current_time > self.AFTERNOON_END:
                # 寻找下一个交易日
                next_date = self.get_next_trading_day(current_date)
                return datetime.combine(next_date, self.MORNING_START)
                
        else:
            # 如果当天不是交易日，寻找下一个交易日
            next_date = self.get_next_trading_day(current_date)
            return datetime.combine(next_date, self.MORNING_START)
            
        # 如果都不符合前面的条件，说明已在交易时段内，返回当前时间
        return dt
        
    def get_next_trading_day(self, date: Optional[datetime] = None) -> datetime.date:
        """
        获取下一个交易日
        
        Args:
            date: 参考日期，默认为当前日期
            
        Returns:
            date: 下一个交易日的日期
        """
        if not date:
            date = datetime.now()
            
        # 如果是datetime对象，提取日期部分
        if isinstance(date, datetime):
            date = date.date()
            
        # 如果日历未加载，使用简单规则查找下一个工作日
        if not self._calendar_loaded:
            next_date = date + timedelta(days=1)
            while next_date.weekday() in self.WEEKEND_DAYS:
                next_date += timedelta(days=1)
            return next_date
        
        # 转换为字符串格式，便于比较
        date_str = date.strftime('%Y-%m-%d')
        
        # 找到大于当前日期的最小交易日
        future_trading_days = [d for d in self._trading_days if d > date_str]
        if future_trading_days:
            next_date_str = min(future_trading_days)
            return datetime.strptime(next_date_str, '%Y-%m-%d').date()
        
        # 如果没有找到未来交易日，获取一年后的工作日
        # 这种情况一般不会发生，但为了健壮性处理一下
        next_date = date + timedelta(days=1)
        while next_date.weekday() in self.WEEKEND_DAYS:
            next_date += timedelta(days=1)
        return next_date
        
    def get_previous_trading_day(self, date: Optional[datetime] = None) -> datetime.date:
        """
        获取上一个交易日
        
        Args:
            date: 参考日期，默认为当前日期
            
        Returns:
            date: 上一个交易日的日期
        """
        if not date:
            date = datetime.now()
            
        # 如果是datetime对象，提取日期部分
        if isinstance(date, datetime):
            date = date.date()
            
        # 如果日历未加载，使用简单规则查找前一个工作日
        if not self._calendar_loaded:
            prev_date = date - timedelta(days=1)
            while prev_date.weekday() in self.WEEKEND_DAYS:
                prev_date -= timedelta(days=1)
            return prev_date
            
        # 转换为字符串格式，便于比较
        date_str = date.strftime('%Y-%m-%d')
        
        # 找到小于当前日期的最大交易日
        past_trading_days = [d for d in self._trading_days if d < date_str]
        if past_trading_days:
            prev_date_str = max(past_trading_days)
            return datetime.strptime(prev_date_str, '%Y-%m-%d').date()
        
        # 如果没有找到过去交易日，返回前一个工作日
        prev_date = date - timedelta(days=1)
        while prev_date.weekday() in self.WEEKEND_DAYS:
            prev_date -= timedelta(days=1)
        return prev_date
        
    def get_trading_days_between(self, start_date: datetime, end_date: datetime) -> List[datetime.date]:
        """
        获取两个日期之间的所有交易日
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            List[date]: 交易日列表
        """
        # 提取日期部分
        if isinstance(start_date, datetime):
            start_date = start_date.date()
        if isinstance(end_date, datetime):
            end_date = end_date.date()
            
        # 如果日历未加载，使用简单规则判断
        if not self._calendar_loaded:
            result = []
            current_date = start_date
            while current_date <= end_date:
                if current_date.weekday() not in self.WEEKEND_DAYS:
                    result.append(current_date)
                current_date += timedelta(days=1)
            return result
            
        # 转换为字符串格式
        start_str = start_date.strftime('%Y-%m-%d')
        end_str = end_date.strftime('%Y-%m-%d')
        
        # 筛选交易日
        trading_days = [
            datetime.strptime(d, '%Y-%m-%d').date() 
            for d in self._trading_days 
            if start_str <= d <= end_str
        ]
        
        return sorted(trading_days)
        
    def get_market_status(self) -> Dict[str, any]:
        """
        获取当前市场状态
        
        Returns:
            Dict: 市场状态信息
        """
        now = datetime.now()
        is_trading_day_value = self.is_trading_day(now)
        is_trading_hours_value = self.is_trading_hours(now)
        
        next_trading_time = self.get_next_trading_time(now)
        next_trading_day = self.get_next_trading_day(now)
        
        # 计算距离下一个交易时间还有多长时间
        time_to_next = (next_trading_time - now).total_seconds()
        hours, remainder = divmod(int(time_to_next), 3600)
        minutes, seconds = divmod(remainder, 60)
        time_to_next_str = f"{hours}小时{minutes}分钟{seconds}秒"
        
        # 市场状态描述
        if is_trading_hours_value:
            status_description = "交易中"
        elif is_trading_day_value:
            if now.time() < self.MORNING_START:
                status_description = "等待开盘"
            elif self.MORNING_END < now.time() < self.AFTERNOON_START:
                status_description = "午间休市"
            else:
                status_description = "已收盘"
        else:
            if now.weekday() in self.WEEKEND_DAYS:
                status_description = "周末休市"
            else:
                status_description = "节假日休市"
        
        return {
            "now": now.strftime("%Y-%m-%d %H:%M:%S"),
            "is_trading_day": is_trading_day_value,
            "is_trading_hours": is_trading_hours_value,
            "status": status_description,
            "next_trading_time": next_trading_time.strftime("%Y-%m-%d %H:%M:%S"),
            "next_trading_day": next_trading_day.strftime("%Y-%m-%d"),
            "time_to_next": time_to_next_str
        }

# 创建单例实例
trading_calendar = TradingCalendar.get_instance()

# 异步工厂函数，用于依赖注入
async def get_trading_calendar() -> TradingCalendar:
    """获取交易日历服务实例"""
    # 确保日历已加载
    if not trading_calendar._calendar_loaded and not trading_calendar._calendar_loading:
        await trading_calendar.load_calendar()
    return trading_calendar 