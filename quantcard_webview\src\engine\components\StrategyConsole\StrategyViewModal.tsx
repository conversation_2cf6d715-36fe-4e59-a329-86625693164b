/**
 * 📊 策略查看模态框组件
 * 展示策略执行历史、性能数据和详细信息
 */

import React, { useState, useEffect } from 'react'
import styled from 'styled-components'
import { motion, AnimatePresence } from 'framer-motion'
import { useStrategyState } from '../../../store/hooks'
import type { StrategyGroup } from '../../../types/game'

// 🎨 样式组件
const ModalOverlay = styled(motion.div)`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
`

const ModalContent = styled(motion.div)`
  background: #ffffff;
  border-radius: 20px;
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
`

const ModalHeader = styled.div`
  padding: 1.5rem;
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
`

const ModalTitle = styled.h2`
  margin: 0;
  font-size: 1.25rem;
  font-weight: 700;
  color: #1a202c;
`

const CloseButton = styled(motion.button)`
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 50%;
  background: #f8fafc;
  color: #64748b;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  
  &:hover {
    background: #e2e8f0;
  }
`

const ModalBody = styled.div`
  padding: 1.5rem;
  max-height: 60vh;
  overflow-y: auto;
`

const TabBar = styled.div`
  display: flex;
  margin-bottom: 1.5rem;
  background: #f8fafc;
  border-radius: 12px;
  padding: 4px;
`

const TabButton = styled(motion.button)<{ $active: boolean }>`
  flex: 1;
  padding: 0.75rem 1rem;
  border: none;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  background: ${props => props.$active ? '#667eea' : 'transparent'};
  color: ${props => props.$active ? 'white' : '#64748b'};
  
  &:hover {
    background: ${props => props.$active ? '#5a67d8' : 'rgba(102, 126, 234, 0.1)'};
  }
`

const LoadingState = styled.div`
  text-align: center;
  padding: 3rem 1rem;
  color: #64748b;
`

const EmptyState = styled.div`
  text-align: center;
  padding: 3rem 1rem;
  color: #64748b;
`

const HistoryList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
`

const HistoryItem = styled(motion.div)`
  background: #f8fafc;
  border-radius: 12px;
  padding: 1rem;
  border: 1px solid #e2e8f0;
`

const HistoryHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
`

const HistoryTime = styled.div`
  font-size: 0.875rem;
  color: #64748b;
  font-weight: 500;
`

const HistoryStatus = styled.span<{ $status: string }>`
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  
  ${props => {
    switch (props.$status) {
      case 'completed':
        return `
          background: #10b98120;
          color: #10b981;
        `
      case 'error':
        return `
          background: #ef444420;
          color: #ef4444;
        `
      default:
        return `
          background: #f59e0b20;
          color: #f59e0b;
        `
    }
  }}
`

const HistoryDetails = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 0.75rem;
  margin-top: 0.75rem;
`

const DetailItem = styled.div`
  text-align: center;
`

const DetailValue = styled.div`
  font-size: 1rem;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 0.25rem;
`

const DetailLabel = styled.div`
  font-size: 0.75rem;
  color: #64748b;
`

// 🎮 组件属性
interface StrategyViewModalProps {
  isOpen: boolean
  onClose: () => void
  strategyId: string | null
}

// 🎮 策略查看模态框组件
function StrategyViewModal({ isOpen, onClose, strategyId }: StrategyViewModalProps) {
  const { groups, getGroupExecutionHistory } = useStrategyState()
  const [activeTab, setActiveTab] = useState<'history' | 'details'>('history')
  const [loading, setLoading] = useState(false)
  const [executionHistory, setExecutionHistory] = useState<any[]>([])
  
  const strategy = strategyId ? groups.find(g => g.id === strategyId) : null
  
  // 🔄 加载执行历史
  useEffect(() => {
    if (isOpen && strategyId && activeTab === 'history') {
      loadExecutionHistory()
    }
  }, [isOpen, strategyId, activeTab])
  
  const loadExecutionHistory = async () => {
    if (!strategyId) return
    
    setLoading(true)
    try {
      const history = await getGroupExecutionHistory(strategyId)
      setExecutionHistory(history)
    } catch (error) {
      console.error('加载执行历史失败:', error)
      setExecutionHistory([])
    } finally {
      setLoading(false)
    }
  }
  
  // 🎨 格式化时间
  const formatTime = (timeStr: string) => {
    try {
      return new Date(timeStr).toLocaleString('zh-CN', {
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    } catch {
      return timeStr
    }
  }
  
  // 🎨 格式化执行时长
  const formatDuration = (ms: number) => {
    if (!ms) return '0.00s'
    return (ms / 1000).toFixed(2) + 's'
  }
  
  if (!isOpen || !strategy) return null

  return (
    <AnimatePresence>
      <ModalOverlay
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        onClick={onClose}
      >
        <ModalContent
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.9, opacity: 0 }}
          onClick={(e) => e.stopPropagation()}
        >
          <ModalHeader>
            <ModalTitle>📊 {strategy.name}</ModalTitle>
            <CloseButton
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={onClose}
            >
              ✕
            </CloseButton>
          </ModalHeader>
          
          <ModalBody>
            <TabBar>
              <TabButton
                $active={activeTab === 'history'}
                onClick={() => setActiveTab('history')}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                📈 执行历史
              </TabButton>
              <TabButton
                $active={activeTab === 'details'}
                onClick={() => setActiveTab('details')}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                📋 策略详情
              </TabButton>
            </TabBar>
            
            {activeTab === 'history' && (
              <>
                {loading ? (
                  <LoadingState>
                    <div style={{ fontSize: '2rem', marginBottom: '1rem' }}>⏳</div>
                    <div>加载执行历史中...</div>
                  </LoadingState>
                ) : executionHistory.length === 0 ? (
                  <EmptyState>
                    <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>📊</div>
                    <div>暂无执行历史</div>
                  </EmptyState>
                ) : (
                  <HistoryList>
                    {executionHistory.map((item, index) => (
                      <HistoryItem
                        key={item.id || index}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: index * 0.1 }}
                      >
                        <HistoryHeader>
                          <HistoryTime>
                            {formatTime(item.execution_time)}
                          </HistoryTime>
                          <HistoryStatus $status={item.status}>
                            {item.status === 'completed' ? '✅ 完成' : 
                             item.status === 'error' ? '❌ 失败' : '⏳ 处理中'}
                          </HistoryStatus>
                        </HistoryHeader>
                        
                        <HistoryDetails>
                          <DetailItem>
                            <DetailValue>{item.signals_count || 0}</DetailValue>
                            <DetailLabel>信号数量</DetailLabel>
                          </DetailItem>
                          <DetailItem>
                            <DetailValue>{formatDuration(item.execution_duration)}</DetailValue>
                            <DetailLabel>执行时长</DetailLabel>
                          </DetailItem>
                        </HistoryDetails>
                      </HistoryItem>
                    ))}
                  </HistoryList>
                )}
              </>
            )}
            
            {activeTab === 'details' && (
              <div>
                <DetailItem style={{ marginBottom: '1rem' }}>
                  <DetailLabel>策略描述</DetailLabel>
                  <DetailValue style={{ textAlign: 'left', marginTop: '0.5rem' }}>
                    {strategy.description || '暂无描述'}
                  </DetailValue>
                </DetailItem>
                
                <HistoryDetails>
                  <DetailItem>
                    <DetailValue>{strategy.group_type === 'timing' ? '择时策略' : '选股策略'}</DetailValue>
                    <DetailLabel>策略类型</DetailLabel>
                  </DetailItem>
                  <DetailItem>
                    <DetailValue>{strategy.execution_mode === 'sequential' ? '串行' : '并行'}</DetailValue>
                    <DetailLabel>执行模式</DetailLabel>
                  </DetailItem>
                  <DetailItem>
                    <DetailValue>{strategy.cards?.length || 0}</DetailValue>
                    <DetailLabel>卡片数量</DetailLabel>
                  </DetailItem>
                  <DetailItem>
                    <DetailValue>{strategy.status === 'active' ? '运行中' : '已停止'}</DetailValue>
                    <DetailLabel>当前状态</DetailLabel>
                  </DetailItem>
                </HistoryDetails>
              </div>
            )}
          </ModalBody>
        </ModalContent>
      </ModalOverlay>
    </AnimatePresence>
  )
}

export default StrategyViewModal
