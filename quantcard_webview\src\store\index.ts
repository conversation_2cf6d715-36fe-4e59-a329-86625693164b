/**
 * 🏗�?Store组合�?- 整合所有切片的统一Store
 * 使用Zustand的slice模式，结合subscribeWithSelector中间�? * 移除immer中间件以提高性能并支持Map/Set数据结构
 */

import { create } from 'zustand'
import { subscribeWithSelector } from 'zustand/middleware'

import type { RootStore } from './types/store'
import { createGameSlice } from './slices/gameSlice'
import { createCardsSlice } from './slices/cardsSlice'
import { createUISlice } from './slices/uiSlice'
import { createStrategySlice } from './slices/strategySlice'
import { createAuthSlice } from './slices/authSlice'

// 🎯 创建根Store，集成所有切片
export const useStore = create<RootStore>()(
  subscribeWithSelector((...args) => ({
    // 🎮 游戏切片
    ...createGameSlice(...args),
    
    // 🎒 卡牌切片
    ...createCardsSlice(...args),
    
    // 🎨 UI切片
    ...createUISlice(...args),
    
    // 🏗�?策略切片
    ...createStrategySlice(...args),
    
    // 🔐 认证切片
    ...createAuthSlice(...args)
  }))
)

// 🎯 Store类型导出
export type { RootStore } from './types/store'

// 🔧 Store选择器工具
export const createSelector = <T>(selector: (store: RootStore) => T) => {
  return (store: RootStore) => selector(store)
}

// 🚀 Store初始化函数
export const initializeStore = async (userId: string) => {
  const store = useStore.getState()
  
  try {
    // 并行初始化各个模块
    await Promise.all([
      store.initializeSession(userId),
      store.loadInventory(),
      store.loadGroups()
    ])
    
  } catch (error) {
    console.error('应用初始化失败', error)
  }
}

// 🔄 Store重置函数
export const resetStore = () => {
  const store = useStore.getState()
  store.resetGameState()
  store.resetCardsState()
  store.resetUIState()
  store.resetStrategyState()
}
