"""
增强认证系统
支持用户认证、访客模式、WebSocket认证和权限管理
"""
import json
import uuid
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List
from jose import jwt, JWTError
from passlib.context import CryptContext
from fastapi import HTTPException, status
from pydantic import BaseModel

from .config import settings
from ..models.user import User

# 密码加密上下文
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

class TokenPayload(BaseModel):
    """Token载荷模型"""
    sub: str  # 用户ID
    exp: float  # 过期时间
    iat: float  # 签发时间
    type: str = "access"  # Token类型
    is_guest: bool = False  # 是否为访客

class GuestSession(BaseModel):
    """访客会话模型"""
    guest_id: str
    created_at: datetime
    expires_at: datetime
    permissions: List[str] = ["browse", "view_worldmap"]
    session_data: Dict[str, Any] = {}

class AuthManager:
    """认证管理器"""
    
    def __init__(self):
        self.guest_sessions: Dict[str, GuestSession] = {}
        
    def create_access_token(self, data: dict, expires_delta: Optional[timedelta] = None, is_guest: bool = False) -> str:
        """创建访问令牌"""
        to_encode = data.copy()
        
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            # 访客Token有效期较短
            expire_minutes = 60 if is_guest else settings.ACCESS_TOKEN_EXPIRE_MINUTES
            expire = datetime.utcnow() + timedelta(minutes=expire_minutes)
            
        to_encode.update({
            "exp": expire.timestamp(),
            "iat": datetime.utcnow().timestamp(),
            "type": "access",
            "is_guest": is_guest
        })
        
        encoded_jwt = jwt.encode(
            to_encode,
            settings.SECRET_KEY,
            algorithm=settings.ALGORITHM
        )
        return encoded_jwt

    def verify_token(self, token: str) -> TokenPayload:
        """验证令牌"""
        try:
            payload = jwt.decode(
                token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM]
            )
            
            token_data = TokenPayload(
                sub=payload.get("sub"),
                exp=payload.get("exp"),
                iat=payload.get("iat"),
                type=payload.get("type", "access"),
                is_guest=payload.get("is_guest", False)
            )
            
            # 检查是否过期
            if token_data.exp < datetime.utcnow().timestamp():
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Token已过期",
                    headers={"WWW-Authenticate": "Bearer"},
                )
                
            return token_data
            
        except JWTError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的认证凭据",
                headers={"WWW-Authenticate": "Bearer"},
            )

    async def authenticate_user(self, username: str, password: str) -> Optional[User]:
        """用户名密码认证"""
        user = await User.get_by_username(username)
        if not user:
            return None
            
        if not user.check_password(password):
            return None
            
        if not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="用户已被禁用"
            )
            
        return user

    def create_guest_session(self, guest_id: Optional[str] = None) -> GuestSession:
        """创建访客会话"""
        if not guest_id:
            guest_id = f"guest_{uuid.uuid4().hex[:8]}"
            
        session = GuestSession(
            guest_id=guest_id,
            created_at=datetime.utcnow(),
            expires_at=datetime.utcnow() + timedelta(hours=2),  # 访客会话2小时过期
            permissions=["browse", "view_worldmap", "view_strategies"],
            session_data={}
        )
        
        self.guest_sessions[guest_id] = session
        return session

    def get_guest_session(self, guest_id: str) -> Optional[GuestSession]:
        """获取访客会话"""
        session = self.guest_sessions.get(guest_id)
        if session and session.expires_at > datetime.utcnow():
            return session
        elif session:
            # 清理过期会话
            del self.guest_sessions[guest_id]
        return None

    def update_guest_session(self, guest_id: str, session_data: Dict[str, Any]) -> bool:
        """更新访客会话数据"""
        session = self.get_guest_session(guest_id)
        if session:
            session.session_data.update(session_data)
            return True
        return False

    def cleanup_expired_guest_sessions(self):
        """清理过期访客会话"""
        current_time = datetime.utcnow()
        expired_sessions = [
            guest_id for guest_id, session in self.guest_sessions.items()
            if session.expires_at <= current_time
        ]
        
        for guest_id in expired_sessions:
            del self.guest_sessions[guest_id]

    async def get_user_from_token(self, token: str) -> Dict[str, Any]:
        """从Token获取用户信息"""
        token_payload = self.verify_token(token)
        
        if token_payload.is_guest:
            # 访客用户
            session = self.get_guest_session(token_payload.sub)
            if not session:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="访客会话已过期"
                )
                
            return {
                "id": session.guest_id,
                "username": f"访客_{session.guest_id[-8:]}",
                "is_guest": True,
                "is_active": True,
                "is_superuser": False,
                "permissions": session.permissions,
                "session_data": session.session_data,
                "created_at": session.created_at,
                "expires_at": session.expires_at
            }
        else:
            # 正常用户
            user = await User.get_by_id(token_payload.sub)
            if not user:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="用户不存在"
                )
                
            return {
                "id": user.id,
                "username": user.username,
                "email": user.email,
                "is_guest": False,
                "is_active": user.is_active,
                "is_superuser": user.is_superuser,
                "created_at": user.created_at,
                "updated_at": user.updated_at,
                "last_login": user.last_login,
                "avatar": user.avatar,
                "favorites": user.favorites
            }

    def check_permission(self, user: Dict[str, Any], required_permission: str) -> bool:
        """检查用户权限"""
        if user.get("is_guest"):
            # 访客权限检查
            permissions = user.get("permissions", [])
            return required_permission in permissions
        else:
            # 正常用户权限检查
            if user.get("is_superuser"):
                return True
                
            # 正常用户默认权限
            default_permissions = [
                "browse", "view_worldmap", "view_strategies", 
                "use_strategies", "create_strategies", "manage_inventory"
            ]
            return required_permission in default_permissions

    def require_permission(self, required_permission: str):
        """权限装饰器工厂"""
        def permission_decorator(func):
            async def wrapper(*args, **kwargs):
                # 获取当前用户（需要在参数中查找）
                current_user = None
                for arg in args:
                    if isinstance(arg, dict) and "id" in arg:
                        current_user = arg
                        break
                
                if not current_user:
                    for value in kwargs.values():
                        if isinstance(value, dict) and "id" in value:
                            current_user = value
                            break
                
                if not current_user:
                    raise HTTPException(
                        status_code=status.HTTP_401_UNAUTHORIZED,
                        detail="需要认证"
                    )
                
                if not self.check_permission(current_user, required_permission):
                    if current_user.get("is_guest"):
                        raise HTTPException(
                            status_code=status.HTTP_403_FORBIDDEN,
                            detail="访客权限不足，请登录后再试"
                        )
                    else:
                        raise HTTPException(
                            status_code=status.HTTP_403_FORBIDDEN,
                            detail="权限不足"
                        )
                
                return await func(*args, **kwargs)
            return wrapper
        return permission_decorator

# 全局认证管理器实例
auth_manager = AuthManager()

# 辅助函数
def create_access_token(data: dict, expires_delta: Optional[timedelta] = None, is_guest: bool = False) -> str:
    """创建访问令牌（向后兼容）"""
    return auth_manager.create_access_token(data, expires_delta, is_guest)

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """验证密码"""
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    """生成密码哈希"""
    return pwd_context.hash(password)

async def authenticate_user(username: str, password: str) -> Optional[User]:
    """用户认证（向后兼容）"""
    return await auth_manager.authenticate_user(username, password) 