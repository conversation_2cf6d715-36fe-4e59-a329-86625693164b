"""
策略执行器模块

负责单个策略的加载和执行
确保策略的执行环境一致
"""
import logging
from typing import Dict, Any, Optional
from datetime import datetime, timedelta

from .types import RuntimeContext, ExecutionResult, StrategyMode
from .template_service import TemplateService
from .utils import strategy_error_handler
from ...services.timing_data_manager import TimingDataManager

logger = logging.getLogger(__name__)

class StrategyExecutor:
    """
    策略执行器类
    
    负责加载和执行单个策略
    处理策略的输入和输出
    """
    
    def __init__(self):
        """初始化策略执行器"""
        self.template_service = TemplateService()
        self.timing_data_manager = TimingDataManager.get_instance()
    
    @strategy_error_handler(strategy_id="strategy_executor")
    async def execute_strategy(
        self,
        strategy_id: str,
        parameters: Dict[str, Any],
        mode: str = "FILTER",
        user_id: Optional[str] = None,
        debug: bool = False
    ) -> ExecutionResult:
        """
        执行单个策略
        
        Args:
            strategy_id: 策略ID
            parameters: 策略参数
            mode: 执行模式（FILTER、BACKTEST、TIMING）
            user_id: 用户ID
            debug: 是否启用调试模式
            
        Returns:
            ExecutionResult: 策略执行结果
        """
        # 创建运行时上下文
        template = await self.template_service.find_template(strategy_id)
        context = RuntimeContext(
            strategy_id=strategy_id,
            user_id=user_id or "system",
            name=template.name,
            type=template.type,
            description=template.description,
            parameters=parameters,
            mode=mode,
            debug=debug
        )
        
        # 执行策略
        return await self._run_strategy(context)
    
    async def _run_strategy(self, ctx: RuntimeContext) -> ExecutionResult:
        """
        执行策略的核心方法 - 精简版
        
        Args:
            ctx: 运行时上下文
            
        Returns:
            ExecutionResult: 策略执行结果
        """
        start_time = datetime.utcnow().timestamp()
        logger.info(f"开始执行策略: ID={ctx.strategy_id}, 名称={ctx.name}, 模式={ctx.mode}")
        
        try:
            # 1. 获取策略模板
            template = await self.template_service.find_template(ctx.strategy_id, ctx.name)
            
            # 2. 根据模式处理数据 - 简化逻辑，统一数据获取路径
            if ctx.mode == StrategyMode.TIMING and not ctx.data_cache.get("kline_data"):
                # 生成缓存ID，包含策略ID和周期信息以确保不同周期使用不同缓存
                period = ctx.metadata.get("group_kline_period") or ctx.parameters.get("kline_period", "5min")
                group_id = f"{ctx.metadata.get('group_id', ctx.strategy_id)}_{period}"
                
                # 统一的数据准备路径，无论是否已初始化
                ctx.metadata["cache_group_id"] = group_id
                await self._prepare_timing_data(ctx)
            
            # 3. 加载策略类
            strategy_class = template.load_strategy_class()
            if not strategy_class:
                raise ValueError(f"加载策略类失败: {template.name}")
            
            # 4. 实例化策略并执行
            strategy = strategy_class(ctx)
            signals = await strategy.execute(ctx)
            
            # 5. 构建执行结果
            execution_time = (datetime.utcnow().timestamp() - start_time) * 1000
            return ExecutionResult(
                strategy_id=ctx.strategy_id,
                signals=signals,
                success=True,
                execution_time=execution_time,
                logs=ctx.logs if hasattr(ctx, 'logs') else []
            )
            
        except Exception as e:
            logger.error(f"策略执行失败: {str(e)}", exc_info=True)
            raise
            
    async def _prepare_timing_data(self, ctx: RuntimeContext):
        """
        为择时策略准备数据 - 优化版本

        Args:
            ctx: 运行时上下文
        """
        logger.info("准备择时策略所需的数据")
        
        # 提取标的列表
        symbols = []
        if ctx.metadata.get("timing_symbols"):
            symbols = [s.strip() for s in ctx.metadata["timing_symbols"].split(",") if s.strip()]
        elif ctx.metadata.get("previous_signals"):
            symbols = [signal.symbol for signal in ctx.metadata["previous_signals"] 
                      if hasattr(signal, 'symbol') and signal.symbol]
        
        if not symbols:
            error_msg = "未指定择时标的，无法执行择时策略"
            ctx.logs.append(f"错误: {error_msg}")
            raise ValueError(error_msg)
        
        # 获取数据参数
        period = ctx.metadata.get("group_kline_period") or ctx.parameters.get("kline_period", "5min")
        days = int(ctx.parameters.get("lookback_days", 5))
        group_id = ctx.metadata.get("cache_group_id") or f"{ctx.metadata.get('group_id', ctx.strategy_id)}_{period}"
        
        # 统一数据获取入口 - 判断是否为调试模式
        force_update = ctx.debug  # 调试模式下强制刷新
        ctx.data_cache["kline_data"] = await self.timing_data_manager.get_or_update_group_data(
            group_id=group_id,
            symbols=symbols[:10],  # 限制标的数量
            period=period,
            days=days,
            force_update=force_update
        )
        
        # 根据K线周期设置合适的TTL
        period_min = int(period.replace('min', '')) if 'min' in period and period.replace('min', '').isdigit() else 5
        
        # 调试模式下设置TTL，与K线周期匹配
        if ctx.debug:
            # 调试模式下使用较短时间，但至少与K线周期相同
            self.timing_data_manager.set_group_ttl(group_id, minutes=period_min)
        else:
            # 非调试模式下设置更长的TTL，为K线周期的3倍，避免频繁重新获取数据
            self.timing_data_manager.set_group_ttl(group_id, minutes=period_min * 3)
        
        # 记录日志
        symbols_count = len(ctx.data_cache["kline_data"])
        if symbols_count == 0:
            warning_msg = "未能获取任何标的的K线数据，请检查数据源"
            ctx.logs.append(warning_msg)
            logger.warning(warning_msg)
            raise ValueError(warning_msg)
        else:
            from_time = datetime.now() - timedelta(days=days)
            to_time = datetime.now()
            ctx.logs.append(f"已成功加载 {symbols_count} 个标的的择时数据，周期={period}，时间范围={from_time.date()}至{to_time.date()}")
 