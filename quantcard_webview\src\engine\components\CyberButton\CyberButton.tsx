/**
 * 🎮 UnifiedCyberButton - 统一赛博朋克风格按钮组件（最小可运行版本）
 */

import React from 'react'
import { motion } from 'framer-motion'
import styled, { css, keyframes } from 'styled-components'
import { buildTheme } from '../../../styles/themes/dual-theme'

const theme = buildTheme('dark')

const neonPulse = keyframes`
  0%, 100% { box-shadow: 0 0 10px currentColor; }
  50% { box-shadow: 0 0 20px currentColor; }
`

export interface UnifiedCyberButtonProps {
  children: React.ReactNode
  variant?: 'primary' | 'secondary' | 'ghost' | 'danger' | 'legendary'
  size?: 'sm' | 'md' | 'lg' | 'xl'
  disabled?: boolean
  loading?: boolean
  fullWidth?: boolean
  simplified?: boolean
  glowEffect?: boolean
  soundEffect?: boolean
  particleEffect?: boolean
  onClick?: (e: React.MouseEvent<HTMLButtonElement>) => void
  className?: string
  type?: 'button' | 'submit' | 'reset'
  icon?: React.ReactNode
  iconPosition?: 'left' | 'right'
}

export type CyberButtonProps = UnifiedCyberButtonProps
export type SimpleCyberButtonProps = UnifiedCyberButtonProps

const getVariantStyles = (variant: UnifiedCyberButtonProps['variant'], simplified = false) => {
  const v = {
    primary: css`
      background: linear-gradient(135deg, ${theme.colors.primaryColors.cyan} 0%, ${theme.colors.primaryColors.magenta} 100%);
      color: ${theme.colors.neutral.white};
      border: 2px solid ${theme.colors.primaryColors.cyan};
      &:hover:not(:disabled) {
        ${simplified ? css`box-shadow: 0 0 15px ${theme.colors.primaryColors.cyan}60;` : css`animation: ${neonPulse} 2s ease-in-out infinite;`}
      }
    `,
    secondary: css`
      background: transparent;
      color: ${theme.colors.primaryColors.cyan};
      border: 2px solid ${theme.colors.primaryColors.cyan};
      &:hover:not(:disabled) { box-shadow: 0 0 10px ${theme.colors.primaryColors.cyan}40; }
    `,
    ghost: css`
      background: rgba(255,255,255,0.05);
      color: ${theme.colors.neutral.lightGray};
      border: 1px solid rgba(255,255,255,0.2);
      &:hover:not(:disabled) { background: rgba(255,255,255,0.1); color: ${theme.colors.neutral.white}; }
    `,
    danger: css`
      background: linear-gradient(135deg, ${theme.colors.semantic.error} 0%, #DC2626 100%);
      color: ${theme.colors.neutral.white};
      border: 2px solid ${theme.colors.semantic.error};
    `,
    legendary: css`
      background: ${theme.colors.gradients.legendary};
      color: ${theme.colors.neutral.white};
      border: 2px solid ${theme.colors.rarity.legendary};
      font-weight: ${theme.typography.weights.bold};
      &:hover:not(:disabled) { box-shadow: 0 0 30px ${theme.colors.rarity.legendary}60; }
    `,
  }
  return v[variant || 'primary']
}

const getSizeStyles = (size: UnifiedCyberButtonProps['size']) => {
  const s = {
    sm: css`padding: 0.4rem 0.8rem; font-size: ${theme.typography.sizes.sm}; min-height: 2rem;`,
    md: css`padding: 0.6rem 1rem; font-size: ${theme.typography.sizes.base}; min-height: 2.5rem;`,
    lg: css`padding: 0.8rem 1.2rem; font-size: ${theme.typography.sizes.lg}; min-height: 3rem;`,
    xl: css`padding: 1rem 1.4rem; font-size: ${theme.typography.sizes.xl}; min-height: 3.5rem;`,
  }
  return s[size || 'md']
}

const StyledButton = styled(motion.button)<{
  $variant: UnifiedCyberButtonProps['variant']
  $size: UnifiedCyberButtonProps['size']
  $fullWidth: boolean
  $glowEffect: boolean
  $simplified: boolean
}>`
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-family: ${theme.typography.fonts.primary};
  font-weight: ${theme.typography.weights.medium};
  letter-spacing: 0.08em;
  border-radius: ${theme.spacing.borderRadius.lg};
  cursor: pointer;
  user-select: none;
  overflow: hidden;
  transition: all 0.25s ease;
  width: ${p => (p.$fullWidth ? '100%' : 'auto')};
  ${p => getVariantStyles(p.$variant, p.$simplified)}
  ${p => getSizeStyles(p.$size)}

  &:disabled { opacity: 0.5; cursor: not-allowed; }
`

const UnifiedCyberButton: React.FC<UnifiedCyberButtonProps> = ({
  children,
  variant = 'primary',
  size = 'md',
  disabled = false,
  loading = false,
  fullWidth = false,
  simplified = false,
  glowEffect = true,
  onClick,
  className,
  type = 'button',
  icon,
  iconPosition = 'left',
  ...props
}) => {
  return (
    <StyledButton
      $variant={variant}
      $size={size}
      $fullWidth={fullWidth}
      $glowEffect={glowEffect}
      $simplified={simplified}
      disabled={disabled}
      onClick={onClick}
      className={className}
      type={type}
      whileHover={{ scale: disabled ? 1 : 1.03 }}
      whileTap={{ scale: disabled ? 1 : 0.97 }}
      {...props}
    >
      {icon && iconPosition === 'left' && <span>{icon}</span>}
      <span>{loading ? 'Loading...' : children}</span>
      {icon && iconPosition === 'right' && <span>{icon}</span>}
    </StyledButton>
  )
}

export const CyberButton = UnifiedCyberButton
export const SimpleCyberButton = (props: SimpleCyberButtonProps) => (
  <UnifiedCyberButton {...props} simplified={true} />
)

export default UnifiedCyberButton
export type { UnifiedCyberButtonProps }