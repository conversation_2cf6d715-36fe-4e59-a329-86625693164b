import React from 'react';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { SortableStrategyCard } from './SortableStrategyCard';
import { StrategyCardInstance } from '../../types/api';
import './DraggableStrategyList.scss';

interface DraggableStrategyListProps {
  items: StrategyCardInstance[];
  onItemsChange: (items: StrategyCardInstance[]) => void;
  executionMode: 'sequential' | 'parallel';
  onRemove: (index: number) => void;
  onEdit: (cardId: string, values: Record<string, any>) => void;
}

const DraggableStrategyList: React.FC<DraggableStrategyListProps> = ({
  items,
  onItemsChange,
  executionMode,
  onRemove,
  onEdit,
}) => {
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (over && active.id !== over.id) {
      const oldIndex = items.findIndex((item) => item.id === active.id);
      const newIndex = items.findIndex((item) => item.id === over.id);
      
      onItemsChange(arrayMove(items, oldIndex, newIndex));
    }
  };

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={closestCenter}
      onDragEnd={handleDragEnd}
    >
      <div className="draggable-strategy-list">
        <SortableContext
          items={items.map(item => ({ id: item.id }))}
          strategy={verticalListSortingStrategy}
        >
          {items.map((item, index) => (
            <SortableStrategyCard
              key={item.id}
              index={index}
              item={item}
              isSequential={executionMode === 'sequential'}
              onRemove={() => onRemove(index)}
              onEdit={(values) => onEdit(item.id, values)}
              total={items.length}
            />
          ))}
        </SortableContext>
      </div>
    </DndContext>
  );
};

export default DraggableStrategyList; 
 