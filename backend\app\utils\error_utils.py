from typing import Optional, Dict, Any
from fastapi import HTTPException
import logging

logger = logging.getLogger(__name__)

class APIError(Exception):
    """API错误基类"""
    def __init__(
        self,
        message: str,
        status_code: int = 400,
        error_code: str = "UNKNOWN_ERROR",
        details: Optional[Dict[str, Any]] = None
    ):
        self.message = message
        self.status_code = status_code
        self.error_code = error_code
        self.details = details or {}
        super().__init__(self.message)

class ErrorHandler:
    """错误处理工具类"""
    
    @staticmethod
    def raise_invalid_id(entity: str = "对象", id_value: str = "") -> None:
        """抛出无效ID错误"""
        raise APIError(
            message=f"无效的{entity}ID: {id_value}",
            status_code=400,
            error_code="INVALID_ID"
        )

    @staticmethod
    def raise_not_found(entity: str = "对象", id_value: str = "") -> None:
        """抛出未找到错误"""
        raise APIError(
            message=f"未找到{entity}: {id_value}",
            status_code=404,
            error_code="NOT_FOUND"
        )

    @staticmethod
    def raise_operation_failed(operation: str, entity: str, details: Optional[Dict[str, Any]] = None) -> None:
        """抛出操作失败错误"""
        raise APIError(
            message=f"{operation}{entity}失败",
            status_code=500,
            error_code="OPERATION_FAILED",
            details=details
        )

    @staticmethod
    def handle_api_error(error: APIError) -> None:
        """处理API错误"""
        logger.error(
            f"API错误: {error.message}",
            extra={
                "error_code": error.error_code,
                "status_code": error.status_code,
                "details": error.details
            }
        )
        raise HTTPException(
            status_code=error.status_code,
            detail={
                "message": error.message,
                "error_code": error.error_code,
                "details": error.details
            }
        ) 