"""
认证相关路由
"""
from datetime import datetime, timedelta
from fastapi import APIRouter, Depends, HTTPException, status, Form
from fastapi.security import OAuth2PasswordRequestForm
from ....core.deps import create_access_token
from ....core.auth import auth_manager
from ....core.data.db.base import db_manager
from ....utils.response_utils import ResponseFormatter
from passlib.context import CryptContext
from ....schemas.user import UserCreate, UserResponse
import logging

router = APIRouter()
logger = logging.getLogger(__name__)

# 密码上下文
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

@router.post("/login")
async def login(
    username: str = Form(...),
    password: str = Form(...),
    remember: bool = Form(False)
):
    """用户登录"""
    try:
        logger.info(f"尝试登录用户: {username}")
        
        # 获取数据库连接
        db = await db_manager.get_mongodb_database("quantcard")
        
        # 查找用户
        user = await db.users.find_one({"username": username})
        if not user or not pwd_context.verify(password, user["hashed_password"]):
            logger.warning(f"用户认证失败: {username}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户名或密码错误",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        # 检查用户状态
        if not user.get("is_active", True):
            logger.error(f"用户已被禁用: {username}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="用户已被禁用"
            )
        
        # 生成访问令牌
        access_token_expires = timedelta(hours=24 if remember else 0.5)
        
        # 使用用户ID作为subject
        user_id = str(user["_id"])  # 确保是字符串类型
        
        access_token = await create_access_token(
            data={"sub": user_id}, expires_delta=access_token_expires
        )
        
        # 更新最后登录时间
        await db.users.update_one(
            {"_id": user["_id"]},
            {"$set": {"last_login": datetime.utcnow()}}
        )
        
        logger.info(f"用户登录成功: {username}")
        
        # 返回登录成功的响应
        return {
            "success": True,
            "message": "登录成功",
            "data": {
                "access_token": access_token,
                "token_type": "bearer",
                "user": {
                    "id": user_id,
                    "username": user["username"],
                    "is_superuser": user.get("is_superuser", False),
                    "is_active": user.get("is_active", True),
                    "last_login": user.get("last_login"),
                    "created_at": user.get("created_at", datetime.utcnow()),
                    "updated_at": user.get("updated_at", datetime.utcnow()),
                    "favorites": user.get("favorites", [])
                }
            }
        }
        
    except HTTPException as e:
        logger.error(f"登录过程中出错: {str(e)}", exc_info=True)
        raise
    except Exception as e:
        logger.error(f"登录过程中出错: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"登录失败: {str(e)}"
        )

@router.post("/guest-login")
async def guest_login():
    """访客登录"""
    try:
        # 创建访客会话
        guest_session = auth_manager.create_guest_session()
        
        # 生成访客Token
        access_token = await auth_manager.create_access_token(
            data={"sub": guest_session.guest_id},
            is_guest=True
        )
        
        return {
            "success": True,
            "message": "访客登录成功",
            "data": {
                "access_token": access_token,
                "token_type": "bearer",
                "is_guest": True,
                "guest_id": guest_session.guest_id,
                "permissions": guest_session.permissions,
                "expires_at": guest_session.expires_at.isoformat(),
                "user": {
                    "id": guest_session.guest_id,
                    "username": f"访客_{guest_session.guest_id[-8:]}",
                    "is_guest": True,
                    "is_active": True,
                    "permissions": guest_session.permissions
                }
            }
        }
        
    except Exception as e:
        logger.error(f"访客登录失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"访客登录失败: {str(e)}"
        )

@router.post("/register", response_model=UserResponse)
async def register(user_data: UserCreate):
    """用户注册"""
    try:
        # 检查用户名是否已存在
        db = await db_manager.get_mongodb_database("quantcard")
        existing_user = await db.users.find_one({"username": user_data.username})
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="用户名已存在"
            )
        
        # 创建新用户
        hashed_password = pwd_context.hash(user_data.password)
        new_user = {
            "username": user_data.username,
            "hashed_password": hashed_password,
            "is_active": True,
            "is_superuser": False,
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow(),
            "favorites": []
        }
        
        result = await db.users.insert_one(new_user)
        new_user["_id"] = result.inserted_id
        
        logger.info(f"用户注册成功: {user_data.username}")
        
        return UserResponse(
            id=str(result.inserted_id),
            username=user_data.username,
            is_active=True,
            is_superuser=False,
            created_at=new_user["created_at"],
            updated_at=new_user["updated_at"]
        )
        
    except HTTPException as e:
        raise
    except Exception as e:
        logger.error(f"用户注册失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"注册失败: {str(e)}"
        )

@router.post("/logout")
async def logout():
    """用户登出"""
    # 由于使用JWT，登出主要在客户端处理
    return {
        "success": True,
        "message": "登出成功"
    }

@router.post("/refresh")
async def refresh_token():
    """刷新令牌"""
    # TODO: 实现令牌刷新逻辑
    return {
        "success": True,
        "message": "令牌刷新功能待实现"
    } 
 