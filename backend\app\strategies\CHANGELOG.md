# 策略框架变更日志

## 2023.XX.XX - 信号创建方式优化

### 新增功能

1. 在 `UnifiedStrategyCard` 基类中添加了 `_create_signal` 辅助方法，简化信号创建过程
2. 增强了 `StrategyRuntime._enhance_signals` 方法，自动添加股票基本信息
3. 添加了策略代码检测和迁移工具

### 移除的功能

1. 移除了策略卡中冗余的股票名称传递过程

### 迁移指南

- 使用 `_create_signal` 替代直接创建 `Signal` 对象
- 移除元数据中的 `name` 字段，它将由 `_enhance_signals` 自动添加
- 运行 `python -m app.cli.strategy_migration` 检测并生成迁移计划

### 示例

```python
# 旧代码
signals.append(Signal(
    id=f"{self.context.strategy_id}_{stock_code}_{now.timestamp()}",
    strategy_id=self.context.strategy_id,
    type=SignalType.FUNDAMENTAL,
    symbol=stock_code,
    direction="BUY",
    confidence=0.8,
    metadata={
        "name": stock_info["name"],  # 移除这个字段
        "some_key": "some_value"
    }
))

# 新代码
signals.append(self._create_signal(
    stock_code=stock_code,
    direction="BUY",
    metadata={
        "some_key": "some_value"
    }
))
``` 