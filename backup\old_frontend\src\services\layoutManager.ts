import { v4 as uuidv4 } from 'uuid';
import { DashboardWidget, WidgetTemplatesMap } from '../types/dashboard';
import { GridStack } from 'gridstack';
import { userLayoutApi } from './api';

/**
 * 小部件模板定义
 */
export const widgetTemplates: WidgetTemplatesMap = {
  'chart': { 
    w: 8, 
    h: 5,
    defaultTitle: '行情蜡烛图',
    defaultSettings: {
      symbol: 'BTC/USDT',
      interval: '1h'
    }
  },
  'heatmap': { 
    w: 4, 
    h: 4,
    defaultTitle: '市场热力图',
    defaultSettings: {
      marketType: 'crypto'
    }
  },
  'sentiment': { 
    w: 4, 
    h: 4,
    defaultTitle: '市场情绪',
    defaultSettings: {}
  },
  'announcements': { 
    w: 4, 
    h: 4,
    defaultTitle: '系统公告',
    defaultSettings: {}
  },
  'strategy-group': { 
    w: 10, 
    h: 3,
    defaultTitle: '我的策略组',
    defaultSettings: {}
  },
};

/**
 * 布局管理器服务
 */
export const LayoutManager = {
  /**
   * 生成部件的唯一ID
   * @param type 部件类型
   * @returns 新的唯一ID
   */
  generateWidgetId: (type: string): string => {
    // 生成前缀，确保同类型的部件使用类型名作为前缀
    const prefix = type;
    const uniqueId = uuidv4().substring(0, 8); // 只使用uuid的一部分，减少ID长度
    return `${prefix}-${uniqueId}`;
  },

  /**
   * 添加新部件到GridStack实例
   * @param gridInstance GridStack实例
   * @param widgetType 部件类型
   * @param title 部件标题
   * @param settings 部件设置
   * @returns HTMLElement | null 新添加的部件元素
   */
  addWidget: (
    gridInstance: GridStack | null, 
    widgetType: string, 
    title: string, 
    settings?: Record<string, any>
  ): { newWidget: HTMLElement | null, widgetId: string, containerId: string } => {
    if (!gridInstance) return { newWidget: null, widgetId: '', containerId: '' };
    
    // 生成唯一ID，使用类型作为前缀
    const widgetId = LayoutManager.generateWidgetId(widgetType);
    // 容器ID，用于渲染组件
    const containerId = `${widgetId}-container`;
    
    // 确定主题类名
    const themeClass = widgetType === 'sentiment' || widgetType === 'announcements' || widgetType === 'strategy-group' 
      ? 'theme-light' 
      : 'theme-dark';
    
    // 准备小部件属性
    const template = widgetTemplates[widgetType] || { w: 4, h: 4, defaultTitle: 'Widget', defaultSettings: {} };
    
    console.log('正在创建新部件，容器ID:', containerId);
    
    try {
      // 创建DOM元素
      const element = document.createElement('div');
      element.className = `grid-stack-item ${themeClass}`;
      element.setAttribute('data-widget-id', widgetId);
      element.setAttribute('data-widget-type', widgetType);
      
      // 设置内容
      element.innerHTML = `
        <div class="grid-stack-item-content">
          <div class="grid-stack-item-title">
            ${title}
            <div class="grid-stack-item-title-buttons">
              <span class="grid-stack-item-delete" title="删除">×</span>
            </div>
          </div>
          <div class="grid-stack-item-body" 
               id="${containerId}" 
               data-widget-type="${widgetType}"
               data-original-id="${widgetId}">
            <!-- ${widgetType}将在此渲染 -->
          </div>
        </div>
      `;
      
      // 首先使用makeWidget将DOM元素转换为GridStack widget
      gridInstance.makeWidget(element);
      
      // 设置位置和大小
      gridInstance.update(element, { 
        w: template.w, 
        h: template.h,
        autoPosition: true
      });
      
      // 获取新添加的部件的GridStack ID
      const gridStackId = element.getAttribute('gs-id');
      console.log(`GridStack分配的ID: ${gridStackId}，我们的容器ID: ${containerId}`);
      
      // 找到新部件中的容器元素，添加额外属性
      const bodyElement = element.querySelector('.grid-stack-item-body') as HTMLElement;
      if (bodyElement) {
        bodyElement.setAttribute('data-grid-id', gridStackId || '');
        
        // 保存组件配置
        if (settings) {
          bodyElement.setAttribute('data-settings', JSON.stringify(settings));
        }
      }
      
      // 添加删除按钮事件
      const deleteButton = element.querySelector('.grid-stack-item-delete') as HTMLElement;
      if (deleteButton) {
        deleteButton.addEventListener('click', (e) => {
          e.stopPropagation();
          gridInstance.removeWidget(element);
        });
      }
      
      console.log('部件添加成功，ID:', containerId);
      return { newWidget: element, widgetId, containerId };
    } catch (error) {
      console.error('添加GridStack部件失败:', error);
      return { newWidget: null, widgetId: '', containerId: '' };
    }
  },

  /**
   * 从DOM元素提取部件设置
   * @param bodyElement 部件的DOM元素
   * @returns 部件设置
   */
  getWidgetSettingsFromElement: (bodyElement: HTMLElement): Record<string, any> => {
    const settingsAttr = bodyElement.getAttribute('data-settings');
    if (settingsAttr) {
      try {
        return JSON.parse(settingsAttr);
      } catch (error) {
        console.error('解析部件设置失败:', error);
      }
    }
    
    const widgetType = bodyElement.getAttribute('data-widget-type') || '';
    return widgetTemplates[widgetType]?.defaultSettings || {};
  },

  /**
   * 保存当前布局到服务器
   * @param gridInstance GridStack实例
   * @returns Promise<boolean> 保存是否成功
   */
  saveLayout: async (gridInstance: GridStack | null): Promise<boolean> => {
    if (!gridInstance) return false;
    
    try {
      // 获取当前GridStack布局
      const currentLayout = gridInstance.save(true) as any[];
      
      console.log('获取到的GridStack布局:', currentLayout);
      
      // 打印所有GridStack项和对应的元素信息，用于调试
      console.log('当前DOM中的所有grid-stack-item-body元素:');
      const allBodyElements = document.querySelectorAll('.grid-stack-item-body');
      allBodyElements.forEach(el => {
        console.log(`- ID: ${el.id}, Type: ${el.getAttribute('data-widget-type')}, GridID: ${el.getAttribute('data-grid-id')}, OriginalID: ${el.getAttribute('data-original-id')}`);
      });
      
      // 将GridStack布局转换为DashboardWidget格式
      const dashboardLayout: DashboardWidget[] = [];
      
      // 遍历当前DOM中的所有grid-stack-item，确保我们获取正确的信息
      document.querySelectorAll('.grid-stack-item').forEach((gridItem: Element) => {
        const bodyElement = gridItem.querySelector('.grid-stack-item-body');
        const titleElement = gridItem.querySelector('.grid-stack-item-title');
        
        if (!bodyElement) {
          console.error('找不到grid-stack-item-body元素');
          return;
        }
        
        // 从元素属性获取信息
        const widgetType = bodyElement.getAttribute('data-widget-type') || 'chart';
        const originalId = bodyElement.getAttribute('data-original-id');
        const bodyElementId = bodyElement.id;
        
        // 从ID中提取实际ID (移除-container后缀)
        let widgetId = '';
        if (bodyElementId && bodyElementId.endsWith('-container')) {
          widgetId = bodyElementId.substring(0, bodyElementId.length - 10);
        } else if (originalId) {
          widgetId = originalId;
        } else {
          widgetId = widgetType + '-' + uuidv4().substring(0, 8);
        }
        
        // 获取位置和尺寸
        const x = parseInt(gridItem.getAttribute('gs-x') || '0');
        const y = parseInt(gridItem.getAttribute('gs-y') || '0');
        const w = parseInt(gridItem.getAttribute('gs-w') || '4');
        const h = parseInt(gridItem.getAttribute('gs-h') || '4');
        
        // 获取标题
        const title = titleElement?.textContent?.trim()
          ? titleElement.textContent.split('×')[0].trim() // 移除删除按钮
          : (widgetTemplates[widgetType]?.defaultTitle || 'Widget');
        
        // 获取组件配置
        const settings = LayoutManager.getWidgetSettingsFromElement(bodyElement as HTMLElement);
        
        // 创建仪表盘部件配置
        dashboardLayout.push({
          id: widgetId,
          x,
          y,
          w,
          h,
          title,
          component: widgetType,
          settings
        });
      });
      
      // 保存到服务器
      await userLayoutApi.saveUserLayout(dashboardLayout);
      return true;
    } catch (error) {
      console.error('保存布局失败:', error);
      return false;
    }
  },

  /**
   * 重置布局为默认布局
   * @param gridInstance GridStack实例
   * @returns Promise<boolean> 重置是否成功
   */
  resetLayout: async (gridInstance: GridStack | null): Promise<boolean> => {
    if (!gridInstance) return false;
    
    try {
      // 1. 清空当前网格
      gridInstance.removeAll();
      
      // 2. 尝试清除用户在服务器上保存的布局
      try {
        await userLayoutApi.saveUserLayout([]);
      } catch (error) {
        console.error('清除服务器布局失败，但会继续重置本地布局:', error);
      }
      
      return true;
    } catch (error) {
      console.error('重置布局失败:', error);
      return false;
    }
  }
}; 