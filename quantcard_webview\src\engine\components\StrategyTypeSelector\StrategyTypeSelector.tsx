/**
 * 🎯 策略类型选择器核心组件
 * 基于旧前端StrategyTypeSelector重构，采用游戏化UI设计
 */

import React, { useState } from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import GroupTypeCard from './GroupTypeCard';
import TimingSymbolsInput from './TimingSymbolsInput';
import type { StrategyTypeConfig, GroupTypeOption, KlinePeriodOption } from './types';

const SelectorContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 2rem;
`;

const SectionTitle = styled.h3`
  font-size: 1.2rem;
  font-weight: 600;
  color: #374151;
  margin: 0 0 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-family: "Inter", sans-serif;
`;

const TypeCardsGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  
  @media (max-width: 640px) {
    grid-template-columns: 1fr;
  }
`;

const TimingConfigSection = styled(motion.div)`
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
`;

const KlinePeriodSelector = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
`;

const KlinePeriodGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 0.5rem;
`;

const PeriodOption = styled(motion.button)<{ $selected: boolean }>`
  padding: 0.75rem;
  border-radius: 8px;
  border: 2px solid ${props => props.$selected ? '#3b82f6' : '#e5e7eb'};
  background: ${props => props.$selected ? 'rgba(59, 130, 246, 0.1)' : 'white'};
  color: ${props => props.$selected ? '#3b82f6' : '#6b7280'};
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  font-family: "Inter", sans-serif;
  
  &:hover {
    border-color: #3b82f6;
    background: rgba(59, 130, 246, 0.05);
  }
`;

const HelpIcon = styled.span`
  color: #9ca3af;
  font-size: 1rem;
  cursor: help;
`;

// 策略组类型选项配置
const GROUP_TYPE_OPTIONS: GroupTypeOption[] = [
  {
    value: 'filter',
    label: '选股策略',
    icon: '🔍',
    description: '在股票池中筛选符合条件的股票',
    color: '#10b981'
  },
  {
    value: 'timing',
    label: '择时策略', 
    icon: '⏱️',
    description: '决定特定股票的买入卖出时机',
    color: '#3b82f6'
  }
];

// K线周期选项配置
const KLINE_PERIOD_OPTIONS: KlinePeriodOption[] = [
  { value: '1min', label: '1分钟', description: '超短线交易' },
  { value: '5min', label: '5分钟', description: '短线交易' },
  { value: '15min', label: '15分钟', description: '日内交易' },
  { value: '30min', label: '30分钟', description: '日内交易' },
  { value: '60min', label: '1小时', description: '短期持仓' },
  { value: '1day', label: '日线', description: '中长期持仓' }
];

interface StrategyTypeSelectorProps {
  value?: StrategyTypeConfig;
  onChange?: (value: StrategyTypeConfig) => void;
}

const StrategyTypeSelector: React.FC<StrategyTypeSelectorProps> = ({
  value = { groupType: 'filter' },
  onChange,
}) => {
  const [groupType, setGroupType] = useState<'filter' | 'timing'>(value.groupType);
  const [timingSymbols, setTimingSymbols] = useState<string>(value.timingSymbols || '');
  const [klinePeriod, setKlinePeriod] = useState<string>(value.klinePeriod || '5min');

  const handleGroupTypeChange = (newGroupType: 'filter' | 'timing') => {
    setGroupType(newGroupType);
    onChange?.({ 
      groupType: newGroupType, 
      timingSymbols: newGroupType === 'timing' ? timingSymbols : undefined,
      klinePeriod: newGroupType === 'timing' ? klinePeriod : undefined
    });
  };

  const handleTimingSymbolsChange = (newTimingSymbols: string) => {
    setTimingSymbols(newTimingSymbols);
    onChange?.({ 
      groupType, 
      timingSymbols: newTimingSymbols,
      klinePeriod
    });
  };

  const handleKlinePeriodChange = (newKlinePeriod: string) => {
    setKlinePeriod(newKlinePeriod);
    onChange?.({
      groupType,
      timingSymbols,
      klinePeriod: newKlinePeriod
    });
  };

  return (
    <SelectorContainer>
      {/* 策略组类型选择 */}
      <div>
        <SectionTitle>
          策略组类型
          <HelpIcon title="选择策略组的类型，不同类型有不同的功能和应用场景">ℹ️</HelpIcon>
        </SectionTitle>
        <TypeCardsGrid>
          {GROUP_TYPE_OPTIONS.map(option => (
            <GroupTypeCard
              key={option.value}
              option={option}
              selected={groupType === option.value}
              onClick={() => handleGroupTypeChange(option.value)}
            />
          ))}
        </TypeCardsGrid>
      </div>

      {/* 择时策略专用配置 */}
      {groupType === 'timing' && (
        <TimingConfigSection
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
          transition={{ duration: 0.3 }}
        >
          {/* 择时标的输入 */}
          <div>
            <SectionTitle>
              择时标的
              <HelpIcon title="输入您要分析的股票代码，多个标的用逗号分隔">ℹ️</HelpIcon>
            </SectionTitle>
            <TimingSymbolsInput
              value={timingSymbols}
              onChange={handleTimingSymbolsChange}
            />
          </div>

          {/* K线周期选择 */}
          <KlinePeriodSelector>
            <SectionTitle>
              K线周期
              <HelpIcon title="选择策略组使用的统一K线周期，所有策略卡将使用相同周期">ℹ️</HelpIcon>
            </SectionTitle>
            <KlinePeriodGrid>
              {KLINE_PERIOD_OPTIONS.map(option => (
                <PeriodOption
                  key={option.value}
                  $selected={klinePeriod === option.value}
                  onClick={() => handleKlinePeriodChange(option.value)}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  title={option.description}
                >
                  {option.label}
                </PeriodOption>
              ))}
            </KlinePeriodGrid>
          </KlinePeriodSelector>
        </TimingConfigSection>
      )}
    </SelectorContainer>
  );
};

export default StrategyTypeSelector; 