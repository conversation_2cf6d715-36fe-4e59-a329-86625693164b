"""
十大股东筛选策略
"""
import logging
import pandas as pd
from typing import Dict, Any, List, Optional
from datetime import datetime
import yaml
import json
from pathlib import Path

from app.core.strategy.base import UnifiedStrategyCard
from app.core.runtime.types import RuntimeContext, Signal, SignalType
from app.core.runtime.types import StrategyMode
from app.core.data.db.base import db_manager
from sqlalchemy import text

logger = logging.getLogger(__name__)

# 从 template.json 加载配置
template_path = Path(__file__).parent / "template.json"
with open(template_path, "r", encoding="utf-8") as f:
    template = json.load(f)

class ShareholderTop10FilterStrategy(UnifiedStrategyCard):
    """十大股东筛选策略"""
    
    def __init__(self, context: Optional[RuntimeContext] = None):
        """初始化策略"""
        super().__init__(context)
        
        # 加载配置文件
        config_path = Path(__file__).parent / "config.yaml"
        with open(config_path, "r", encoding="utf-8") as f:
            self.config = yaml.safe_load(f)

    async def prepare_data(self, context: RuntimeContext) -> pd.DataFrame:
        """准备数据"""
        try:
            # 检查串行执行模式
            if not self._check_sequential_mode(context):
                return pd.DataFrame()
            
            # 获取模式配置 - 直接使用执行模式作为数据源配置
            mode = context.mode.value if context.mode else "filter"
            mode_config = self.config["data_sources"].get(mode, self.config["data_sources"]["filter"])
            
            # 获取股票代码列表
            symbols = self._get_stock_symbols(context)
            
            # 检查缓存
            cache_key = f"{self._get_cache_key(context)}_{mode}"
            cached_data = self._data_cache.get(cache_key)
            if cached_data is not None:
                if symbols and not cached_data.empty:
                    filtered_cache = cached_data[cached_data["股票代码"].isin(symbols)]
                    self._log(f"从缓存获取: {len(filtered_cache)}条数据")
                    return filtered_cache
                return cached_data
            
            # 获取数据
            db_type = mode_config["database"]["type"]
            if db_type == "postgresql":
                data = await self._get_data_from_postgresql(mode_config, symbols)
            else:
                self._log(f"不支持的数据库类型: {db_type}", "warning")
                return pd.DataFrame()
            
            if data.empty:
                return pd.DataFrame()
            
            # 缓存数据
            self._data_cache.set(cache_key, data, ttl=60)
            return data
            
        except Exception as e:
            self._log(f"准备数据失败: {str(e)}", "error")
            return pd.DataFrame()
    
    async def _get_data_from_postgresql(self, mode_config: Dict[str, Any], symbols: List[str]) -> pd.DataFrame:
        """从PostgreSQL获取数据"""
        try:
            if "database" not in mode_config or "table" not in mode_config["database"]:
                raise ValueError("配置错误：数据源未定义表名")
            
            table = mode_config["database"]["table"]
            fields = mode_config.get("fields", ["股东名称", "期末持股持股变动", "股东类型", "股票代码", "股东分类"])
            
            fields_str = ", ".join(fields)
            sql_query = f"SELECT {fields_str} FROM {table}"
            
            if symbols:
                placeholders = ", ".join([f"'{symbol}'" for symbol in symbols])
                sql_query += f" WHERE 股票代码 IN ({placeholders})"
            
            async with db_manager.get_session() as session:
                result = await session.execute(text(sql_query))
                rows = result.fetchall()
                return pd.DataFrame(rows, columns=fields)
                
        except Exception as e:
            self._log(f"从PostgreSQL获取数据失败: {str(e)}", "error")
            return pd.DataFrame()
            
    async def generate_signal(self, data: pd.DataFrame, params: Dict[str, Any]) -> List[Signal]:
        """生成信号"""
        try:
            if data.empty:
                return []
            
            # 获取参数
            name_operator = params.get("name_operator", "包含")
            name_keywords = params.get("name_keywords", "")
            holding_change = params.get("holding_change", "全部")
            shareholder_type = params.get("shareholder_type", "全部")
            
            # 处理关键字
            keywords = [kw.strip() for kw in name_keywords.split(",")] if name_keywords else []
            
            # 记录筛选条件
            filter_conditions = []
            if keywords:
                filter_conditions.append(f"股东名称{name_operator}[{name_keywords}]")
            if holding_change != "全部":
                filter_conditions.append(f"持股变动为[{holding_change}]")
            if shareholder_type != "全部":
                filter_conditions.append(f"股东类型为[{shareholder_type}]")
                
            self._log(f"筛选条件: {' AND '.join(filter_conditions) if filter_conditions else '无'}")
            
            # 筛选数据
            filtered_data = data.copy()
            
            # 1. 按股东名称筛选
            if keywords:
                if name_operator == "包含":
                    # 包含任意一个关键字
                    mask = filtered_data["股东名称"].str.contains("|".join(keywords), case=False, na=False)
                else:  # 不包含
                    # 不包含任何关键字
                    mask = ~filtered_data["股东名称"].str.contains("|".join(keywords), case=False, na=False)
                filtered_data = filtered_data[mask]
            
            # 2. 按持股变动筛选
            if holding_change != "全部":
                filtered_data = filtered_data[filtered_data["期末持股持股变动"] == holding_change]
            
            # 3. 按股东类型筛选
            if shareholder_type != "全部":
                filtered_data = filtered_data[filtered_data["股东类型"].str.contains(shareholder_type, case=False, na=False)]
            
            # 获取唯一的股票代码
            stock_codes = filtered_data["股票代码"].unique()
            
            self._log(f"筛选出 {len(stock_codes)}只股票")
            
            # 生成信号
            now = datetime.utcnow()
            signals = []
            
            for stock_code in stock_codes:
                signals.append(Signal(
                    id=f"{self.context.strategy_id}_{stock_code}_{now.timestamp()}",
                    strategy_id=self.context.strategy_id,
                    type=SignalType.FUNDAMENTAL,
                    symbol=stock_code,
                    direction="BUY",
                    confidence=0.75,
                    metadata={
                        "filter_conditions": filter_conditions,
                        "shareholder_count": len(filtered_data[filtered_data["股票代码"] == stock_code]),
                        "trigger_time": now.isoformat()
                    }
                ))
            
            return signals
            
        except Exception as e:
            self._log(f"生成信号失败: {str(e)}", "error")
            return [] 