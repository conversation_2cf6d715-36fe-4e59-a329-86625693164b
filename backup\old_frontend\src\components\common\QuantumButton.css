.quantum-button {
  border-radius: 9999px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  position: relative;
  overflow: hidden;
}

.quantum-button--primary {
  background: linear-gradient(45deg, #6366f1, #818cf8);
  border: none !important;
  color: #fff !important;
  box-shadow: 0 2px 4px rgba(99, 102, 241, 0.2);
}

.quantum-button--primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, #4f46e5, #6366f1);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.quantum-button--primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 0 20px rgba(99, 102, 241, 0.4);
  color: #fff !important;
  border: none !important;
}

.quantum-button--primary:hover::before {
  opacity: 1;
}

.quantum-button--primary > * {
  position: relative;
  z-index: 1;
}

.quantum-button--primary:active {
  transform: translateY(0);
  box-shadow: 0 0 10px rgba(99, 102, 241, 0.4);
}

.quantum-button--secondary {
  background: transparent;
  border: 1px solid #e2e8f0;
  color: #475569;
}

.quantum-button--secondary:hover {
  border-color: #6366f1 !important;
  color: #6366f1 !important;
}

.quantum-button--secondary:active {
  background: rgba(99, 102, 241, 0.05);
}

.quantum-button--outline {
  background: transparent;
  border: 2px solid #6366f1 !important;
  color: #6366f1;
}

.quantum-button--outline:hover {
  background: rgba(99, 102, 241, 0.05);
  transform: translateY(-2px);
  box-shadow: 0 0 20px rgba(99, 102, 241, 0.2);
  color: #6366f1 !important;
}

.quantum-button--outline:active {
  transform: translateY(0);
}

.quantum-button--small {
  height: 32px;
  padding: 0 12px;
  font-size: 14px;
}

.quantum-button--medium {
  height: 40px;
  padding: 0 16px;
  font-size: 16px;
}

.quantum-button--large {
  height: 48px;
  padding: 0 20px;
  font-size: 18px;
}

.quantum-button--full-width {
  width: 100%;
}

/* 覆盖 Ant Design 的默认样式 */
.quantum-button.ant-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.quantum-button.ant-btn:hover,
.quantum-button.ant-btn:focus {
  color: inherit;
  border-color: inherit;
}

.quantum-button.ant-btn-primary:hover {
  background: linear-gradient(45deg, #4f46e5, #6366f1);
}

/* 添加工具提示的样式 */
.quantum-button[title] {
  position: relative;
}

.quantum-button[title]:hover::after {
  content: attr(title);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  padding: 4px 8px;
  background: rgba(0, 0, 0, 0.75);
  color: white;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  margin-bottom: 8px;
  pointer-events: none;
  z-index: 1000;
} 