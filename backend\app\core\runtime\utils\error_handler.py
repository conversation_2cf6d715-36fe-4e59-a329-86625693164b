"""
错误处理工具模块
提供统一的异常处理机制和日志记录功能
"""
import logging
import functools
import asyncio
import gc
from datetime import datetime
from typing import Any, Dict, Optional, Callable, TypeVar, Awaitable, List

from ..types import ExecutionResult, Signal

logger = logging.getLogger(__name__)

T = TypeVar('T')

def strategy_error_handler(
    strategy_id: str = "unknown", 
    context_name: Optional[str] = None
) -> Callable:
    """
    策略错误处理装饰器
    
    捕获策略执行过程中的异常，并将其转换为标准格式的结果
    
    Args:
        strategy_id: 策略ID
        context_name: 上下文名称，用于错误信息
        
    Returns:
        包装后的函数
    """
    def decorator(func):
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            start_time = datetime.utcnow().timestamp()
            context = context_name or func.__name__
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                error_message = f"执行{context}失败: {str(e)}"
                logger.error(error_message, exc_info=True)
                
                # 直接返回标准格式的错误结果
                return {
                    "success": False,
                    "message": error_message,
                    "logs": [error_message],
                    "data": {
                        "signals": [],
                        "statistics": {"total": 0, "matched": 0, "ratio": "0%"},
                        "filterCondition": f"执行出错 - {strategy_id}",
                        "executionTime": datetime.utcnow().strftime("%Y/%m/%d %H:%M:%S")
                    }
                }
        return wrapper
    return decorator 