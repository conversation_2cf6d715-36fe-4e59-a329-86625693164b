from ..core.data.db.base import MongoModel
from ..core.runtime.types import Signal

class SignalDocument(Signal, MongoModel):
    """信号数据库模型
    继承自runtime.types.Signal，用于数据库存储
    """
    @classmethod
    def get_collection_name(cls) -> str:
        """获取集合名称"""
        return "signals"
        
    @classmethod
    def get_db_name(cls) -> str:
        """获取数据库名称"""
        return "quantcard"
        
    class Config:
        # 允许额外字段，因为我们继承了Signal类
        arbitrary_types_allowed = True
        
    def to_db_model(self) -> dict:
        """转换为数据库存储格式，复用Signal的方法"""
        return super().to_db_model()
