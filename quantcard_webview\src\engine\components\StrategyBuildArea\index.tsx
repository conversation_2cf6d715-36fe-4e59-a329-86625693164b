/**
 * 🎯 策略构建区组件 - Strategy Build Area
 * 独立的构建区组件，封装卡牌展示、拖拽处理、移除、参数编辑等功能
 */

import React, { useState, useCallback } from 'react';
import styled from 'styled-components';
import { motion, AnimatePresence } from 'framer-motion';
import { UniversalCard } from '../UniversalCard';
import type { UnifiedCardData } from '../UniversalCard';
import type { StrategyCardInstance, StrategyGroup } from '../../../types/game';
import { buildCoreSentence } from '../../../utils/templateSentence';
import AdvancedParameterModal from '../AdvancedParameterEditor/ParameterModal';
import type { StrategyParameter } from '../AdvancedParameterEditor/types';
import { fetchStrategyTemplate } from '../../../services/api/strategy';

// 🎴 策略卡片区域
const CardsArea = styled.div`
  min-height: clamp(260px, 40vh, 520px);
  border: 2px dashed rgba(59, 130, 246, 0.3);
  border-radius: 12px;
  padding: 0.75rem;
  background: rgba(59, 130, 246, 0.02);
  position: relative;
  transition: all 0.3s ease;
  
  &.drag-over {
    border-color: #3b82f6;
    background: rgba(59, 130, 246, 0.08);
    border-style: solid;
  }
`;

const CardsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(115px, 1fr));
  gap: 6px;
  min-height: 120px;
`;

const EmptyState = styled.div`
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #64748b;
  pointer-events: none;
  
  .icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    opacity: 0.7;
  }
  
  .title {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
  }
  
  .subtitle {
    font-size: 0.875rem;
    opacity: 0.8;
  }
`;

const ConfigCardWrapper = styled(motion.div)<{ $isSelected?: boolean }>`
  position: relative;
  cursor: pointer;
  
  ${props => props.$isSelected && `
    &::after {
      content: '';
      position: absolute;
      top: -4px;
      left: -4px;
      right: -4px;
      bottom: -4px;
      border: 2px solid #3b82f6;
      border-radius: 20px;
      background: rgba(59, 130, 246, 0.1);
      z-index: -1;
    }
  `}
`;

const CardAnchor = styled.div`
  position: relative;
  display: block;
`;

const CardRemoveButton = styled(motion.button)`
  position: absolute;
  top: 2px;
  right: 2px;
  background: #ef4444;
  border: 2px solid white;
  color: white;
  border-radius: 50%;
  width: 22px;
  height: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 0.75rem;
  font-weight: bold;
  z-index: 20;
  line-height: 1;
  
  &:hover {
    background: #dc2626;
    transform: scale(1.05);
  }
`;

export interface StrategyBuildAreaProps {
  currentGroup: StrategyGroup | null;
  templateCache: Record<string, any>;
  isDragOver: boolean;
  onCardAdd: (cardData: UnifiedCardData) => void;
  onCardRemove: (cardId: string) => void;
  onCardUpdate: (cardId: string, parameters: Record<string, any>) => void;
  onDragOver: (e: React.DragEvent) => void;
  onDragLeave: (e: React.DragEvent) => void;
  onDrop: (e: React.DragEvent) => void;
}

export const StrategyBuildArea: React.FC<StrategyBuildAreaProps> = ({
  currentGroup,
  templateCache,
  isDragOver,
  onCardAdd,
  onCardRemove,
  onCardUpdate,
  onDragOver,
  onDragLeave,
  onDrop
}) => {
  // 🎮 内部状态管理
  const [selectedCardId, setSelectedCardId] = useState<string | null>(null);
  const [editingCard, setEditingCard] = useState<StrategyCardInstance | null>(null);
  const [editingParameters, setEditingParameters] = useState<Record<string, any>>({});
  const [editingParamDefs, setEditingParamDefs] = useState<Record<string, StrategyParameter>>({});
  const [editingParamGroups, setEditingParamGroups] = useState<Record<string, any>>({});
  const [paramLoading, setParamLoading] = useState(false);
  const [paramError, setParamError] = useState<string | undefined>(undefined);

  // ⚙️ 处理参数编辑
  const handleEditCard = useCallback(async (card: StrategyCardInstance) => {
    try {
      setParamLoading(true);
      setParamError(undefined);
      setEditingCard(card);
      setSelectedCardId(card.id);

      // 优先使用模板缓存，避免重复请求
      let template = templateCache[card.template_id];
      if (!template) {
        // 如果缓存中没有，则从后端获取
        const tpl = await fetchStrategyTemplate(card.template_id);
        template = tpl?.data || tpl;
      }

      const paramsDef: Record<string, StrategyParameter> = template?.parameters || {};
      const paramGroups: Record<string, any> = template?.parameterGroups || {};

      // 初始值 = 现有参数或模板默认
      const values: Record<string, any> = {};
      Object.keys(paramsDef).forEach(k => {
        const def = paramsDef[k];
        values[k] = card.parameters?.[k] ?? (def as any).default ?? '';
      });

      setEditingParamDefs(paramsDef);
      setEditingParamGroups(paramGroups);
      setEditingParameters(values);
    } catch (error) {
      console.error('加载参数定义失败:', error);
      setParamError(error instanceof Error ? error.message : '加载参数失败');
    } finally {
      setParamLoading(false);
    }
  }, [templateCache]);

  // 🔄 处理参数确认
  const handleParameterConfirm = useCallback((values: Record<string, any>) => {
    if (editingCard) {
      setEditingParameters(values);
      onCardUpdate(editingCard.id, values);
      setEditingCard(null);
      setSelectedCardId(null);
    }
  }, [editingCard, onCardUpdate]);

  // 🚫 处理参数编辑取消
  const handleParameterCancel = useCallback(() => {
    setEditingCard(null);
    setSelectedCardId(null);
    setParamError(undefined);
  }, []);
  return (
    <>
      <CardsArea
        className={isDragOver ? 'drag-over' : ''}
        onDragOver={onDragOver}
        onDragLeave={onDragLeave}
        onDrop={onDrop}
      >
        {currentGroup && currentGroup.cards.length > 0 ? (
          <CardsGrid>
            <AnimatePresence mode="popLayout">
              {currentGroup.cards.map((card, index) => {
                // 使用模板缓存而不是库存数据
                const template = templateCache[card.template_id];
                if (!template) return null;

                const coreSentence = (() => {
                  try {
                    // 使用模板的参数定义和参数组
                    const params = template.parameters || {}
                    const groups = template.parameterGroups || {}
                    const values = card.parameters || {}
                    return buildCoreSentence(params, groups, values)
                  } catch (error) {
                    console.warn(`生成卡牌 ${card.template_id} 核心句式失败:`, error)
                    return template.description || ''
                  }
                })()

                const cardData: UnifiedCardData = {
                  ...template,
                  id: card.template_id,
                  coreSentence
                };

                return (
                  <motion.div
                    key={card.id}
                    initial={{ opacity: 0, scale: 0.8, y: 20 }}
                    animate={{ opacity: 1, scale: 1, y: 0 }}
                    exit={{ opacity: 0, scale: 0.8, y: -20 }}
                    transition={{ duration: 0.3, delay: index * 0.05 }}
                    layout
                  >
                    <ConfigCardWrapper
                      $isSelected={selectedCardId === card.id}
                      onClick={() => handleEditCard(card)}
                    >
                      <CardAnchor>
                        <UniversalCard
                          card={cardData}
                          variant="collection"
                          size="sm"
                          interactive={true}
                          showStats={true}
                          showLevel={true}
                        />
                        <CardRemoveButton
                          onClick={(e) => {
                            e.stopPropagation();
                            onCardRemove(card.id);
                          }}
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.9 }}
                        >
                          −
                        </CardRemoveButton>
                      </CardAnchor>
                    </ConfigCardWrapper>
                  </motion.div>
                );
              })}
            </AnimatePresence>
          </CardsGrid>
        ) : (
          <EmptyState>
            <div className="icon">🎴</div>
            <div className="title">拖拽卡片到此处</div>
            <div className="subtitle">从左侧卡包中选择策略卡片</div>
          </EmptyState>
        )}
      </CardsArea>

      {/* 参数编辑器 */}
      {!!editingCard && (
        <div>
          {paramError && (
            <div style={{color:'#ef4444', padding:'8px 12px'}}>
              参数加载失败：{paramError}
            </div>
          )}
          <AdvancedParameterModal
            open={!!editingCard}
            parameters={editingParamDefs}
            parameterGroups={editingParamGroups}
            values={editingParameters}
            onChange={(vals) => setEditingParameters(vals)}
            onClose={handleParameterCancel}
            onConfirm={handleParameterConfirm}
          />
        </div>
      )}
    </>
  );
};

export default StrategyBuildArea;
