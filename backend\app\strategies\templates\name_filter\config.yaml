data_sources:
  backtest:
    database:
      name: market_data
      schema: public
      table: stock_historical_quotes
      type: postgresql
    fields:
    - 代码
    - 名称
    frequency: 1d
    window_size: 1
  filter:
    database:
      name: stock_base_info
      schema: public
      table: stock_list_rt
      type: postgresql
    fields:
    - 代码
    - 名称
    frequency: tick
    window_size: 1
  monitor: &id001
    database:
      collection: stocks
      name: realtime_data
      table: stock_list_rt
      type: mongodb
    fields:
    - 代码
    - 名称
    frequency: 1m
    window_size: 2
  timing: *id001
description: 根据股票名称中包含或不包含的关键字进行筛选
name: 股票名称筛选策略
outputs:
  backtest:
    fields:
    - 代码
    - 名称
    - 命中时间
    - 信号类型
    type: backtest_result
  filter:
    fields:
    - 代码
    - 名称
    type: stock_list
  monitor: &id002
    fields:
    - 代码
    - 名称
    - 触发条件
    - 触发时间
    type: alert
  timing: *id002
strategy_class_name: NameFilterStrategy
version: 1.0.0
