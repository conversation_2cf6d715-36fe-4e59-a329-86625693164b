/**
 * 🎮 QuantCard Arena - 主应用组件
 * H5 游戏化量化交易平台 - 游戏优先架构
 */

import React from 'react'
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom'
import styled, { createGlobalStyle } from 'styled-components'
import { motion } from 'framer-motion'
import { DynamicThemeProvider, useTheme } from './styles/themes/ThemeProvider'
import GameShell from './engine/GameShell'
import SceneRouter from './engine/SceneRouter'
import { useAuth } from './store/hooks/useAuth'
import { WebSocketProvider } from './services/WebSocketProvider'

// 🌌 动态全局样式
const GlobalStyle = createGlobalStyle<{ theme: any }>`
  /* 🎨 现代化CSS重置 */
  *, *::before, *::after {
    box-sizing: border-box;
  }

  * {
    margin: 0;
    padding: 0;
  }

  html, body {
    height: 100%;
    line-height: 1.5;
    -webkit-font-smoothing: antialiased;
  }

  body {
    font-family: ${props => props.theme.typography.fonts.primary};
    background: ${props => props.theme.colors.background.void};
    color: ${props => props.theme.colors.neutral.charcoal};
    overflow-x: hidden;
    -webkit-text-size-adjust: 100%;
    -webkit-tap-highlight-color: transparent;
    transition: background-color 0.8s ease, color 0.8s ease;
  }

  #root {
    min-height: 100vh;
    isolation: isolate;
  }

  /* 🎯 滚动条样式 - 主题适配 */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: ${props => props.theme.colors.background.dark};
  }

  ::-webkit-scrollbar-thumb {
    background: ${props => props.theme.colors.primaryColors.cyan};
    border-radius: 4px;
    
    &:hover {
      background: ${props => props.theme.colors.primaryColors.magenta};
    }
  }

  /* 🎪 选择文本样式 */
  ::selection {
    background: ${props => props.theme.colors.primaryColors.cyan}40;
    color: ${props => props.theme.colors.neutral.white};
  }

  /* 🔗 链接样式 */
  a {
    color: ${props => props.theme.colors.primaryColors.cyan};
    text-decoration: none;
    transition: color 0.25s ease;
    
    &:hover {
      color: ${props => props.theme.colors.primaryColors.magenta};
    }
  }

  /* 🎮 按钮重置 */
  button {
    font-family: inherit;
    border: none;
    background: none;
    cursor: pointer;
  }

  /* 🎯 聚焦样式 */
  *:focus-visible {
    outline: 2px solid ${props => props.theme.colors.primaryColors.cyan};
    outline-offset: 2px;
  }

  /* 🌟 动画优化 */
  @media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }
`

// 🎪 应用容器 - 动态主题适配
const AppContainer = styled.div<{ theme: any }>`
  position: relative;
  min-height: 100vh;
  background: ${props => props.theme.colors.background.void};
  overflow-x: hidden;
  transition: background 0.8s ease;
`

// ⚡ 加载屏幕组件 - 深色主题
const LoadingScreen = styled(motion.div)`
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: linear-gradient(180deg, #000008 0%, #0A0A0F 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 9999;
`

const LoadingSpinner = styled(motion.div)`
  width: 60px;
  height: 60px;
  border: 3px solid transparent;
  border-top: 3px solid #00FFFF;
  border-right: 3px solid #FF00FF;
  border-radius: 50%;
  margin-bottom: 2rem;
`

const LoadingText = styled(motion.div)`
  font-family: "Orbitron", sans-serif;
  font-size: 1.125rem;
  color: #00FFFF;
  text-transform: uppercase;
  letter-spacing: 0.2em;
`

// 🎮 加载组件
const Loading: React.FC = () => (
  <LoadingScreen
    initial={{ opacity: 0 }}
    animate={{ opacity: 1 }}
    exit={{ opacity: 0 }}
    transition={{ duration: 0.5 }}
  >
    <LoadingSpinner
      animate={{
        rotate: 360,
        scale: [1, 1.1, 1],
      }}
      transition={{
        rotate: { duration: 1, repeat: Infinity, ease: "linear" },
        scale: { duration: 2, repeat: Infinity, ease: "easeInOut" }
      }}
    />

    <LoadingText
      animate={{
        opacity: [0.5, 1, 0.5],
        textShadow: [
          "0 0 10px #00FFFF",
          "0 0 20px #FF00FF",
          "0 0 10px #00FFFF"
        ]
      }}
      transition={{
        duration: 2,
        repeat: Infinity,
        ease: "easeInOut"
      }}
    >
      量化竞技场启动中...
    </LoadingText>
  </LoadingScreen>
)

// 🎨 应用内容组件
const AppContent: React.FC = () => {
  const { theme } = useTheme()
  const { checkAuthStatus } = useAuth()

  // 应用启动时检查认证状态（仅执行一次）
  React.useEffect(() => {
    console.log('🚀 应用启动，初始化认证状态')
    checkAuthStatus()
  }, []) // 移除依赖，仅在组件挂载时执行一次

  return (
    <AppContainer theme={theme}>
      <GlobalStyle theme={theme} />

      <Router>
        <Routes>
          {/* 🎮 游戏主入口 */}
          <Route path="/engine" element={
            <WebSocketProvider>
              <GameShell>
                <SceneRouter />
              </GameShell>
            </WebSocketProvider>
          } />
          <Route path="/game" element={<Navigate to="/engine" replace />} />
          <Route path="/" element={<Navigate to="/engine" replace />} />

          {/* 404页面 */}
          <Route path="*" element={
            <div style={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              height: '100vh',
              textAlign: 'center'
            }}>
              <div style={{ fontSize: '6rem', marginBottom: '2rem' }}>🌌</div>
              <h1 style={{
                fontFamily: theme.typography.fonts.gaming,
                color: theme.colors.primaryColors.cyan,
                marginBottom: '1rem'
              }}>
                区域未找到
              </h1>
              <p style={{ color: theme.colors.neutral.midGray }}>
                此竞技场区域不存在。
              </p>
            </div>
          } />
        </Routes>
      </Router>
    </AppContainer>
  )
}

// 🌟 主应用组件
const App: React.FC = () => {
  return (
    <DynamicThemeProvider>
      <AppContent />
    </DynamicThemeProvider>
  )
}

export default App
