"""
股票基础信息服务

从MongoDB数据库获取股票基础信息，包括行业、概念等相对稳定的属性
支持高效的批量查询和缓存
"""
import logging
import time
from typing import List, Dict, Any, Optional, Set
from datetime import datetime, timedelta

from motor.motor_asyncio import AsyncIOMotorClient
from ..core.config import settings
from .cache_service import cache_service

logger = logging.getLogger(__name__)

class StockInfoService:
    """股票基础信息服务，负责获取股票相关的基础属性"""
    
    def __init__(self):
        """初始化股票基础信息服务"""
        self.cache_service = cache_service
        self._mongo_client = None
        self._industry_cache = {}
        self._industry_cache_time = 0
        self._industry_cache_ttl = 3600  # 行业信息缓存1小时
        logger.info("股票基础信息服务已初始化")
    
    async def _get_mongo_client(self):
        """获取MongoDB客户端连接(延迟初始化)"""
        if self._mongo_client is None:
            self._mongo_client = AsyncIOMotorClient(settings.MONGODB_URL)
        return self._mongo_client
    
    async def get_industry_info(self, symbols: List[str]) -> Dict[str, str]:
        """
        获取股票行业信息
        
        Args:
            symbols: 股票代码列表
            
        Returns:
            Dict[str, str]: 股票代码到行业的映射
        """
        if not symbols:
            return {}
        
        # 检查缓存是否有效
        current_time = time.time()
        cache_valid = current_time - self._industry_cache_time < self._industry_cache_ttl
        
        # 尝试从缓存中读取
        if cache_valid and self._industry_cache:
            # 查找缓存中所有股票是否都存在
            missing_symbols = [s for s in symbols if s not in self._industry_cache]
            
            # 如果所有股票都在缓存中，直接返回
            if not missing_symbols:
                cached_data = {s: self._industry_cache[s] for s in symbols}
                logger.info(f"行业信息缓存命中: {len(cached_data)}只股票")
                return cached_data
            
            # 仅查询缺失的股票
            logger.info(f"行业信息部分缓存命中，查询缺失的{len(missing_symbols)}只")
            symbols = missing_symbols
        
        try:
            client = await self._get_mongo_client()
            db = client.quantcard
            collection = db.stock_info
            
            start_time = time.time()
            logger.info(f"从MongoDB查询{len(symbols)}只股票的行业信息")
            
            # 批量查询所有股票，只获取必要字段
            industry_map = {}
            cursor = collection.find(
                {"股票代码": {"$in": symbols}},
                {"股票代码": 1, "行业": 1, "_id": 0}
            )
            
            # 使用异步迭代器处理查询结果
            async for doc in cursor:
                symbol = doc.get("股票代码")
                industry = doc.get("行业", "")
                if symbol:
                    industry_map[symbol] = industry
                    # 更新缓存
                    self._industry_cache[symbol] = industry
            
            # 更新缓存时间
            self._industry_cache_time = current_time
            
            # 记录查询性能
            elapsed = time.time() - start_time
            logger.info(f"行业信息查询完成: 找到{len(industry_map)}/{len(symbols)}只股票, 耗时: {elapsed:.3f}秒")
            
            # 合并已有缓存和新查询结果
            if cache_valid and self._industry_cache:
                for s in symbols:
                    if s not in industry_map and s in self._industry_cache:
                        industry_map[s] = self._industry_cache[s]
            
            return industry_map
        
        except Exception as e:
            logger.error(f"获取股票行业信息失败: {str(e)}", exc_info=True)
            # 如果查询失败但缓存有效，尽可能返回缓存数据
            if cache_valid and self._industry_cache:
                cached_data = {s: self._industry_cache.get(s, "") for s in symbols}
                logger.warning(f"返回缓存数据: {len(cached_data)}只股票")
                return cached_data
            return {}
    
    async def get_concept_info(self, symbols: List[str]) -> Dict[str, List[Dict[str, str]]]:
        """
        获取股票概念信息
        
        Args:
            symbols: 股票代码列表
            
        Returns:
            Dict[str, List[Dict[str, str]]]: 股票代码到概念列表的映射
        """
        if not symbols:
            return {}
        
        try:
            client = await self._get_mongo_client()
            db = client.quantcard
            collection = db.stock_info
            
            start_time = time.time()
            logger.info(f"从MongoDB查询{len(symbols)}只股票的概念信息")
            
            # 批量查询所有股票，只获取必要字段
            concept_map = {}
            cursor = collection.find(
                {"股票代码": {"$in": symbols}},
                {"股票代码": 1, "概念": 1, "_id": 0}
            )
            
            # 使用异步迭代器处理查询结果
            async for doc in cursor:
                symbol = doc.get("股票代码")
                concepts = doc.get("概念", [])
                if symbol:
                    concept_map[symbol] = concepts
            
            # 记录查询性能
            elapsed = time.time() - start_time
            logger.info(f"概念信息查询完成: 找到{len(concept_map)}/{len(symbols)}只股票, 耗时: {elapsed:.3f}秒")
            
            return concept_map
        
        except Exception as e:
            logger.error(f"获取股票概念信息失败: {str(e)}", exc_info=True)
            return {}
    
    async def get_stock_details(self, symbol: str) -> Dict[str, Any]:
        """
        获取单只股票的详细信息
        
        Args:
            symbol: 股票代码
            
        Returns:
            Dict[str, Any]: 股票详细信息
        """
        try:
            client = await self._get_mongo_client()
            db = client.quantcard
            collection = db.stock_info
            
            # 查询单只股票详情
            doc = await collection.find_one({"股票代码": symbol}, {"_id": 0})
            if doc:
                return doc
            return {}
        
        except Exception as e:
            logger.error(f"获取股票详情失败: {str(e)}", exc_info=True)
            return {}
    
    async def close(self):
        """关闭股票基础信息服务"""
        if self._mongo_client:
            self._mongo_client.close()
            self._mongo_client = None

# 创建单例实例
stock_info_service = StockInfoService() 