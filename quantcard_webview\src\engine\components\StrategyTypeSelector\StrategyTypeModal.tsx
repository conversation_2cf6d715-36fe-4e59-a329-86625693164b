/**
 * 🎯 策略类型选择模态框
 * 基于旧前端StrategyTypeModal改造，适配新的游戏化界面
 */

import React, { useState } from 'react';
import styled from 'styled-components';
import { motion, AnimatePresence } from 'framer-motion';
import StrategyTypeSelector from './StrategyTypeSelector';
import type { StrategyTypeConfig } from './types';

const ModalOverlay = styled(motion.div)`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
`;

const ModalContainer = styled(motion.div)`
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.95) 0%,
    rgba(240, 248, 255, 0.95) 100%);
  border-radius: 20px;
  border: 2px solid rgba(59, 130, 246, 0.2);
  box-shadow: 
    0 25px 50px -12px rgba(0, 0, 0, 0.25),
    0 0 0 1px rgba(255, 255, 255, 0.8) inset;
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  overflow: hidden;
  backdrop-filter: blur(20px);
`;

const ModalHeader = styled.div`
  padding: 2rem 2rem 1rem;
  text-align: center;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
`;

const ModalTitle = styled.h2`
  font-size: 1.8rem;
  font-weight: 700;
  margin: 0 0 0.5rem;
  font-family: "Inter", sans-serif;
`;

const ModalDescription = styled.p`
  color: #64748b;
  font-size: 1rem;
  margin: 0;
  line-height: 1.5;
`;

const ModalContent = styled.div`
  padding: 1rem 2rem 2rem;
`;

const ModalFooter = styled.div`
  padding: 1rem 2rem 2rem;
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  
  @media (max-width: 640px) {
    flex-direction: column;
  }
`;

const ActionButton = styled(motion.button)<{ $variant?: 'primary' | 'secondary' }>`
  padding: 0.75rem 2rem;
  border-radius: 12px;
  font-family: "Inter", sans-serif;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  border: 2px solid;
  transition: all 0.2s ease;
  
  ${props => props.$variant === 'primary' ? `
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    border-color: #3b82f6;
    color: white;
    
    &:hover {
      background: linear-gradient(135deg, #2563eb, #1d4ed8);
      transform: translateY(-1px);
      box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
    }
  ` : `
    background: transparent;
    border-color: #d1d5db;
    color: #6b7280;
    
    &:hover {
      border-color: #9ca3af;
      background: rgba(243, 244, 246, 0.5);
    }
  `}
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }
`;

interface StrategyTypeModalProps {
  open: boolean;
  onCancel: () => void;
  onConfirm: (config: StrategyTypeConfig) => void;
}

const StrategyTypeModal: React.FC<StrategyTypeModalProps> = ({
  open,
  onCancel,
  onConfirm,
}) => {
  const [loading, setLoading] = useState(false);
  const [typeConfig, setTypeConfig] = useState<StrategyTypeConfig>({
    groupType: 'filter',
  });

  const handleFormChange = (value: StrategyTypeConfig) => {
    setTypeConfig(value);
  };

  const handleSubmit = async () => {
    try {
      setLoading(true);
      
      // 验证择时标的
      if (typeConfig.groupType === 'timing' && (!typeConfig.timingSymbols || !typeConfig.timingSymbols.trim())) {
        alert('请输入择时标的');
        setLoading(false);
        return;
      }
      
      onConfirm(typeConfig);
    } catch (error) {
      console.error('提交策略类型选择失败:', error);
      alert('配置失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  const isValid = typeConfig.groupType === 'filter' || 
    (typeConfig.groupType === 'timing' && typeConfig.timingSymbols?.trim());

  return (
    <AnimatePresence>
      {open && (
        <ModalOverlay
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          onClick={onCancel}
        >
          <ModalContainer
            initial={{ opacity: 0, scale: 0.9, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.9, y: 20 }}
            onClick={(e) => e.stopPropagation()}
          >
            <ModalHeader>
              <ModalTitle>选择策略组类型</ModalTitle>
              <ModalDescription>
                请选择要创建的策略组类型，不同类型适用于不同的投资场景
              </ModalDescription>
            </ModalHeader>
            
            <ModalContent>
              <StrategyTypeSelector 
                value={typeConfig} 
                onChange={handleFormChange} 
              />
            </ModalContent>
            
            <ModalFooter>
              <ActionButton 
                $variant="secondary" 
                onClick={onCancel}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                取消
              </ActionButton>
              <ActionButton
                $variant="primary"
                disabled={!isValid || loading}
                onClick={handleSubmit}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                {loading ? '配置中...' : '下一步'}
              </ActionButton>
            </ModalFooter>
          </ModalContainer>
        </ModalOverlay>
      )}
    </AnimatePresence>
  );
};

export default StrategyTypeModal; 