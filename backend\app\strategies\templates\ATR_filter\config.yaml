name: 振幅过滤策略
description: 根据股票振幅区间进行过滤
strategy_class_name: ATRFilterStrategy
version: 1.0.0
data_sources:
  backtest:
    database:
      type: postgresql
      name: market_data
      schema: public
      table: stock_historical_quotes
    frequency: 1d
    fields:
    - 代码
    - 名称
    - 振幅
    - trade_time
    window_size: 1
  filter:
    database:
      type: postgresql
      name: stock_base_info
      schema: public
      table: stock_list_rt
    frequency: tick
    fields:
    - 代码
    - 名称
    - 振幅
    window_size: 1
  monitor:
    database:
      type: mongodb
      name: realtime_data
      collection: stocks
      table: stock_list_rt
    frequency: 1m
    fields:
    - 代码
    - 名称
    - 振幅
    window_size: 2
  timing:
    database:
      type: mongodb
      name: realtime_data
      collection: stocks
      table: stock_list_rt
    frequency: 5m
    fields:
    - 代码
    - 名称
    - 振幅
    window_size: 10
outputs:
  backtest:
    type: backtest_result
    fields:
    - 代码
    - 名称
    - 振幅
    - 命中时间
    - 信号类型
  filter:
    type: stock_list
    fields:
    - 代码
    - 名称
    - 振幅
  monitor:
    type: alert
    fields:
    - 代码
    - 名称
    - 振幅
    - 触发条件
    - 触发时间
  timing:
    type: signal
    fields:
    - 代码
    - 名称
    - 振幅
    - 信号类型
    - 信号强度
