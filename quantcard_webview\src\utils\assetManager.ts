/**
 * 🎨 简化版资源管理系统
 * 基于现代浏览器能力的轻量级资源管�? */

import React from 'react'

// 🎯 简化的资源类型
export type AssetType = 'icon' | 'image' | 'animation'
export type AssetFormat = 'svg' | 'png' | 'webp' | 'json'

// 🎨 资源信息
interface SimpleAssetInfo {
  id: string
  path: string
  fallback: string
  type: AssetType
}

// 🎯 策略图标映射 - 简化版
export const StrategyIcons: Record<string, SimpleAssetInfo> = {
  // 技术指标类
  'rsi': { id: 'rsi', path: '/icons/rsi.svg', fallback: '📈', type: 'icon' },
  'macd': { id: 'macd', path: '/icons/macd.svg', fallback: '📊', type: 'icon' },
  'ma': { id: 'ma', path: '/icons/ma.svg', fallback: '📈', type: 'icon' },
  'kdj': { id: 'kdj', path: '/icons/kdj.svg', fallback: '📉', type: 'icon' },
  'bollinger': { id: 'bollinger', path: '/icons/bollinger.svg', fallback: '📉', type: 'icon' },
  
  // 基本面类
  'pe': { id: 'pe', path: '/icons/pe.svg', fallback: '💰', type: 'icon' },
  'pb': { id: 'pb', path: '/icons/pb.svg', fallback: '📋', type: 'icon' },
  'roe': { id: 'roe', path: '/icons/roe.svg', fallback: '💎', type: 'icon' },
  
  // 风控类
  'stop_loss': { id: 'stop_loss', path: '/icons/stop.svg', fallback: '🛑', type: 'icon' },
  'position': { id: 'position', path: '/icons/position.svg', fallback: '⚖️', type: 'icon' },
  
  // 其他
  'ai': { id: 'ai', path: '/icons/ai.svg', fallback: '🤖', type: 'icon' },
  'custom': { id: 'custom', path: '/icons/custom.svg', fallback: '⚙️', type: 'icon' }
}

// 🎨 简化的资源管理器
class SimpleAssetManager {
  private cache = new Map<string, string>()
  private loading = new Set<string>()

  // 🎯 获取策略图标
  getStrategyIcon(templateId: string): string {
    const iconInfo = StrategyIcons[templateId] || StrategyIcons['custom']
    
    // 优先返回缓存的资源
    if (this.cache.has(iconInfo.id)) {
      return this.cache.get(iconInfo.id)!
    }

    // 异步加载图标
    this.loadIcon(iconInfo)
    
    // 立即返回 fallback
    return iconInfo.fallback
  }

  // 🔄 异步加载图标
  private async loadIcon(iconInfo: SimpleAssetInfo): Promise<void> {
    if (this.loading.has(iconInfo.id)) return
    
    this.loading.add(iconInfo.id)
    
    try {
      // 使用现代浏览器的 fetch API
      const response = await fetch(iconInfo.path)
      if (response.ok) {
        const iconData = await response.text()
        this.cache.set(iconInfo.id, iconData)
      } else {
        // 加载失败，缓存 fallback
        this.cache.set(iconInfo.id, iconInfo.fallback)
      }
    } catch (error) {
      console.warn(`Failed to load icon ${iconInfo.id}:`, error)
      this.cache.set(iconInfo.id, iconInfo.fallback)
    } finally {
      this.loading.delete(iconInfo.id)
    }
  }

  // 🧹 清理缓存
  clearCache(): void {
    this.cache.clear()
    this.loading.clear()
  }

  // 📊 获取缓存信息
  getCacheInfo(): { size: number; items: string[] } {
    return {
      size: this.cache.size,
      items: Array.from(this.cache.keys())
    }
  }

  // 🎯 预加载关键资源
  async preloadCriticalAssets(): Promise<void> {
    const criticalIcons = ['rsi', 'macd', 'ma', 'kdj', 'stop_loss']
    const promises = criticalIcons.map(iconId => {
      const iconInfo = StrategyIcons[iconId]
      return iconInfo ? this.loadIcon(iconInfo) : Promise.resolve()
    })
    await Promise.allSettled(promises)
  }
}

// 🎯 全局实例
export const assetManager = new SimpleAssetManager()

// 🎨 React Hook for asset management
export const useAssets = () => {
  const [, forceUpdate] = React.useReducer(x => x + 1, 0)

  React.useEffect(() => {
    // 预加载关键资源
    assetManager.preloadCriticalAssets()
  }, [])

  return {
    getStrategyIcon: (templateId: string) => {
      const icon = assetManager.getStrategyIcon(templateId)
      // 如果图标更新，强制重新渲染
      if (icon !== StrategyIcons[templateId]?.fallback) {
        setTimeout(forceUpdate, 0)
      }
      return icon
    },
    clearCache: () => assetManager.clearCache(),
    getCacheInfo: () => assetManager.getCacheInfo()
  }
}

// 🔄 向后兼容
export { SimpleAssetManager as AssetManager }
export default assetManager
