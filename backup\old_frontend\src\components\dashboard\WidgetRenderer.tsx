import React, { useCallback, useRef } from 'react';
import * as ReactDOM from 'react-dom/client';
import CyberpunkCandlestick, { CandlestickData } from '../../components/charts/CyberpunkCandlestick';
import MarketSentiment from '../../components/market/MarketSentiment';
import MarketHeatmap from '../../components/market/MarketHeatmap';
import AnnouncementBoard from '../../components/notification/AnnouncementBoard';
import MyStrategyGroupStat from '../../components/strategy/MyStrategyGroupStat';
import { DashboardWidget } from '../../types/dashboard';
import './WidgetRenderer.scss';

// 导入必要的类型
interface Announcement {
  id: string;
  type: 'success' | 'warning' | 'info';
  title: string;
  content: string;
  timestamp: string;
  strategy?: string;
  read?: boolean;
}

// 导入数据
import { mockMarketData, mockAnnouncements } from '../../data/mockData';

// 组件类型映射
export const componentMap: Record<string, (props: any) => React.ReactElement> = {
  'chart': (props: any) => <CyberpunkCandlestick {...props} />,
  'heatmap': (props: any) => <MarketHeatmap {...props} />,
  'sentiment': (props: any) => <MarketSentiment {...props} />,
  'announcements': (props: any) => <AnnouncementBoard {...props} />,
  'strategy-group': (props: any) => <MyStrategyGroupStat {...props} />,
};

interface WidgetRendererProps {
  widgets: DashboardWidget[];
  candlestickData: CandlestickData[];
}

/**
 * 小部件渲染器组件 - 负责将小部件渲染到对应的容器中
 */
const WidgetRenderer: React.FC<WidgetRendererProps> = ({ widgets, candlestickData }) => {
  const rootRefs = useRef<Map<string, ReactDOM.Root>>(new Map());
  
  // 创建或获取Root实例
  const getOrCreateRoot = useCallback((containerId: string, container: HTMLElement) => {
    let root = rootRefs.current.get(containerId);
    if (!root) {
      // 检查容器是否已经有了React root
      try {
        root = ReactDOM.createRoot(container);
        rootRefs.current.set(containerId, root);
      } catch (error) {
        console.error(`为容器 ${containerId} 创建Root失败:`, error);
        // 如果创建失败，尝试重新使用同一个容器
        container.innerHTML = '';
        root = ReactDOM.createRoot(container);
        rootRefs.current.set(containerId, root);
      }
    }
    return root;
  }, []);
  
  // 渲染组件
  const renderComponent = useCallback((containerId: string, componentType: string, props: any, settings?: Record<string, any>) => {
    const container = document.getElementById(containerId);
    if (!container) {
      console.error(`找不到容器: ${containerId}`);
      return;
    }
    
    const Component = componentMap[componentType];
    if (!Component) {
      console.error(`未知组件类型: ${componentType}`);
      return;
    }
    
    // 合并设置到属性中
    const mergedProps = { ...props, ...(settings || {}) };
    
    const root = getOrCreateRoot(containerId, container);
    root.render(<Component {...mergedProps} />);
  }, [getOrCreateRoot]);
  
  // 为每个小部件准备适当的属性
  const getComponentProps = useCallback((widget: DashboardWidget) => {
    const { component: componentType, title, settings } = widget;
    let props = {};
    
    switch (componentType) {
      case 'chart':
        props = { 
          data: candlestickData, 
          height: 280, 
          showVolume: true, 
          showMA: true, 
          title: settings?.symbol || "BTC/USDT",
          interval: settings?.interval || "1h",
          showTitle: true,
          style: { border: 'none', boxShadow: 'none' }
        };
        break;
      case 'heatmap':
        props = { 
          height: 280, 
          title,
          marketType: settings?.marketType || 'crypto',
          showTitle: false,
          style: { border: 'none', boxShadow: 'none' }
        };
        break;
      case 'sentiment':
        props = { 
          data: mockMarketData,
          style: { 
            border: 'none', 
            boxShadow: 'none',
            background: '#ffffff',
            padding: '12px'
          }
        };
        break;
      case 'announcements':
        props = { 
          announcements: mockAnnouncements,
          onAnnouncementClick: (announcement: Announcement) => console.log('点击公告:', announcement),
          style: { 
            border: 'none', 
            boxShadow: 'none',
            background: '#ffffff',
            paddingTop: '10px'
          }
        };
        break;
      case 'strategy-group':
        props = { 
          style: { 
            border: 'none', 
            boxShadow: 'none',
            background: '#ffffff'
          }
        };
        break;
    }
    return props;
  }, [candlestickData]);
  
  // 渲染所有小部件
  const renderAllWidgets = useCallback(() => {
    // 创建一个对象存储需要渲染的小部件及其重试次数
    const widgetsToRender = new Map(widgets.map(widget => [widget.id, { widget, retries: 0 }]));
    
    // 最大重试次数
    const MAX_RETRIES = 5;
    // 每次检查的间隔（毫秒）
    const CHECK_INTERVAL = 300;
    // 最大等待时间（毫秒）
    const MAX_WAIT_TIME = 3000;
    
    // 尝试渲染所有未渲染的小部件
    const attemptRender = () => {
      // 如果没有需要渲染的小部件了，停止重试
      if (widgetsToRender.size === 0) {
        return;
      }
      
      // 遍历所有需要渲染的小部件
      widgetsToRender.forEach((data, id) => {
        const { widget, retries } = data;
        const containerId = `${widget.id}-container`;
        
        // 尝试获取容器
        const container = document.getElementById(containerId);
        
        // 如果找到容器，就渲染组件
        if (container) {
          console.log(`成功找到容器: ${containerId}，开始渲染组件`);
          const props = getComponentProps(widget);
          renderComponent(containerId, widget.component, props, widget.settings);
          // 渲染成功，从待渲染列表中移除
          widgetsToRender.delete(id);
        } 
        // 如果没找到容器，且尚未超过最大重试次数，增加重试计数
        else if (retries < MAX_RETRIES) {
          console.log(`未找到容器: ${containerId}，重试 ${retries + 1}/${MAX_RETRIES}`);
          widgetsToRender.set(id, { widget, retries: retries + 1 });
        } 
        // 如果超过最大重试次数，放弃渲染
        else {
          console.error(`找不到容器: ${containerId}，已达到最大重试次数，放弃渲染`, widget);
          widgetsToRender.delete(id);
        }
      });
      
      // 如果还有未渲染的小部件，且未超过最大等待时间，继续重试
      if (widgetsToRender.size > 0) {
        setTimeout(attemptRender, CHECK_INTERVAL);
      }
    };
    
    // 开始第一次尝试渲染
    setTimeout(attemptRender, 100);
  }, [widgets, getComponentProps, renderComponent]);
  
  // 组件卸载时清理
  React.useEffect(() => {
    // 初始化时渲染所有部件
    console.log('开始渲染所有小部件', widgets);
    renderAllWidgets();
    
    // 卸载时清理
    return () => {
      console.log('清理所有组件Root');
      // 使用setTimeout来避免同步卸载
      setTimeout(() => {
        rootRefs.current.forEach((root, id) => {
          try {
            // 检查DOM元素是否仍然存在
            const container = document.getElementById(id);
            if (container) {
              root.unmount();
              console.log(`成功卸载组件: ${id}`);
            } else {
              console.log(`容器已经不存在，跳过卸载: ${id}`);
            }
          } catch (error) {
            console.error(`卸载Root ${id} 失败:`, error);
          }
        });
        rootRefs.current.clear();
      }, 0);
    };
  }, [widgets, renderAllWidgets]);
  
  return null; // 这个组件不渲染任何UI，只负责将组件渲染到对应的容器中
};

export default WidgetRenderer; 