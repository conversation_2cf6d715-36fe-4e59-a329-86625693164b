/**
 * 🎮 游戏状态切�?- 管理用户游戏数据和会话状�?
 * 使用原生immutable更新模式，简化状态管理逻辑
 */

import type { SliceCreator, GameSlice } from '../types/store'
import type { GameSession } from '../../types/game'

// 🎯 创建游戏切片
export const createGameSlice: SliceCreator<GameSlice> = (set, get) => ({
  // 🎮 初始状�?
  session: null,
  playerLevel: 1,
  totalExperience: 0,
  currency: { coins: 1000, gems: 50 },
  loading: false,
  error: undefined,

  // 🎯 会话管理
  initializeSession: async (userId: string) => {
    const currentState = get()
    set({
      ...currentState,
      loading: true,
      error: undefined
    })

    try {
      // 创建游戏会话（无需API调用，直接在前端管理）
      const session: GameSession = {
        user_id: userId,
        session_start: new Date(),
        current_scene: 'WorldMap',
        total_playtime: 0,
        achievements_unlocked: []
      }

      set({
        ...get(),
        session,
        loading: false
      })
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '初始化游戏会话失败'
      set({
        ...get(),
        loading: false,
        error: errorMessage
      })
    }
  },

  // 💰 货币管理
  updateCurrency: (type: 'coins' | 'gems', amount: number) => {
    const currentState = get()
    set({
      ...currentState,
      currency: {
        ...currentState.currency,
        [type]: Math.max(0, currentState.currency[type] + amount)
      }
    })
  },

  // �?经验值管�?
  addExperience: (amount: number) => {
    const currentState = get()
    const newExperience = currentState.totalExperience + amount
    const newLevel = Math.floor(newExperience / 1000) + 1

    set({
      ...currentState,
      totalExperience: newExperience,
      playerLevel: Math.max(currentState.playerLevel, newLevel)
    })
  },

  // 🔧 错误处理
  clearError: () => {
    const currentState = get()
    set({
      ...currentState,
      error: undefined
    })
  },

  // 🔄 重置游戏状�?
  resetGameState: () => {
    set({
      session: null,
      playerLevel: 1,
      totalExperience: 0,
      currency: { coins: 1000, gems: 50 },
      loading: false,
      error: undefined
    })
  }
})
