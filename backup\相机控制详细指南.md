# 🎥 3D场景相机控制详细指南

## 📍 当前相机配置分析

### 基础相机设置
```typescript
// 在 WorldMapScene.tsx 第657-661行
<Canvas
  camera={{
    position: [0, 6, 12],  // X, Y, Z 坐标
    fov: 60               // 视野角度 (Field of View)
  }}
  style={{ width: '100%', height: '100%' }}
  className="world-map-canvas"
>
```

### OrbitControls 配置
```typescript
// 在 WorldMapScene.tsx 第720-740行
<OrbitControls
  enablePan={false}                    // 禁用平移
  enableZoom={true}                    // 启用缩放
  enableRotate={true}                  // 启用旋转
  minDistance={8}                      // 最近距离
  maxDistance={18}                     // 最远距离
  minPolarAngle={Math.PI / 6}          // 最小极角 (30度)
  maxPolarAngle={Math.PI / 2.2}        // 最大极角 (约80度)
  autoRotate={!selectedLandmark}       // 自动旋转
  autoRotateSpeed={0.2}                // 自动旋转速度
  target={[0, 0, 0]}                   // 注视目标
/>
```

## 🎯 相机参数详解

### 1. 相机位置 (Position)
```typescript
position: [X, Y, Z]

// 当前设置: [0, 6, 12]
// X = 0:  水平居中
// Y = 6:  高度6单位 (俯视角度)
// Z = 12: 距离地球12单位

// 🎨 不同视角效果:
position: [0, 2, 8]    // 低角度，更接近地面
position: [0, 10, 15]  // 高角度，更俯视
position: [5, 6, 12]   // 侧面偏移
position: [0, 0, 15]   // 水平视角
```

### 2. 视野角度 (FOV)
```typescript
fov: 60  // 度数

// 🎨 不同FOV效果:
fov: 45   // 窄视野，望远镜效果，物体更大
fov: 60   // 标准视野，接近人眼
fov: 75   // 广角视野，能看到更多内容
fov: 90   // 超广角，有鱼眼效果
```

### 3. 极角控制 (Polar Angle)
```typescript
// 控制上下旋转范围
minPolarAngle: Math.PI / 6     // 30度 (最高视角)
maxPolarAngle: Math.PI / 2.2   // 约80度 (最低视角)

// 🎨 角度对照表:
Math.PI / 6     = 30度  (高俯视)
Math.PI / 4     = 45度  (中等俯视)
Math.PI / 3     = 60度  (低俯视)
Math.PI / 2     = 90度  (水平)
Math.PI / 2.2   = 82度  (接近水平)
```

### 4. 距离控制 (Distance)
```typescript
minDistance: 8   // 最近8单位
maxDistance: 18  // 最远18单位

// 🎨 距离效果:
minDistance: 5   // 可以很近，看到细节
minDistance: 10  // 保持距离，整体视角
maxDistance: 25  // 可以很远，全景视角
maxDistance: 15  // 限制距离，聚焦效果
```

## 🛠️ 实际调整代码

### 方案1: 经典俯视角 (推荐)
```typescript
// 适合策略游戏，清晰的地图视角
<Canvas
  camera={{
    position: [0, 8, 14],  // 更高更远
    fov: 50               // 更窄视野，聚焦
  }}
>

<OrbitControls
  minDistance={10}
  maxDistance={20}
  minPolarAngle={Math.PI / 8}    // 22.5度，更高俯视
  maxPolarAngle={Math.PI / 2.5}  // 72度，不能太低
  autoRotateSpeed={0.1}          // 更慢的旋转
/>
```

### 方案2: 沉浸式视角
```typescript
// 更接近地面，有代入感
<Canvas
  camera={{
    position: [0, 4, 10],  // 更低更近
    fov: 70               // 更广视野
  }}
>

<OrbitControls
  minDistance={6}
  maxDistance={15}
  minPolarAngle={Math.PI / 4}    // 45度
  maxPolarAngle={Math.PI / 1.8}  // 100度，可以看到更多
  enablePan={true}               // 允许平移
/>
```

### 方案3: 电影级视角
```typescript
// 动态感强，适合展示
<Canvas
  camera={{
    position: [3, 7, 13],  // 侧面偏移
    fov: 55               // 电影比例
  }}
>

<OrbitControls
  minDistance={8}
  maxDistance={25}
  minPolarAngle={Math.PI / 6}
  maxPolarAngle={Math.PI / 2.1}
  autoRotate={true}
  autoRotateSpeed={0.3}          // 更快展示
/>
```

## 🎮 动态相机控制

### 添加相机动画
```typescript
// 在 WorldMapScene.tsx 中添加
const [cameraTarget, setCameraTarget] = useState([0, 6, 12]);

// 地标选中时的相机动画
const animateToLandmark = (landmark: Landmark) => {
  const targetPos = [
    landmark.position[0] * 1.5,  // X偏移
    landmark.position[1] + 3,    // Y抬高
    landmark.position[2] * 1.5   // Z偏移
  ];
  setCameraTarget(targetPos);
};

// 在Canvas中使用
<Canvas
  camera={{
    position: cameraTarget,
    fov: 60
  }}
>
```

### 响应式相机设置
```typescript
// 根据屏幕尺寸调整
const getCameraConfig = () => {
  const isMobile = window.innerWidth < 768;
  
  return {
    position: isMobile ? [0, 5, 10] : [0, 6, 12],
    fov: isMobile ? 70 : 60
  };
};

<Canvas camera={getCameraConfig()}>
```

## 🎨 视觉效果调优

### 景深效果 (可选)
```typescript
import { DepthOfField, EffectComposer } from '@react-three/postprocessing';

// 在Canvas内添加
<EffectComposer>
  <DepthOfField
    focusDistance={0.02}  // 焦点距离
    focalLength={0.05}    // 焦距
    bokehScale={5}        // 虚化强度
  />
</EffectComposer>
```

### 相机震动效果
```typescript
// 地标选中时的震动
const [cameraShake, setCameraShake] = useState(0);

useFrame((state) => {
  if (cameraShake > 0) {
    state.camera.position.x += (Math.random() - 0.5) * cameraShake;
    state.camera.position.y += (Math.random() - 0.5) * cameraShake;
    setCameraShake(cameraShake * 0.9); // 衰减
  }
});
```

## 🔧 调试工具

### 相机位置显示
```typescript
// 添加到性能监控中
const CameraDebug = () => {
  const [cameraPos, setCameraPos] = useState([0, 0, 0]);
  
  useFrame((state) => {
    setCameraPos([
      state.camera.position.x.toFixed(1),
      state.camera.position.y.toFixed(1),
      state.camera.position.z.toFixed(1)
    ]);
  });

  return (
    <div style={{ position: 'absolute', top: '50px', left: '10px' }}>
      相机位置: [{cameraPos.join(', ')}]
    </div>
  );
};
```

### 相机控制面板
```typescript
// 在3D设置面板中添加
<div style={{ marginBottom: '10px' }}>
  <label>相机高度: {cameraHeight}</label>
  <input
    type="range"
    min="2"
    max="15"
    value={cameraHeight}
    onChange={(e) => setCameraHeight(Number(e.target.value))}
  />
</div>

<div style={{ marginBottom: '10px' }}>
  <label>视野角度: {fov}°</label>
  <input
    type="range"
    min="30"
    max="90"
    value={fov}
    onChange={(e) => setFov(Number(e.target.value))}
  />
</div>
```

---

接下来我将为您详细讲解球体贴图制作...
