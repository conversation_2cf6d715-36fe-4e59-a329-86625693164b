from typing import Dict, Any, Optional
from datetime import datetime
from pydantic import BaseModel, Field
from bson import ObjectId

from ..core.data.db.base import MongoModel, db_manager

class SystemMetrics(MongoModel, BaseModel):
    """系统指标模型"""
    cpu_usage: float = Field(..., description="CPU使用率")
    memory_usage: float = Field(..., description="内存使用率")
    disk_usage: float = Field(..., description="磁盘使用率")
    timestamp: datetime = Field(default_factory=datetime.now, description="记录时间")

    @classmethod
    def get_collection_name(cls) -> str:
        """获取集合名称"""
        return "system_metrics"
        
    @classmethod
    def get_db_name(cls) -> str:
        """获取数据库名称"""
        return "quantcard"
        
    class Config:
        arbitrary_types_allowed = True
        json_encoders = {
            ObjectId: str,
            datetime: lambda v: v.isoformat()
        }

class PerformanceMetrics(MongoModel, BaseModel):
    """性能指标模型"""
    api_path: str = Field(..., description="API路径")
    method: str = Field(..., description="请求方法")
    response_time: float = Field(..., description="响应时间(ms)")
    status_code: int = Field(..., description="状态码")
    timestamp: datetime = Field(default_factory=datetime.now, description="记录时间")

    @classmethod
    def get_collection_name(cls) -> str:
        """获取集合名称"""
        return "performance_metrics"
        
    @classmethod
    def get_db_name(cls) -> str:
        """获取数据库名称"""
        return "quantcard"
        
    class Config:
        arbitrary_types_allowed = True
        json_encoders = {
            ObjectId: str,
            datetime: lambda v: v.isoformat()
        }

class ErrorLog(MongoModel, BaseModel):
    """错误日志模型"""
    error_type: str = Field(..., description="错误类型")
    error_message: str = Field(..., description="错误信息")
    stack_trace: Optional[str] = Field(None, description="堆栈跟踪")
    timestamp: datetime = Field(default_factory=datetime.now, description="记录时间")

    @classmethod
    def get_collection_name(cls) -> str:
        """获取集合名称"""
        return "error_logs"
        
    @classmethod
    def get_db_name(cls) -> str:
        """获取数据库名称"""
        return "quantcard"
        
    class Config:
        arbitrary_types_allowed = True
        json_encoders = {
            ObjectId: str,
            datetime: lambda v: v.isoformat()
        }

class UserActivity(MongoModel, BaseModel):
    """用户活动记录模型"""
    user_id: str = Field(..., description="用户ID")
    action: str = Field(..., description="操作类型")
    resource: str = Field(..., description="资源类型")
    details: Optional[Dict[str, Any]] = Field(default=None, description="详细信息")
    timestamp: datetime = Field(default_factory=datetime.now, description="记录时间")

    @classmethod
    def get_collection_name(cls) -> str:
        """获取集合名称"""
        return "user_activities"
        
    @classmethod
    def get_db_name(cls) -> str:
        """获取数据库名称"""
        return "quantcard"
        
    class Config:
        arbitrary_types_allowed = True
        json_encoders = {
            ObjectId: str,
            datetime: lambda v: v.isoformat()
        }
        
    def to_response(self) -> Dict[str, Any]:
        """转换为响应格式"""
        return {
            "id": str(self.id) if hasattr(self, "id") else None,
            "user_id": self.user_id,
            "action": self.action,
            "resource": self.resource,
            "details": self.details,
            "timestamp": self.timestamp.isoformat()
        } 