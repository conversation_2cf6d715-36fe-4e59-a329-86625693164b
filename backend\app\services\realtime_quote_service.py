"""
实时行情数据服务模块

提供东方财富实时行情报价的获取和管理功能
"""
import logging
from typing import List, Optional, Dict, Any, Union
import pandas as pd
import asyncio
from datetime import datetime, timedelta
import traceback

from ..core.data.db.base import db_manager

logger = logging.getLogger(__name__)

class RealTimeQuoteService:
    """实时行情数据服务，用于获取东方财富实时行情数据"""
    
    _instances = {}
    
    @classmethod
    def get_instance(cls, *args, **kwargs):
        """获取单例实例"""
        if cls not in cls._instances:
            cls._instances[cls] = cls(*args, **kwargs)
        return cls._instances[cls]
    
    def __init__(self):
        """初始化实时行情数据服务"""
        self.table_name = "realtime_quotes"
    
    async def get_realtime_quotes(self, symbols: List[str], save_to_db: bool = True) -> Dict[str, pd.DataFrame]:
        """
        批量获取实时行情数据
        
        Args:
            symbols: 股票代码列表
            save_to_db: 是否保存数据到数据库
            
        Returns:
            Dict[str, pd.DataFrame]: 股票代码到行情数据的映射
        """
        if not symbols:
            return {}
            
        cache_key = f"realtime_quotes_{','.join(symbols)}"
        current_time = datetime.now()
        
        # 使用缓存
        cache_data = db_manager.get_questdb_cache(cache_key)
        if cache_data:
            cache_time = cache_data.get('time', datetime.min)
            # 缓存有效期5秒
            if (current_time - cache_time).total_seconds() < 5:
                logger.info(f"从缓存获取实时行情数据: {len(symbols)}个标的")
                return cache_data.get('data', {})
        
        # 创建结果容器
        result = {}
        
        try:
            # 并行获取数据
            tasks = []
            for symbol in symbols:
                task = asyncio.create_task(self._fetch_single_quote(symbol))
                tasks.append((symbol, task))
            
            # 等待所有任务完成
            for symbol, task in tasks:
                try:
                    df = await task
                    if df is not None and not df.empty:
                        result[symbol] = df
                        
                        # 异步保存数据到数据库
                        if save_to_db:
                            asyncio.create_task(self._async_save_data(df, self.table_name))
                except Exception as e:
                    logger.error(f"获取标的 {symbol} 的实时行情失败: {str(e)}")
            
            # 缓存结果
            db_manager.set_questdb_cache(cache_key, {
                'time': current_time,
                'data': result
            })
            
            return result
            
        except Exception as e:
            logger.error(f"获取实时行情数据失败: {str(e)}")
            return {}
    
    async def _fetch_single_quote(self, symbol: str) -> Optional[pd.DataFrame]:
        """
        获取单个标的的实时行情
        
        Args:
            symbol: 股票代码
            
        Returns:
            Optional[pd.DataFrame]: 行情数据，获取失败则返回None
        """
        try:
            # 导入AKShare
            import akshare as ak
            
            logger.info(f"获取标的 {symbol} 的实时行情")
            
            # 从AKShare获取数据 - 同步调用
            quote_data = await asyncio.to_thread(
                ak.stock_bid_ask_em,
                symbol=symbol
            )
            
            if quote_data is None or quote_data.empty:
                logger.warning(f"获取标的 {symbol} 的实时行情为空")
                return None
                
            # 处理数据
            now = datetime.now()
            
            # 创建结果DataFrame
            result_data = {
                'symbol': symbol,
                'time': now,
                'data_source': 'eastmoney'
            }
            
            # 将行情数据转换为字典
            quote_dict = dict(zip(quote_data['item'], quote_data['value']))
            
            # 映射字段名
            field_mapping = {
                '最新': 'latest_price',
                '均价': 'avg_price',
                '涨幅': 'change_pct',
                '涨跌': 'change_price',
                '总手': 'volume',
                '金额': 'amount',
                '换手': 'turnover',
                '量比': 'volume_ratio',
                '最高': 'high_price',
                '最低': 'low_price',
                '今开': 'open_price',
                '昨收': 'pre_close',
                '涨停': 'upper_limit',
                '跌停': 'lower_limit',
                '外盘': 'outer_volume',
                '内盘': 'inner_volume',
                'buy_1': 'buy1',
                'buy_1_vol': 'buy1_vol',
                'buy_2': 'buy2',
                'buy_2_vol': 'buy2_vol',
                'buy_3': 'buy3',
                'buy_3_vol': 'buy3_vol',
                'buy_4': 'buy4',
                'buy_4_vol': 'buy4_vol',
                'buy_5': 'buy5',
                'buy_5_vol': 'buy5_vol',
                'sell_1': 'sell1',
                'sell_1_vol': 'sell1_vol',
                'sell_2': 'sell2',
                'sell_2_vol': 'sell2_vol',
                'sell_3': 'sell3',
                'sell_3_vol': 'sell3_vol',
                'sell_4': 'sell4',
                'sell_4_vol': 'sell4_vol',
                'sell_5': 'sell5',
                'sell_5_vol': 'sell5_vol',
            }
            
            # 定义数值类型的字段
            numeric_fields = {
                'latest_price', 'avg_price', 'change_pct', 'change_price', 'volume', 
                'amount', 'turnover', 'volume_ratio', 'high_price', 'low_price', 
                'open_price', 'pre_close', 'upper_limit', 'lower_limit', 
                'outer_volume', 'inner_volume',
                'buy1', 'buy1_vol', 'buy2', 'buy2_vol', 'buy3', 'buy3_vol', 
                'buy4', 'buy4_vol', 'buy5', 'buy5_vol',
                'sell1', 'sell1_vol', 'sell2', 'sell2_vol', 'sell3', 'sell3_vol', 
                'sell4', 'sell4_vol', 'sell5', 'sell5_vol'
            }
            
            # 转换字段 - 一次性处理数值类型
            for src_field, dst_field in field_mapping.items():
                value = quote_dict.get(src_field)
                if value is not None:
                    # 对数值字段直接进行类型转换
                    if dst_field in numeric_fields:
                        if value == '-' or value == '' or value == 'nan' or value == 'None':
                            value = None
                        else:
                            try:
                                value = float(value)
                            except (ValueError, TypeError):
                                value = None
                    result_data[dst_field] = value
            
            # 创建DataFrame
            df = pd.DataFrame([result_data])
            
            logger.info(f"成功获取标的 {symbol} 的实时行情")
            return df
            
        except Exception as e:
            logger.error(f"获取标的 {symbol} 的实时行情失败: {str(e)}")
            logger.error(f"错误堆栈: {traceback.format_exc()}")
            return None
    
    async def _async_save_data(self, df: pd.DataFrame, table_name: str) -> None:
        """
        异步保存数据到数据库
        
        Args:
            df: 数据框
            table_name: 表名
        """
        try:
            if df.empty:
                return
                
            # 定义实时行情数据的基础列（数据库中已存在的列）
            base_columns = [
                'symbol', 'time', 'latest_price', 'avg_price', 'change_pct', 'change_price',
                'volume', 'amount', 'turnover', 'volume_ratio', 'high_price', 'low_price',
                'open_price', 'pre_close', 'upper_limit', 'lower_limit', 'outer_volume', 'inner_volume',
                'buy1', 'buy1_vol', 'buy2', 'buy2_vol', 'buy3', 'buy3_vol', 'buy4', 'buy4_vol', 'buy5', 'buy5_vol',
                'sell1', 'sell1_vol', 'sell2', 'sell2_vol', 'sell3', 'sell3_vol', 'sell4', 'sell4_vol',
                'sell5', 'sell5_vol', 'data_source'
            ]
            
            # 过滤数据框，只保留数据库中存在的列
            filtered_df = df.copy()
            available_columns = [col for col in base_columns if col in df.columns]
            filtered_df = filtered_df[available_columns]
            
            # 获取列名和数据记录
            columns = filtered_df.columns.tolist()
            records = filtered_df.to_dict('records')
            
            if not records:
                return
                
            # 批量插入
            batch_size = 500
            total_records = len(records)
            inserted_count = 0
            
            # 获取数据库连接
            conn = await db_manager.get_questdb_connection()
            
            # 构建UPSERT查询 - 使用INSERT IGNORE语法
            # 对于QuestDB，我们使用主键约束来防止重复插入
            columns_str = ", ".join(columns)
            placeholders = ", ".join(["%s"] * len(columns))
            
            # 先查看表是否存在
            try:
                with conn.cursor() as cursor:
                    cursor.execute(f"SELECT * FROM {table_name} LIMIT 1")
            except Exception:
                # 创建表，添加符合的数据类型和主键约束
                create_table_query = f"""
                CREATE TABLE IF NOT EXISTS {table_name} (
                    symbol SYMBOL,
                    time TIMESTAMP,
                    latest_price DOUBLE,
                    avg_price DOUBLE,
                    change_pct DOUBLE,
                    change_price DOUBLE,
                    volume DOUBLE,
                    amount DOUBLE,
                    turnover DOUBLE,
                    volume_ratio DOUBLE,
                    high_price DOUBLE,
                    low_price DOUBLE,
                    open_price DOUBLE,
                    pre_close DOUBLE,
                    upper_limit DOUBLE,
                    lower_limit DOUBLE,
                    outer_volume DOUBLE,
                    inner_volume DOUBLE,
                    buy1 DOUBLE,
                    buy1_vol DOUBLE,
                    buy2 DOUBLE,
                    buy2_vol DOUBLE,
                    buy3 DOUBLE,
                    buy3_vol DOUBLE,
                    buy4 DOUBLE,
                    buy4_vol DOUBLE,
                    buy5 DOUBLE,
                    buy5_vol DOUBLE,
                    sell1 DOUBLE,
                    sell1_vol DOUBLE,
                    sell2 DOUBLE,
                    sell2_vol DOUBLE,
                    sell3 DOUBLE,
                    sell3_vol DOUBLE,
                    sell4 DOUBLE,
                    sell4_vol DOUBLE,
                    sell5 DOUBLE,
                    sell5_vol DOUBLE,
                    data_source SYMBOL,
                    PRIMARY KEY(symbol, time)
                ) timestamp(time)
                """
                with conn.cursor() as cursor:
                    cursor.execute(create_table_query)
                    conn.commit()
            
            # 插入或更新数据 - 使用临时表和INSERT IGNORE策略
            for i in range(0, total_records, batch_size):
                batch = records[i:i+batch_size]
                
                with conn.cursor() as cursor:
                    for record in batch:
                        # 检查记录是否已存在
                        check_query = f"""
                        SELECT COUNT(*) FROM {table_name}
                        WHERE symbol = %s AND time = %s
                        """
                        cursor.execute(check_query, [record['symbol'], record['time']])
                        count = cursor.fetchone()[0]
                        
                        # 只有不存在时才插入
                        if count == 0:
                            values = [record[col] if col in record else None for col in columns]
                            insert_query = f"INSERT INTO {table_name} ({columns_str}) VALUES ({placeholders})"
                            cursor.execute(insert_query, values)
                            inserted_count += 1
                    
                    conn.commit()
            
            if inserted_count > 0:
                logger.info(f"成功保存 {inserted_count} 条新数据到 {table_name}，跳过 {total_records - inserted_count} 条重复数据")
            
        except Exception as e:
            logger.error(f"异步保存数据失败: {str(e)}")
    
    async def get_realtime_quotes_db(self, symbols: List[str], 
                                   from_time: Optional[datetime] = None) -> pd.DataFrame:
        """
        从数据库获取实时行情数据
        
        Args:
            symbols: 股票代码列表
            from_time: 开始时间，默认为当前时间前5分钟
            
        Returns:
            pd.DataFrame: 实时行情数据
        """
        if not symbols:
            return pd.DataFrame()
            
        if not from_time:
            from_time = datetime.now() - timedelta(minutes=5)
            
        try:
            # 构建SQL查询 - 直接内联参数
            symbols_str = "','".join(symbols)
            query = f"""
            SELECT * FROM {self.table_name} 
            WHERE symbol IN ('{symbols_str}')
            AND time >= '{from_time.isoformat()}'
            ORDER BY time DESC
            """
            
            # 执行异步查询
            df = await db_manager.execute_questdb_query(query)
            
            # 优化类型转换
            if not df.empty:
                # 时间列转换
                if 'time' in df.columns and not pd.api.types.is_datetime64_any_dtype(df['time']):
                    df['time'] = pd.to_datetime(df['time'])
                
                # 数值列列表
                numeric_columns = [
                    'latest_price', 'avg_price', 'change_pct', 'change_price', 'volume', 
                    'amount', 'turnover', 'volume_ratio', 'high_price', 'low_price', 
                    'open_price', 'pre_close', 'upper_limit', 'lower_limit'
                ]
                # 只转换存在的列
                existing_numeric_cols = [col for col in numeric_columns if col in df.columns]
                if existing_numeric_cols:
                    df[existing_numeric_cols] = df[existing_numeric_cols].astype(float)
            
            return df
            
        except Exception as e:
            logger.error(f"从数据库获取实时行情失败: {str(e)}")
            return pd.DataFrame() 