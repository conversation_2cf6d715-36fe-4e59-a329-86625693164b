/**
 * 🎒 卡牌状态Hook - 简化版
 * 只导出最核心的卡牌管理功�? */

import { useStore } from '../index'

// 🌐 核心卡牌状态Hook
export const useCardsState = () => {
  const inventory = useStore(state => state.inventory)
  const loading = useStore(state => state.loading)
  const error = useStore(state => state.error)
  const loadInventory = useStore(state => state.loadInventory)
  const addCards = useStore(state => state.addCards)
  const consumeCard = useStore(state => state.consumeCard)
  const tempUseCard = useStore(state => state.tempUseCard)
  const releaseTempCard = useStore(state => state.releaseTempCard)
  const getAvailableQuantity = useStore(state => state.getAvailableQuantity)
  const getTotalQuantity = useStore(state => state.getTotalQuantity)
  const searchCards = useStore(state => state.searchCards)
  const clearError = useStore(state => state.clearError)

  return {
    inventory,
    loading,
    error,
    loadInventory,
    addCards,
    consumeCard,
    tempUseCard,
    releaseTempCard,
    getAvailableQuantity,
    getTotalQuantity,
    searchCards,
    clearError
  }
}
