import React, { useMemo } from 'react';
import { Typography, Table, Tooltip, Tag, Space, Badge } from 'antd';
import { motion } from 'framer-motion';
import { ArrowUpOutlined, ArrowDownOutlined } from '@ant-design/icons';

const { Text } = Typography;

// 定义择时信号的类型
export interface TimingSignal {
  symbol: string;
  name?: string;
  direction?: 'BUY' | 'SELL' | 'UNKNOWN';
  trigger_condition?: string;
  latest_price?: number;
  ma_diff_pct?: number;
  market_data?: {
    name?: string;
    price?: number;
    market_cap?: number;
    day60_change?: number;
    industry?: string;
    [key: string]: any;
  };
  [key: string]: any;
}

interface TimingSignalTableProps {
  signals: TimingSignal[];
  className?: string;
}

// 格式化数字的工具函数
const formatNumber = (value: any, unit: string = '', decimalPlaces: number = 2): string => {
  if (value === undefined || value === null) return '-';
  
  const numValue = typeof value === 'string' ? parseFloat(value) : Number(value);
  if (isNaN(numValue)) return '-';
  
  return numValue.toFixed(decimalPlaces) + unit;
};

// 获取涨跌颜色 - 统一使用Dracula主题色
const getChangeColor = (value: number): string => {
  if (value > 0) return '#50fa7b'; // 从#52c41a改为Dracula的绿色
  if (value < 0) return '#ff5555'; // 从#f5222d改为Dracula的红色
  return '#f8f8f2'; // 从#d9d9d9改为Dracula的默认文本色
};

// 获取信号方向的颜色 - 统一使用Dracula主题色
const getDirectionColor = (direction: string): string => {
  if (direction === 'BUY') return '#50fa7b'; // 从#52c41a改为Dracula的绿色
  if (direction === 'SELL') return '#ff5555'; // 从#f5222d改为Dracula的红色
  return '#6272a4'; // 从#d9d9d9改为Dracula的注释色
};

const TimingSignalTable: React.FC<TimingSignalTableProps> = ({ signals = [], className = '' }) => {
  // 标准化处理信号数据
  const signalList = useMemo(() => {
    // 开发环境下记录调试信息
    if (process.env.NODE_ENV !== 'production' && signals.length > 0) {
      console.log('择时信号数据示例:', signals[0]);
    }
    
    return signals.map((signal: TimingSignal) => {
      // 获取股票代码
      const symbol = signal.symbol;
      
      // 优先使用后端整合好的market_data
      const marketData = signal.market_data || {};
      
      // 创建标准化的信号对象
      return {
        key: symbol,
        symbol: symbol,
        name: signal.name || marketData.name || symbol,
        direction: signal.direction || 'UNKNOWN',
        trigger_condition: signal.trigger_condition || '-',
        latest_price: signal.latest_price || marketData.price || 0,
        ma_diff_pct: signal.ma_diff_pct || 0,
        industry: marketData.industry || '-',
        // 保留原始数据以供需要
        originalData: signal
      };
    });
  }, [signals]);
  
  // 动态生成表格列
  const columns = useMemo(() => {
    // 基础列配置
    const baseColumns = [
      {
        title: '代码',
        dataIndex: 'symbol',
        key: 'symbol',
        fixed: 'left' as const,
        width: 90,
        render: (code: string) => (
          <Text style={{ color: '#ff79c6', fontFamily: 'monospace', fontSize: '13px' }}>
            {code}
          </Text>
        ),
      },
      {
        title: '名称',
        dataIndex: 'name',
        key: 'name',
        fixed: 'left' as const,
        width: 100,
        render: (name: string) => (
          <Text style={{ color: '#f8f8f2', fontSize: '13px' }}>
            {name}
          </Text>
        ),
      },
      {
        title: '行业',
        dataIndex: 'industry',
        key: 'industry',
        width: 100,
        render: (industry: string) => (
          <Text style={{ color: '#8be9fd', fontSize: '13px' }}>
            {industry || '-'}
          </Text>
        ),
      },
      {
        title: '最新价',
        dataIndex: 'latest_price',
        key: 'latest_price',
        width: 90,
        render: (price: number) => (
          <Text style={{ color: '#8be9fd', fontFamily: 'monospace', fontSize: '13px' }}>
            {price === undefined || price === null ? '-' : `¥${formatNumber(price)}`}
          </Text>
        ),
      },
      {
        title: '信号方向',
        dataIndex: 'direction',
        key: 'direction',
        width: 100,
        render: (direction: string) => {
          const directionMap = {
            'BUY': '买入',
            'SELL': '卖出',
            'UNKNOWN': '未知'
          };
          
          const directionText = directionMap[direction as keyof typeof directionMap] || direction;
          const color = getDirectionColor(direction);
          
          return (
            <Space>
              {direction === 'BUY' && <ArrowUpOutlined style={{ color }} />}
              {direction === 'SELL' && <ArrowDownOutlined style={{ color }} />}
              <Text style={{ 
                color, 
                fontWeight: 'bold',
                fontSize: '13px' 
              }}>
                {directionText}
              </Text>
            </Space>
          );
        },
      },
      {
        title: '触发条件',
        dataIndex: 'trigger_condition',
        key: 'trigger_condition',
        width: 140,
        render: (condition: string) => (
          <Text style={{ color: '#f1fa8c', fontSize: '13px' }}>
            {condition || '-'}
          </Text>
        ),
      },
      {
        title: '均线偏离(%)',
        dataIndex: 'ma_diff_pct',
        key: 'ma_diff_pct',
        width: 110,
        render: (value: number) => {
          const numValue = typeof value === 'string' ? parseFloat(value) : Number(value);
          
          return (
            <Text style={{ 
              color: getChangeColor(numValue),
              fontFamily: 'monospace', 
              fontSize: '13px' 
            }}>
              {value === undefined || value === null ? '0.00%' : 
              `${numValue > 0 ? '+' : ''}${formatNumber(numValue, '%')}`}
            </Text>
          );
        },
      },
      {
        title: '详细信息',
        dataIndex: 'originalData',
        key: 'originalData',
        width: 90,
        render: (value: any) => (
          <Tooltip title={value ? JSON.stringify(value, null, 2) : '无详细数据'}>
            <Text style={{ color: '#bd93f9', fontSize: '13px' }}>
              {value ? '{...}' : '-'}
            </Text>
          </Tooltip>
        ),
      },
    ];
    
    // 为了避免表格过宽，添加水平滚动支持
    return baseColumns;
  }, [signalList]);

  if (signals.length === 0) {
    return (
      <div className="empty-result">
        <Text>未找到交易信号</Text>
      </div>
    );
  }

  // 统计买入和卖出信号数量
  const buySignals = signalList.filter(signal => signal.direction === 'BUY').length;
  const sellSignals = signalList.filter(signal => signal.direction === 'SELL').length;

  return (
    <motion.div 
      className={`stocks-table ${className}`}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ delay: 0.2 }}
    >
      <div className="results-header">
        <Space>
          <Text strong style={{ color: '#ff79c6' }}>择时信号</Text>
          <Tag color="#ff79c6" style={{ color: '#282a36', fontWeight: 'bold' }}>
            共 {signalList.length} 个信号
          </Tag>
          {buySignals > 0 && (
            <Badge count={buySignals} style={{ backgroundColor: '#50fa7b' }}>
              <Tag color="#50fa7b" style={{ color: '#282a36' }}>买入</Tag>
            </Badge>
          )}
          {sellSignals > 0 && (
            <Badge count={sellSignals} style={{ backgroundColor: '#ff5555' }}>
              <Tag color="#ff5555" style={{ color: '#282a36' }}>卖出</Tag>
            </Badge>
          )}
        </Space>
      </div>

      <Table 
        columns={columns}
        dataSource={signalList}
        size="small"
        pagination={{ 
          pageSize: 10,
          size: 'small',
          className: 'custom-pagination'
        }}
        rowKey="key"
        className="custom-table"
        scroll={{ x: 'max-content', y: 400 }}
      />
    </motion.div>
  );
};

export default React.memo(TimingSignalTable); 