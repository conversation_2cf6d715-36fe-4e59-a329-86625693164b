name: 主力资金追踪策略
description: 基于大单资金流向和主力行为模式分析的智能资金追踪策略
version: 1.0.0
author: QuantCard团队
tags:
  - 量价分析
  - 资金流向
  - 主力追踪
  - 大单分析
  - 行为识别

strategy_class_name: SmartMoneyTrackerStrategy

parameters:
  large_order_threshold:
    type: float
    default: 2.0
    min: 1.5
    max: 5.0
    description: 大单识别阈值（成交量倍数）
  flow_window:
    type: int
    default: 10
    min: 5
    max: 30
    description: 资金流向分析窗口
  min_flow_strength:
    type: float
    default: 0.1
    min: 0.05
    max: 0.5
    description: 最小资金流向强度
  min_control_ratio:
    type: float
    default: 0.2
    min: 0.1
    max: 0.5
    description: 最小主力控盘比例
  accumulation_threshold:
    type: float
    default: 0.6
    min: 0.5
    max: 0.8
    description: 建仓模式识别阈值
  distribution_threshold:
    type: float
    default: 0.6
    min: 0.5
    max: 0.8
    description: 减仓模式识别阈值

data_sources:
  timing:
    database:
      type: questdb
      table: kline_1min
    fields:
      - symbol
      - time
      - open
      - high
      - low
      - close
      - volume
      - amount
    time_field: time
    frequency: 1m
    window_size: 100
    
  backtest:
    database:
      type: questdb
      table: kline_1min
    fields:
      - symbol
      - time
      - open
      - high
      - low
      - close
      - volume
      - amount
    time_field: time
    frequency: 1m
    window_size: 300
    
  monitor:
    database:
      type: questdb
      table: kline_1min
    fields:
      - symbol
      - time
      - open
      - high
      - low
      - close
      - volume
      - amount
    time_field: time
    frequency: 1m
    window_size: 100

outputs:
  timing:
    type: signal
    fields:
      - symbol
      - name
      - direction
      - confidence
      - trigger_condition
      - current_price
      - net_inflow
      - flow_strength
      - control_ratio
      - flow_persistence
      - volume_ratio
      - signal_reason
      - smart_building
      - smart_distributing
      - smart_pulling
      - smart_suppressing
      
  backtest:
    type: backtest_result
    fields:
      - symbol
      - name
      - entry_time
      - entry_price
      - exit_time
      - exit_price
      - return_pct
      - entry_reason
      - money_flow_strength
      
  monitor:
    type: alert
    fields:
      - symbol
      - name
      - alert_type
      - money_flow_status
      - control_ratio
      - alert_time