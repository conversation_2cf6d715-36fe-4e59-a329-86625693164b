import React, { Component, ErrorInfo } from 'react';
import { Result } from 'antd';
import QuantumButton from './QuantumButton';

interface Props {
  children: React.ReactNode;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
}

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
    };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    this.setState({
      error,
      errorInfo,
    });
    console.error('错误详情:', error);
    console.error('错误堆栈:', errorInfo);
  }

  handleReload = () => {
    window.location.reload();
  };

  render() {
    if (this.state.hasError) {
      return (
        <Result
          status="error"
          title="应用程序出错了"
          subTitle="我们很抱歉，请尝试刷新页面"
          extra={[
            <QuantumButton
              key="reload"
              variant="primary"
              onClick={this.handleReload}
            >
              刷新页面
            </QuantumButton>,
          ]}
        />
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary; 