## 目标
- 以"最少冗余、最佳性能、可落地"为准则，统一策略模板参数模型与前后端接口
- 明确参数定义与参数组的边界：参数=业务与校验；参数组=UI 展示
- 利用项目开发测试期优势，直接升级当前架构，避免维护多版本复杂度

## 升级架构（直接替换版）
- 权威模板 Schema，固定字段、驼峰命名
- 后端：服务端参数校验（确保性能） + 模板缓存优化
- 前端：基于现有组件优化，增强游戏化/移动端友好渲染
- 执行侧保持稳定，仅消费参数实值

## 数据模型（升级版Schema）
- StrategyTemplate（升级现有模型）
  - template_id: string
  - name: string
  - version: string
  - description?: string
  - tags: string[]
  - parameters: Record<string, ParameterConfig>
  - parameterGroups: Record<string, ParameterGroup>
  - ui?: UiConfig
  - template_code: string

- ParameterConfig（统一字段结构）
  - type: 'number' | 'text' | 'select' | 'boolean' | 'multi-select'
  - label: string
  - default?: any
  - options?: { label: string; value: any }[]    // 仅 select/multi-select 使用
  - unit?: string
  - validation?: { required?: boolean; min?: number; max?: number; step?: number; pattern?: string }
  - visibleWhen?: { param: string; eq?: any; in?: any[] }  // 支持数组条件，兼容现有逻辑
  - order?: number
  - help?: string
  - 变更：移除顶层 min/max/step/required 字段，统一并入 validation；移除 group 字段

- ParameterGroup（基于现有功能增强）
  - parameters: string[]                // 参数列表，顺序即渲染顺序
  - style?: 'sentence' | 'block' | 'inline'  // 新增sentence样式支持
  - layout?: 'horizontal' | 'vertical'
  - title?: string
  - sentenceTemplate?: string          // 句式模板，如"{operator} {value} {unit}"
  - displayMode?: string               // 兼容现有displayMode字段

- UiConfig（基于现有扩展）
  - icon?: string; color?: string; category?: string; order?: number
  - form?: { layout?: 'vertical' | 'horizontal'; mobile?: { prefer?: 'drawer' | 'modal'; compact?: boolean } }

## API 设计（保持现有端点）
- GET /api/v1/strategy/templates            // 保持现有路径
- GET /api/v1/strategy/templates/{template_id}
  - 返回升级后的 StrategyTemplate 结构
- POST /api/v1/strategy/execute
  - body: { template_id: string; parameters: Record<string, any>; ... }
  - 行为：后端按 validation 规则严格校验，通过后进入执行

## 后端升级方案
- 模型升级
  - 修改现有 ParameterConfig、ParameterGroup、UiConfig 类
  - 保持 StrategyTemplate 类名不变，升级字段结构
  - 添加字段迁移逻辑，自动转换旧格式
- 校验增强
  - 完善 validate_parameters 方法，支持 validation 对象
  - 实现 visibleWhen 条件判断（支持 eq 和 in 条件）
  - 保持执行器接口不变
- 缓存优化
  - 基于现有 TemplateService 增加内存缓存
  - 实现模板热重载机制
  - 添加缓存失效策略

## 前端升级方案（基于现有组件）
- 类型定义更新
  - 更新 StrategyParameter 接口，适配新的 validation 结构
  - 保持现有组件名称，升级实现逻辑
- 渲染器增强  
  - 基于现有 InlineParameterEditor 添加 sentence 模式渲染
  - 优化 ParameterField 组件，支持 validation 对象
  - 保持现有的 visibleWhen 逻辑，扩展支持 in 条件
- 移动端优化
  - 基于现有样式系统，添加移动端优化配置
  - 保持现有的 Drawer/Modal 选择器

## 文件与目录约定（保持现状）
- 模板文件：backend/app/strategies/templates/<template_id>/template.json
- 策略代码：backend/app/strategies/templates/<template_id>/strategy.py
- 保持现有文件结构不变

## 升级示例模板
```json
{
  "template_id": "market_cap_filter",
  "name": "市值",
  "version": "2.0.0",
  "tags": ["选股", "基础筛选"],
  "parameters": {
    "operator": { 
      "type": "select", 
      "label": "条件", 
      "default": "gt", 
      "options": [
      {"label": "大于", "value": "gt"},
      {"label": "小于", "value": "lt"},
      {"label": "区间", "value": "between"}
      ]
    },
    "marketCapMin": { 
      "type": "number", 
      "label": "下限", 
      "default": 100, 
      "unit": "亿元", 
      "validation": { "min": 0, "max": 100000, "step": 1 }, 
      "visibleWhen": { "param": "operator", "in": ["gt", "between"] } 
    },
    "marketCapMax": { 
      "type": "number", 
      "label": "上限", 
      "default": 500, 
      "unit": "亿元", 
      "validation": { "min": 0, "max": 100000, "step": 1 }, 
      "visibleWhen": { "param": "operator", "in": ["lt", "between"] } 
    }
  },
  "parameterGroups": {
    "sentence": { 
      "style": "sentence", 
      "sentenceTemplate": "市值{operator}{marketCapMin}{marketCapMax}", 
      "layout": "horizontal", 
      "parameters": ["operator", "marketCapMin", "marketCapMax"] 
    }
  },
  "ui": { 
    "icon": "filter", 
    "color": "#1890ff", 
    "category": "筛选", 
    "form": { "layout": "vertical", "mobile": { "prefer": "drawer", "compact": true } } 
  },
  "template_code": "market_cap_filter/strategy.py"
}
```

## 实施步骤（3-4天完成）
1) 后端模型升级（1天）
   - 升级 Pydantic 模型，添加字段迁移逻辑
   - 完善参数校验机制
2) 前端组件适配（1天）  
   - 更新类型定义，适配新的 validation 结构
   - 基于现有组件添加 sentence 模式支持
3) 数据迁移脚本（0.5天）
   - 编写模板格式转换脚本
   - 批量升级现有模板文件
4) 集成测试（1天）
   - 前后端联调测试
   - 现有功能回归验证
5) 性能优化（0.5天）
   - 缓存机制调优
   - 响应时间优化

## 升级优势
1. **无版本分裂**：直接升级避免维护多套代码
2. **渐进式改进**：基于现有架构优化，风险可控
3. **保持稳定**：执行层和API端点保持不变
4. **开发友好**：利用测试期优势，一次性解决历史问题
5. **性能提升**：统一字段结构，减少前后端数据转换开销

## 验收标准
- 现有模板功能完全兼容，渲染效果一致或更优
- 新增 sentence 模式和移动端优化生效
- 服务端校验可拦截非法参数并返回详细错误信息  
- 模板加载与执行性能无明显回退（P95 <5ms）
- 代码结构更清晰，技术债务降低

1. 后端策略模板配置分析
通过检查多个模板配置文件，我发现以下不符合升级方案的配置问题：
📋 主要不符合项：
字段分散问题
问题: min/max/step 分布在顶层和 validation 内
示例: turnover_filter 中 min/max 在顶层，ATR_filter 中在 validation 内
需要统一: 全部移入 validation 对象
参数命名不一致
问题: 使用下划线命名如 market_cap1, ATR1
需要改为: 驼峰命名 marketCap1, atr1
字段冗余
问题: 参数中有 group 字段，与 parameterGroups 重复
示例: "group": "ATR_filter" 应该移除
visibleWhen 结构不统一
问题: 大部分用 value 字段，应改为 eq 或 in
示例: "value": "区间" 应改为 "eq": "区间"
UI配置字段需调整
问题: 使用 group 应改为 category
示例: "group": "筛选" 应改为 "category": "筛选"
📊 统计结果：
检查的模板中，约80%需要字段迁移，主要集中在：
校验字段统一（10个模板）
命名规范化（8个模板）
冗余字段清理（12个模板）
2. 前端卡牌显示分析
🎮 当前卡面显示内容：
图标区域: 策略类型图标（📈/⏱️/🛡️）
标题: 策略名称
属性数值:
🗡️ 攻击力（power）- 随机数20-120
🛡️ 防御力（defense）- 随机数20-120
等级: Lv.1-10（随机）
⚠️ 发现的问题：
属性数值无意义: 攻击力/防御力是随机生成的虚假数据
信息不实用: 卡面没有显示策略的实际功能
用户体验差: 用户无法从卡面了解策略用途
3. 卡面显示优化方案建议
🎯 强烈建议：显示核心句式而非虚假属性
设计理念：
实用性优先: 用户一眼就能理解策略功能
游戏化保持: 保持卡牌的视觉魅力
信息密度适中: 移动端友好的信息展示
🎨 具体设计方案：
方案A: 句式替换（推荐）
┌─────────────────┐
│      📈         │ ← 图标保持
│     市值        │ ← 标题
├─────────────────┤
│ 市值 > 100亿元   │ ← 核心句式（新增）
│     ★★☆         │ ← 稀有度
└─────────────────┘