@use "sass:map";
@use "../../styles/themes/variables" as *;

.page-title {
  margin-bottom: map.get($spacing, 6);
  animation: slideIn 0.3s ease-out;

  .title {
    margin: 0 !important;
    display: flex;
    align-items: center;
    gap: map.get($spacing, 2);

    .help-icon {
      transition: map.get($transition, base);

      &:hover {
        color: map.get($color-neutral, gray-600) !important;
        transform: scale(1.1);
      }
    }
  }

  .subtitle {
    color: map.get($color-neutral, gray-600);
    font-size: map.get($font-size, base);
  }
  
  // 操作区样式
  .ant-space-item button {
    margin-left: map.get($spacing, 2);
    transition: all 0.3s;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
} 