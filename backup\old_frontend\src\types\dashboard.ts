/**
 * 仪表盘小部件配置接口
 */
export interface DashboardWidget {
  id: string;
  x: number;
  y: number;
  w: number;
  h: number;
  title: string;
  component: string;
  settings?: Record<string, any>; // 用于存储组件特定的配置
}

/**
 * 小部件模板定义
 */
export interface WidgetTemplate {
  w: number;
  h: number;
  defaultTitle: string;
  defaultSettings?: Record<string, any>;
}

/**
 * 小部件模板映射
 */
export type WidgetTemplatesMap = Record<string, WidgetTemplate>; 