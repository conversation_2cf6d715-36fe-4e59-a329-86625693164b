/**
 * 🎯 策略保存管理器 - Strategy Save Manager
 * 独立的策略保存功能组件，封装所有保存相关的逻辑
 */

import React, { useState, useCallback } from 'react';
import { useCardsState } from '../../../store/hooks/useCardsState';
import { createStrategyGroup } from '../../../services/api/strategy';
import type { StrategyGroup } from '../../../types/game';
import type { StrategyTypeConfig } from '../StrategyTypeSelector/types';

export interface SaveResult {
  success: boolean;
  groupId?: string;
  message: string;
  error?: string;
}

export interface StrategySaveManagerProps {
  currentGroup: StrategyGroup | null;
  strategyTypeConfig: StrategyTypeConfig;
  templateCache: Record<string, any>; // 添加模板缓存
  onSaveComplete: (result: SaveResult) => void;
  children: (props: {
    handleSave: (name: string, description: string) => Promise<void>;
    isSaving: boolean;
    saveError: string | null;
  }) => React.ReactNode;
}

/**
 * 策略保存管理器组件
 * 使用 render props 模式，提供保存功能给子组件
 */
export const StrategySaveManager: React.FC<StrategySaveManagerProps> = ({
  currentGroup,
  strategyTypeConfig,
  templateCache,
  onSaveComplete,
  children
}) => {
  const { consumeCard, releaseTempCard, getTotalQuantity } = useCardsState();
  const [isSaving, setIsSaving] = useState(false);
  const [saveError, setSaveError] = useState<string | null>(null);

  const handleSave = useCallback(async (name: string, description: string) => {
    if (!currentGroup) {
      const error = '没有可保存的策略组';
      setSaveError(error);
      onSaveComplete({ success: false, message: error, error });
      return;
    }

    if (currentGroup.cards.length === 0) {
      const error = '策略组中没有卡牌，无法保存';
      setSaveError(error);
      onSaveComplete({ success: false, message: error, error });
      return;
    }

    setIsSaving(true);
    setSaveError(null);

    try {
      // 1. 验证所有卡片的库存状态
      console.log('开始验证策略组卡片库存...')
      for (const card of currentGroup.cards) {
        const totalQuantity = getTotalQuantity(card.template_id)
        if (totalQuantity < 1) {
          throw new Error(`卡片 ${card.template_id} 库存不足，当前数量: ${totalQuantity}`)
        }
      }

      // 2. 批量消费卡片（consumeCard方法已经处理了临时使用状态的清理）
      console.log('开始消费策略组卡片...')
      const consumeResults: Array<{templateId: string, success: boolean}> = []

      for (const card of currentGroup.cards) {
        console.log(`消费卡片: ${card.template_id}`)
        const success = await consumeCard(card.template_id, 1)
        consumeResults.push({ templateId: card.template_id, success })

        if (!success) {
          // 如果消费失败，记录错误但继续尝试其他卡片
          console.error(`消费卡片 ${card.template_id} 失败`)
          throw new Error(`消费卡片 ${card.template_id} 失败，请检查库存状态`)
        }
      }

      console.log('所有卡片消费成功，消费结果:', consumeResults)

      // 3. 构建策略组保存载荷
      console.log('构建策略组保存载荷...')
      const payload = {
        name: name.trim(),
        description: description.trim(),
        group_type: strategyTypeConfig.groupType,
        timing_symbols: strategyTypeConfig.timingSymbols,
        kline_period: strategyTypeConfig.klinePeriod,
        cards: currentGroup.cards.map(card => {
          const template = templateCache[card.template_id]
          return {
            id: card.id,
            name: template?.name || `策略卡-${card.template_id}`,
            description: template?.description || '',
            parameters: card.parameters || {},
            template_id: card.template_id,
            template_name: template?.name || `策略卡-${card.template_id}`,
            position: card.position || { x: 0, y: 0 }
          }
        }),
        execution_mode: currentGroup.execution_mode,
        execution_config: {
          execution_type: strategyTypeConfig.groupType === 'timing' ? 'continuous' : 'onetime'
        }
      }

      console.log('策略组载荷:', payload)

      // 4. 调用后端API保存策略组
      console.log('调用后端API保存策略组...')
      const response = await createStrategyGroup(payload as any)
      const newGroupId = response?.data?.id || response?.id || ''

      if (!newGroupId) {
        throw new Error('策略组保存失败：未获取到有效的策略组ID')
      }

      console.log('策略组保存成功，ID:', newGroupId)

      const successMessage = `策略组保存成功！\n\n🆔 组ID：${newGroupId}\n📛 名称：${name}\n🎴 卡片数：${currentGroup.cards.length}\n⚙️ 执行模式：${currentGroup.execution_mode}\n🎯 类型：${strategyTypeConfig.groupType === 'timing' ? '择时' : '选股'}`;

      onSaveComplete({
        success: true,
        groupId: newGroupId,
        message: successMessage
      });

    } catch (error) {
      console.error('策略组保存失败:', error)

      let errorMessage = '策略组保存失败'
      let userFriendlyMessage = ''
      let suggestions = ''

      if (error instanceof Error) {
        errorMessage = error.message

        // 根据错误类型提供用户友好的提示
        if (error.message.includes('库存不足') || error.message.includes('数量不足')) {
          userFriendlyMessage = '卡牌库存不足'
          suggestions = '💡 建议：\n• 检查卡包中是否有足够的卡牌\n• 确认卡牌未被其他策略组占用\n• 尝试重新加载库存数据'
        } else if (error.message.includes('消费卡片失败')) {
          userFriendlyMessage = '卡牌消费失败'
          suggestions = '💡 建议：\n• 检查卡牌库存状态\n• 确认网络连接正常\n• 重新尝试保存操作'
        } else if (error.message.includes('网络') || error.message.includes('timeout')) {
          userFriendlyMessage = '网络连接异常'
          suggestions = '💡 建议：\n• 检查网络连接\n• 稍后重试\n• 确认服务器状态正常'
        } else if (error.message.includes('401') || error.message.includes('403') || error.message.includes('登录')) {
          userFriendlyMessage = '身份验证失败'
          suggestions = '💡 建议：\n• 重新登录账户\n• 检查登录状态\n• 清除浏览器缓存后重试'
        } else if (error.message.includes('500') || error.message.includes('服务器')) {
          userFriendlyMessage = '服务器内部错误'
          suggestions = '💡 建议：\n• 稍后重试\n• 联系技术支持\n• 检查服务器状态'
        } else if (error.message.includes('未获取到') || error.message.includes('ID')) {
          userFriendlyMessage = '策略组创建异常'
          suggestions = '💡 建议：\n• 重新尝试保存\n• 检查后端服务状态\n• 联系技术支持'
        } else {
          userFriendlyMessage = errorMessage
          suggestions = '💡 建议：\n• 检查网络连接和卡牌库存\n• 重新尝试操作\n• 如问题持续，请联系技术支持'
        }
      }

      const fullErrorMessage = `❌ ${userFriendlyMessage}\n\n${suggestions}`

      console.error('完整错误信息:', fullErrorMessage)

      setSaveError(fullErrorMessage)
      onSaveComplete({
        success: false,
        message: fullErrorMessage,
        error: errorMessage
      })
    } finally {
      setIsSaving(false)
    }
  }, [currentGroup, strategyTypeConfig, consumeCard, releaseTempCard, onSaveComplete]);

  return (
    <>
      {children({
        handleSave,
        isSaving,
        saveError
      })}
    </>
  );
};

export default StrategySaveManager;
