"""
均线交叉择时策略
"""
import logging
import pandas as pd
import numpy as np
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
import yaml
import json
from pathlib import Path

from app.core.strategy.base import UnifiedStrategyCard
from app.core.runtime.types import RuntimeContext, Signal, SignalType, StrategyMode
from app.services.minute_kline_service import MinuteKlineService

logger = logging.getLogger(__name__)

# 从 template.json 加载配置
template_path = Path(__file__).parent / "template.json"
with open(template_path, "r", encoding="utf-8") as f:
    template = json.load(f)

class MACrossTiming(UnifiedStrategyCard):
    """均线交叉择时策略：使用短期均线和长期均线的交叉来产生交易信号"""
    
    def __init__(self, context: Optional[RuntimeContext] = None):
        """初始化策略"""
        super().__init__(context)
        
        # 加载配置文件
        config_path = Path(__file__).parent / "config.yaml"
        with open(config_path, "r", encoding="utf-8") as f:
            self.config = yaml.safe_load(f)
        
        # 初始化分钟K线服务
        self.kline_service = MinuteKlineService.get_instance()

    async def prepare_data(self, context: RuntimeContext) -> Dict[str, pd.DataFrame]:
        """准备数据
        
        Args:
            context: 运行时上下文
            
        Returns:
            Dict[str, pd.DataFrame]: 股票代码到K线数据的映射
        """
        try:
            # 检查串行执行模式
            if not self._check_sequential_mode(context):
                return {}
            
            # 从context.data_cache获取K线数据 - 由_prepare_timing_data方法准备
            kline_data = context.data_cache.get("kline_data", {})
            if not kline_data:
                self._log("未能从上下文中获取K线数据，请确保已调用_prepare_timing_data方法", "warning")
                return {}
            
            # 获取策略参数
            params = context.parameters
            short_period = params.get("short_period", 5)
            long_period = params.get("long_period", 20)
            
            # 处理每只股票的数据
            for symbol, df in kline_data.items():
                # 确保数据按时间排序
                df.sort_values(by='time', inplace=True)
                
                # 打印最近10条原始行情数据，用于调试
                last_rows = min(10, len(df))
                if last_rows > 0:
                    self._log(f"股票{symbol}最近{last_rows}条原始行情数据:")
                    recent_data = df.tail(last_rows).copy()
                    # 格式化时间为可读格式
                    recent_data['formatted_time'] = recent_data['time'].dt.strftime('%Y-%m-%d %H:%M:%S')
                    # 只显示必要的列以减少日志量
                    debug_cols = ['formatted_time', 'open', 'high', 'low', 'close', 'volume']
                    available_cols = [col for col in debug_cols if col in recent_data.columns]
                    self._log(recent_data[available_cols].to_string())
                
                # 计算均线
                df['short_ma'] = df['close'].rolling(window=short_period).mean()
                df['long_ma'] = df['close'].rolling(window=long_period).mean()
                
                # 打印计算后的最近均线数据，用于调试
                if last_rows > 0:
                    self._log(f"股票{symbol}最近{last_rows}条均线数据 (短期MA={short_period}, 长期MA={long_period}):")
                    recent_ma = df.tail(last_rows)[['time', 'close', 'short_ma', 'long_ma']].copy()
                    recent_ma['formatted_time'] = recent_ma['time'].dt.strftime('%Y-%m-%d %H:%M:%S')
                    recent_ma['ma_diff'] = recent_ma['short_ma'] - recent_ma['long_ma']
                    recent_ma['ma_diff_pct'] = (recent_ma['ma_diff'] / recent_ma['long_ma'] * 100).round(4)
                    self._log(recent_ma[['formatted_time', 'close', 'short_ma', 'long_ma', 'ma_diff', 'ma_diff_pct']].to_string())
            
            self._log(f"成功获取{len(kline_data)}只股票的K线数据")
            return kline_data
            
        except Exception as e:
            self._log(f"准备数据失败: {str(e)}", "error")
            return {}
    
    async def generate_signal(self, data: Dict[str, pd.DataFrame], params: Dict[str, Any]) -> List[Signal]:
        """生成信号
        
        Args:
            data: 股票代码到K线数据的映射
            params: 策略参数
            
        Returns:
            List[Signal]: 信号列表
        """
        try:
            if not data:
                return []
            
            # 获取参数
            short_period = params.get("short_period", 5)
            long_period = params.get("long_period", 20)
            signal_threshold = params.get("signal_threshold", 0.006)  # 默认信号阈值1%
            
            # 记录参数
            self._log(f"策略参数: 短期均线={short_period}, 长期均线={long_period}, 信号阈值={signal_threshold}%")
            
            # 为交叉判断设置更低的阈值
            cross_threshold = min(0.0005, signal_threshold / 10)  # 使用非常小的阈值检测交叉
            self._log(f"交叉判断精度阈值: {cross_threshold:.6f}%")
            
            signals = []
            for symbol, df in data.items():
                if df.empty or len(df) < max(short_period, long_period) + 2:
                    self._log(f"股票{symbol}数据不足，无法计算均线")
                    continue
                
                # 获取最近几个周期的数据进行更准确的交叉判断
                last_n = min(5, len(df))
                recent_df = df.tail(last_n).copy()
                
                # 计算近期均线差值和趋势
                recent_df['ma_diff'] = recent_df['short_ma'] - recent_df['long_ma']
                recent_df['ma_diff_pct'] = (recent_df['ma_diff'] / recent_df['long_ma'] * 100)
                
                # 打印完整的近期行情和均线数据，方便排查
                self._log(f"股票{symbol}近{last_n}个周期的均线差值情况:")
                for i, row in recent_df.iterrows():
                    self._log(f"时间: {row['time'].strftime('%Y-%m-%d %H:%M:%S')}, "
                             f"收盘价: {row['close']:.4f}, "
                             f"短期MA: {row['short_ma']:.4f}, "
                             f"长期MA: {row['long_ma']:.4f}, "
                             f"差值: {row['ma_diff']:.6f}, "
                             f"差值百分比: {row['ma_diff_pct']:.6f}%")
                
                # 获取最新和前一个数据点
                latest = recent_df.iloc[-1]
                previous = recent_df.iloc[-2]
                
                # 当前价格
                current_price = latest['close']
                
                # 详细记录用于交叉判断的数据
                prev_diff = previous['ma_diff']
                latest_diff = latest['ma_diff']
                
                self._log(f"交叉判断关键数据 - 前一周期差值: {prev_diff:.6f}, 最新周期差值: {latest_diff:.6f}")
                self._log(f"符号变化: {'是' if (prev_diff * latest_diff <= 0 and prev_diff != latest_diff) else '否'}")
                
                # 差值百分比的绝对值
                ma_diff_pct_abs = abs(latest['ma_diff_pct'])
                
                # 检查是否存在交叉或接近交叉的情况
                is_crossing = False
                cross_type = ""
                cross_direction = ""
                
                # 判断最近两个周期是否发生了交叉（差值符号变化）
                if prev_diff * latest_diff <= 0 and prev_diff != latest_diff:
                    # 确认发生了交叉（差值跨过了0）
                    is_crossing = True
                    if prev_diff < 0 and latest_diff >= 0:
                        cross_type = "金叉"
                        cross_direction = "BUY"
                    elif prev_diff > 0 and latest_diff <= 0:
                        cross_type = "死叉"
                        cross_direction = "SELL"
                        
                    self._log(f"检测到真实{cross_type}交叉，差值从 {prev_diff:.6f} 变为 {latest_diff:.6f}")
                    
                # 检查是否非常接近交叉（差值百分比非常小）
                elif ma_diff_pct_abs < cross_threshold:
                    # 判断是接近金叉还是死叉
                    if latest_diff > 0 and prev_diff < latest_diff:
                        # 短期均线高于长期均线且差距在扩大，可能刚发生金叉
                        cross_type = "几乎金叉"
                        cross_direction = "BUY"
                        is_crossing = True
                    elif latest_diff < 0 and prev_diff > latest_diff:
                        # 短期均线低于长期均线且差距在扩大，可能刚发生死叉
                        cross_type = "几乎死叉"
                        cross_direction = "SELL"
                        is_crossing = True
                        
                    if is_crossing:
                        self._log(f"检测到{cross_type}情况，差距非常小: {ma_diff_pct_abs:.6f}%")
                
                # 生成交易信号
                if is_crossing:
                    # 计算置信度 - 当交叉非常接近0时给予更高置信度
                    if ma_diff_pct_abs < cross_threshold:
                        confidence = 0.7  # 对于非常接近的交叉给予较高置信度
                    else:
                        confidence = min(0.5 + ma_diff_pct_abs/20, 0.95)  # 正常情况下差距越大置信度越高
                    
                    self._log(f"股票{symbol}出现{cross_type}信号，方向: {cross_direction}，差距: {ma_diff_pct_abs:.6f}%，置信度: {confidence:.2f}")
                    
                    signals.append(
                        self.create_signal(
                            symbol=symbol,
                            name=symbol,
                            direction=cross_direction,
                            signal_type="technical",
                            confidence=confidence,
                            trigger_condition=f"MA{cross_type}_{short_period}_{long_period}",
                            latest_price=float(current_price),
                            ma_diff_pct=float(ma_diff_pct_abs)
                        )
                    )
                else:
                    # 如果没有交叉信号，生成观望信号
                    trend_direction = "向上" if latest_diff > 0 else "向下"
                    
                    self._log(f"股票{symbol}无交叉信号，趋势{trend_direction}，差距{ma_diff_pct_abs:.6f}%")
                    signals.append(
                        self.create_signal(
                            symbol=symbol,
                            name=symbol,
                            direction="HOLD",  # 观望
                            signal_type="technical",
                            confidence=0.5,  # 观望信号置信度较低
                            trigger_condition=f"MA趋势{trend_direction}",
                            latest_price=float(current_price),
                            ma_diff_pct=float(ma_diff_pct_abs)
                        )
                    )
            
            self._log(f"共生成{len(signals)}个信号")
            return signals
            
        except Exception as e:
            self._log(f"生成信号失败: {str(e)}", "error")
            return []
    
    async def execute(self, context: RuntimeContext) -> List[Signal]:
        """执行策略
        
        Args:
            context: 运行时上下文
            
        Returns:
            List[Signal]: 信号列表
        """
        try:
            # 添加执行模式日志
            self._log(f"执行策略模式: {context.mode}")
            
            # 准备数据
            data = await self.prepare_data(context)
            
            # 生成信号
            signals = await self.generate_signal(data, context.parameters)
            
            # 记录结果
            self._log(f"策略执行完成, 生成{len(signals)}个信号")
            
            return signals
            
        except Exception as e:
            self._log(f"策略执行失败: {str(e)}", "error")
            return [] 