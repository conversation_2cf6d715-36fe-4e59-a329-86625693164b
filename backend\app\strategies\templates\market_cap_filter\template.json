{"template_id": "market_cap_filter", "name": "市值", "description": "根据股票市值进行筛选。例：市值小于500亿元。", "version": "2.0.0", "author": "quantcard", "stars": 1, "tags": ["选股", "基础筛选"], "parameters": {"operator": {"type": "select", "label": "条件", "description": "市值比较条件", "default": "大于", "options": [{"label": "大于", "value": "大于"}, {"label": "小于", "value": "小于"}, {"label": "区间", "value": "区间"}]}, "marketCapMin": {"type": "number", "label": "下限", "description": "市值下限（亿元）", "default": 100, "unit": "亿元", "validation": {"required": true, "min": 0, "max": 100000, "step": 1}}, "marketCapMax": {"type": "number", "label": "上限", "description": "市值上限（亿元），仅在'区间'模式下使用", "default": null, "unit": "亿元", "validation": {"min": 0, "max": 100000, "step": 1}, "visibleWhen": {"param": "operator", "in": ["区间"]}}}, "parameterGroups": {"market_cap_filter": {"parameters": ["operator", "marketCapMin", "marketCapMax"], "displayMode": "inline", "prefix": "市值", "separator": "-", "layout": "horizontal"}}, "ui": {"icon": "filter", "color": "#1890ff", "category": "筛选", "order": 1, "form": {"layout": "horizontal", "compact": true, "showDescription": false}}}