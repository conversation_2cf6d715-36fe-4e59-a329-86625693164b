# 策略开发指南

## 执行模式说明

QuantCard平台当前支持三种策略执行模式：

1. **过滤模式 (filter)**：用于筛选股票，生成股票池
2. **择时模式 (timing)**：用于生成买卖信号，执行实时交易决策
3. **回测模式 (backtest)**：用于测试策略在历史数据上的表现

> 注意：之前的 "trade" 模式已更名为 "timing"，"analysis" 模式已移除，以简化系统架构。

## 配置文件结构

每个策略必须包含一个 `config.yaml` 文件，该文件定义了策略的基本信息和数据源配置。配置文件结构如下：

```yaml
# 策略基本信息
name: 策略名称
description: 策略描述
type: 策略类型
category: 策略分类
strategy_class_name: 策略类名
version: 1.0.0

# 数据源配置
data_sources:
  # 过滤模式配置
  filter:
    database:
      type: postgresql
      name: stock_base_info
      table: stock_list_rt
    fields:
      - 代码
      - 名称
      - 其他字段
  
  # 择时模式配置
  timing:
    database:
      type: postgresql
      name: stock_base_info
      table: stock_list_rt
    fields:
      - 代码
      - 名称
      - 其他字段
    
  # 回测模式配置
  backtest:
    database:
      type: postgresql
      name: market_data
      table: stock_historical_quotes
    fields:
      - 代码
      - 名称
      - 其他字段

# 输出配置
outputs:
  filter:
    type: stock_list
    fields:
      - 代码
      - 名称
  
  timing:
    type: signal
    fields:
      - 代码
      - 名称
      - 信号类型
  
  backtest:
    type: backtest_result
    fields:
      - 代码
      - 名称
      - 信号类型
```

## 重要更新说明

从最新版本开始，策略模板文件结构有以下重要更新：

1. 不再使用模式映射，直接使用执行模式作为数据源配置
2. 移除了 `analysis` 模式，简化系统结构
3. 配置文件中的 `data_sources` 和 `outputs` 节点中的键名必须与执行模式名称一致

例如，如果要在择时模式下执行策略，配置文件中必须包含一个 `data_sources.timing` 配置节点。

## 策略开发步骤

1. 创建策略类，继承 `UnifiedStrategyCard` 基类
2. 实现 `prepare_data` 方法，从数据源获取数据
3. 实现 `generate_signal` 方法，生成信号
4. 创建 `config.yaml` 文件，配置数据源和输出
5. 创建 `template.json` 文件，定义策略模板参数

## 执行脚本说明

我们提供了两个脚本来帮助更新现有策略：

1. `remove_mode_mapping.py`：移除策略文件中的模式映射代码
2. `update_config_files.py`：更新配置文件，移除analysis模式

可以通过运行 `run_updates.py` 一键执行所有更新：

```bash
cd backend
python -m app.scripts.run_updates
``` 