from typing import Optional, List, Dict, Any, Literal
from datetime import datetime
from pydantic import BaseModel, Field
from ..core.data.db.base import MongoModel

class ScheduleConfig(BaseModel):
    """调度配置"""
    type: Literal["interval", "cron"]
    value: str
    timezone: Optional[str] = "UTC"

class ExecutionFlow(BaseModel):
    """执行流节点"""
    id: str
    card_id: str
    next: Optional[str]
    parameters: Dict[str, Any]

class CanvasConfig(BaseModel):
    """画布配置"""
    nodes: List[Dict[str, Any]]
    edges: List[Dict[str, Any]]

class StrategyGroup(MongoModel):
    """策略组模型"""
    name: str = Field(..., description="策略组名称")
    description: str = Field(default="", description="策略组描述")
    cards: List[Dict[str, Any]] = Field(default_factory=list, description="包含的策略卡片及其配置")
    status: Literal["active", "inactive", "error"] = Field(
        default="inactive",
        description="运行状态"
    )
    
    # 新增字段
    creation_mode: Literal["simple", "canvas"] = Field(
        default="simple",
        description="策略创建模式：simple/canvas"
    )
    execution_type: Literal["onetime", "continuous"] = Field(
        default="onetime",
        description="执行类型：onetime/continuous"
    )
    execution_mode: Literal["sequential", "parallel"] = Field(
        default="sequential",
        description="简单模式的执行方式：sequential/parallel"
    )
    canvas_config: Optional[CanvasConfig] = Field(
        None,
        description="画布模式的配置信息"
    )
    execution_flow: Optional[List[ExecutionFlow]] = Field(
        None,
        description="执行流程配置"
    )
    schedule_config: Optional[ScheduleConfig] = Field(
        None,
        description="调度配置（持续执行模式）"
    )
    
    # 现有字段
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    created_by: str = Field(..., description="创建者ID")
    is_active: bool = Field(default=True, description="是否激活")
    performance_metrics: Dict[str, Any] = Field(default_factory=dict, description="性能指标")
    execution_logs: List[Dict[str, Any]] = Field(default_factory=list, description="执行日志")
    risk_settings: Dict[str, Any] = Field(default_factory=dict, description="风险控制设置")

    class Config:
        collection = "strategy_groups"
        schema_extra = {
            "example": {
                "name": "示例策略组",
                "description": "这是一个示例策略组",
                "creation_mode": "simple",
                "execution_type": "onetime",
                "execution_mode": "sequential",
                "cards": [
                    {
                        "card_id": "card1",
                        "parameters": {"param1": "value1"}
                    }
                ]
            }
        } 