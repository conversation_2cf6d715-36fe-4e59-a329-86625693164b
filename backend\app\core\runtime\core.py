"""
核心运行时模块

提供策略运行时的基础服务和生命周期管理
管理缓存和共享状态
"""
import logging
from typing import Dict, Any, Optional

from .cache import UnifiedCache

logger = logging.getLogger(__name__)

class RuntimeCore:
    """
    运行时核心类
    
    提供策略运行时的基础功能：
    - 缓存管理
    - 状态维护
    - 生命周期控制
    """
    
    def __init__(self):
        """初始化运行时核心"""
        # 初始化缓存系统
        self._cache = UnifiedCache(
            max_size=1000,
            max_memory_mb=500
        )
        
        # 状态存储
        self._state: Dict[str, Any] = {}
        
        # 市场数据服务引用
        self.market_data_service = None
    
    async def start(self):
        """
        启动运行时
        
        初始化必要的服务和资源
        """
        logger.info("启动策略运行时")
        await self._cache.start()
        logger.info("缓存系统已启动")
    
    async def stop(self):
        """
        停止运行时
        
        清理资源并关闭服务
        """
        logger.info("停止策略运行时")
        await self._cache.stop()
        logger.info("缓存系统已停止")
    
    def get_cache(self) -> UnifiedCache:
        """
        获取缓存实例
        
        Returns:
            UnifiedCache: 缓存管理器实例
        """
        return self._cache
    
    def set_state(self, key: str, value: Any):
        """
        设置状态值
        
        Args:
            key: 状态键
            value: 状态值
        """
        self._state[key] = value
    
    def get_state(self, key: str, default: Any = None) -> Any:
        """
        获取状态值
        
        Args:
            key: 状态键
            default: 默认值（如果键不存在）
            
        Returns:
            状态值
        """
        return self._state.get(key, default)
    
    def clear_state(self):
        """清除所有状态"""
        self._state.clear()
        
    def set_market_data_service(self, service: Any):
        """
        设置市场数据服务
        
        Args:
            service: 市场数据服务实例
        """
        self.market_data_service = service
        logger.info("已设置市场数据服务")
    
    def get_market_data_service(self) -> Optional[Any]:
        """
        获取市场数据服务
        
        Returns:
            市场数据服务实例
        """
        return self.market_data_service 