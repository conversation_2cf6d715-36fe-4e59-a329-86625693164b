@use "sass:map";
@use '../../styles/themes/variables' as *;

.layout-container {
  min-height: 100vh;
  background: map.get($color-neutral, gray-50);
}

.header {
  padding: 0;
  background: map.get($color-neutral, white);
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  height: 64px;
  z-index: map.get($z-index, drawer);
  transition: map.get($menu-transition, all);
  box-shadow: map.get($shadow, sm);
  backdrop-filter: blur(10px);
  background: rgba(map.get($color-neutral, white), 0.9);
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;

  .header-left {
    display: flex;
    align-items: center;
    position: relative;
    z-index: 2;
    flex: 1;

    .logo-container {
      height: 64px;
      padding: 0 map.get($spacing, 4);
      display: flex;
      align-items: center;
      justify-content: flex-start;
      overflow: hidden;
      position: relative;
      margin-right: map.get($spacing, 4);

      .logo-image {
        min-width: 36px;
        width: 36px;
        height: 36px;
        border-radius: map.get($border-radius, full);
        overflow: hidden;
        margin-right: map.get($spacing, 3);
        transition: map.get($menu-transition, transform);
        border: 2px solid transparent;
        
        &:hover {
          transform: map.get($hover-effect, scale);
          border-color: $color-primary;
          box-shadow: map.get($hover-effect, glow);
        }
        
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          transition: map.get($menu-transition, transform);
        }
      }

      .logo-text {
        font-size: map.get($font-size, lg);
        font-weight: 600;
        background: linear-gradient(45deg, $color-primary, map.get($color-primary-light, 500));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        white-space: nowrap;
        opacity: 1;
        transition: map.get($menu-transition, all);
      }
    }

    .top-menu {
      border-bottom: none;
      line-height: 64px;
      flex: 1;

      .ant-menu-item {
        padding: 0 map.get($spacing, 4);
        margin: 0 map.get($spacing, 1);
        position: relative;
        transition: map.get($menu-transition, all);
        height: 64px;
        display: flex;
        align-items: center;
        justify-content: center;

        &:hover {
          color: $color-primary;

          .anticon {
            transform: map.get($hover-effect, scale);
            color: $color-primary;
          }
        }

        &.ant-menu-item-selected {
          color: $color-primary;
          background: transparent;

          &::after {
            border-bottom: 2px solid $color-primary;
          }

          .anticon {
            color: $color-primary;
          }
        }

        .anticon {
          font-size: map.get($font-size, lg);
          transition: map.get($menu-transition, all);
          margin-right: map.get($spacing, 2);
        }

        .ant-menu-title-content {
          font-size: map.get($font-size, base);
          font-weight: 500;
          letter-spacing: 0.5px;
          transition: map.get($menu-transition, all);
        }
      }
    }
  }

  .header-center {
    display: flex;
    justify-content: center;
    flex: 0 1 320px;
    margin: 0 map.get($spacing, 4);

    .search-input {
      width: 100%;

      :global(.ant-input-affix-wrapper) {
        background: map.get($color-neutral, gray-50);
        border-radius: map.get($border-radius, full);
        border: 1px solid map.get($color-neutral, gray-200);
        transition: map.get($menu-transition, all);
        height: 36px;

        &:hover {
          border-color: map.get($color-primary-light, 300);
        }

        &.ant-input-affix-wrapper-focused {
          border-color: $color-primary;
          box-shadow: 0 0 0 2px rgba($color-primary, 0.1);
        }

        .ant-input {
          background: transparent;
          font-size: map.get($font-size, sm);
        }

        .anticon {
          color: map.get($color-neutral, gray-400);
        }
      }
    }
  }

  .header-right {
    padding: 0 map.get($spacing, 4);

    .user-info {
      display: flex;
      align-items: center;
      gap: map.get($spacing, 3);
      padding: map.get($spacing, 2) map.get($spacing, 3);
      border-radius: map.get($border-radius, lg);
      cursor: pointer;
      transition: map.get($menu-transition, all);

      &:hover {
        background: map.get($color-neutral, gray-100);
      }

      .username {
        font-size: map.get($font-size, base);
        font-weight: 500;
        color: map.get($color-neutral, gray-700);

        @media screen and (max-width: 768px) {
          display: none;
        }
      }

      .ant-avatar {
        background: $color-primary;
        transition: map.get($menu-transition, transform);

        &:hover {
          transform: map.get($hover-effect, scale);
        }
      }
    }
  }

  @media screen and (max-width: 768px) {
    .header-left {
      .logo-container {
        margin-right: map.get($spacing, 1);

        .logo-text {
          display: none;
        }
      }

      .top-menu {
        .ant-menu-item {
          padding: 0 map.get($spacing, 2);
        }
      }
    }

    .header-center {
      flex: 0 1 160px;
      margin: 0 map.get($spacing, 1);
    }
  }
}

.content {
  margin: 80px map.get($spacing, 6) map.get($spacing, 6);
  padding: map.get($spacing, 6);
  background: map.get($color-neutral, white);
  border-radius: map.get($border-radius, lg);
  min-height: calc(100vh - 112px);
  transition: map.get($menu-transition, all);
  box-shadow: map.get($shadow, sm);

  @media screen and (max-width: 768px) {
    margin: 80px map.get($spacing, 4) map.get($spacing, 4);
    padding: map.get($spacing, 4);
  }
} 