import React, { ReactNode } from 'react';
import { Typography, Space, Row, Col } from 'antd';
import './PageTitle.scss';

const { Title } = Typography;

interface PageTitleProps {
  title: ReactNode;
  subtitle?: string;
  icon?: ReactNode;
  breadcrumbs?: Array<{
    label: string;
    path?: string;
  }>;
  extra?: ReactNode[];
}

const PageTitle: React.FC<PageTitleProps> = ({
  title,
  subtitle,
  icon,
  breadcrumbs = [],
  extra,
}) => {
  return (
    <div className="page-title">
      <Row justify="space-between" align="middle">
        <Col>
          <Space direction="vertical" size={8}>
            <Space align="center">
              {icon}
              <Title level={2} className="title">
                {title}
              </Title>
            </Space>
            {subtitle && (
              <Typography.Text type="secondary" className="subtitle">
                {subtitle}
              </Typography.Text>
            )}
          </Space>
        </Col>
        {extra && extra.length > 0 && (
          <Col>
            <Space>
              {extra}
            </Space>
          </Col>
        )}
      </Row>
    </div>
  );
};

export default PageTitle; 