/**
 * 🧪 实验室页面组件
 * 提供策略分析工具和快速操作功能
 */

import React from 'react'
import styled from 'styled-components'
import { motion } from 'framer-motion'
import { useUIState } from '../../../store/hooks'

// 🎨 样式组件
const PageSection = styled.div`
  margin-bottom: 2rem;
`

const SectionTitle = styled.h3`
  margin: 0 0 1rem 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
  display: flex;
  align-items: center;
  gap: 0.5rem;
`

const FeatureGrid = styled.div`
  display: grid;
  gap: 1rem;
  margin-bottom: 1.5rem;
`

const FeatureCard = styled(motion.div)`
  background: #ffffff;
  border-radius: 12px;
  padding: 1rem;
  border: 1px solid #e2e8f0;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  
  &:hover {
    border-color: #667eea;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
  }
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px 12px 0 0;
  }
`

const FeatureIcon = styled.div`
  font-size: 2rem;
  margin-bottom: 0.75rem;
  text-align: center;
`

const FeatureTitle = styled.h4`
  margin: 0 0 0.5rem 0;
  font-size: 0.95rem;
  font-weight: 600;
  color: #333;
  text-align: center;
`

const FeatureDescription = styled.p`
  margin: 0;
  font-size: 0.8rem;
  color: #666;
  line-height: 1.4;
  text-align: center;
`

const ActionButton = styled(motion.button)<{ $variant: 'primary' | 'secondary' }>`
  width: 100%;
  padding: 1rem;
  border: none;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  
  ${props => props.$variant === 'primary' ? `
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
  ` : `
    background: #f8fafc;
    color: #333;
    border: 1px solid #e2e8f0;
  `}
  
  &:hover {
    transform: translateY(-1px);
  }
`

const ComingSoonBadge = styled.span`
  position: absolute;
  top: 1rem;
  right: 1rem;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  background: #f59e0b20;
  color: #f59e0b;
  font-size: 0.7rem;
  font-weight: 500;
`

// 🧪 实验室功能配置
const labFeatures = [
  {
    id: 'strategy_optimizer',
    icon: '🎯',
    title: '策略优化器',
    description: '使用AI算法自动优化策略参数，提升策略表现',
    available: true,
    scene: 'StrategyOptimization'
  },
  {
    id: 'backtest_engine',
    icon: '📈',
    title: '回测引擎',
    description: '历史数据回测，验证策略在不同市场环境下的表现',
    available: true,
    scene: 'Backtest'
  },
  {
    id: 'risk_analyzer',
    icon: '🛡️',
    title: '风险分析器',
    description: '深度分析策略风险特征，提供风险控制建议',
    available: false,
    scene: null
  },
  {
    id: 'signal_simulator',
    icon: '⚡',
    title: '信号模拟器',
    description: '模拟策略信号生成过程，调试策略逻辑',
    available: false,
    scene: null
  }
]

// 🎮 实验室页面组件
function LabPage() {
  const { switchScene } = useUIState()

  // 🎯 处理功能选择
  const handleFeatureSelect = (feature: typeof labFeatures[0]) => {
    if (!feature.available) {
      console.log('功能暂未开放:', feature.id)
      return
    }

    if (feature.scene) {
      console.log('跳转到场景:', feature.scene)
      switchScene(feature.scene as any)
    }
  }

  return (
    <PageSection>
      <SectionTitle>
        🧪 实验室
      </SectionTitle>
      
      {/* 快速操作区 */}
      <FeatureGrid>
        <ActionButton
          $variant="primary"
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          onClick={() => switchScene('StrategyCreation')}
        >
          🎯 创建新策略
        </ActionButton>
        
        <ActionButton
          $variant="secondary"
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          onClick={() => switchScene('StrategyOptimization')}
        >
          📈 策略优化
        </ActionButton>
        
        <ActionButton
          $variant="secondary"
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          onClick={() => switchScene('Backtest')}
        >
          📊 回测分析
        </ActionButton>
      </FeatureGrid>

      {/* 实验室功能 */}
      <div style={{ marginTop: '1.5rem' }}>
        <h4 style={{ margin: '0 0 1rem 0', color: '#333', fontSize: '1rem' }}>
          🔬 高级功能
        </h4>
        
        <FeatureGrid>
          {labFeatures.map((feature, index) => (
            <FeatureCard
              key={feature.id}
              onClick={() => handleFeatureSelect(feature)}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 + index * 0.1 }}
              style={{
                opacity: feature.available ? 1 : 0.6,
                position: 'relative'
              }}
            >
              {!feature.available && (
                <ComingSoonBadge>即将推出</ComingSoonBadge>
              )}
              
              <FeatureIcon>{feature.icon}</FeatureIcon>
              <FeatureTitle>{feature.title}</FeatureTitle>
              <FeatureDescription>
                {feature.description}
              </FeatureDescription>
            </FeatureCard>
          ))}
        </FeatureGrid>
      </div>
    </PageSection>
  )
}

export default LabPage
