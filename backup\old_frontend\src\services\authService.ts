import { request, withRetry } from './api';

// 用户类型定义
interface User {
  id: string;
  username: string;
  email?: string;
  createdAt: string;
  updatedAt: string;
  preferences?: {
    theme: 'light' | 'dark';
    notifications: boolean;
    timezone: string;
  };
}

// 登录参数
interface LoginRequest {
  username: string;
  password: string;
}

// 注册参数
interface RegisterParams extends LoginRequest {
  username: string;
}

// 认证响应
// interface LoginResponse {
//   access_token: string;
//   token_type: string;
//   user: {
//     id: string;
//     username: string;
//     email?: string;
//     is_active: boolean;
//     is_superuser: boolean;
//     created_at: string;
//     updated_at: string;
//     last_login?: string;
//     avatar?: string;
//     favorites: string[];
//   };
// }

// 认证响应
interface LoginApiResponse {
  success: boolean;
  message: string;
  data: {
    access_token: string;
    token_type: string;
    user: {
      id: string;
      username: string;
      email?: string;
      is_active: boolean;
      is_superuser: boolean;
      created_at: string;
      updated_at: string;
      last_login?: string;
      avatar?: string;
      favorites: string[];
    };
  };
}

export const authService = {
  // 登录
  login: async (username: string, password: string, remember: boolean): Promise<LoginApiResponse['data']> => {
    // 使用 URLSearchParams 替代 FormData
    const formData = new URLSearchParams();
    formData.append('username', username);
    formData.append('password', password);
    formData.append('grant_type', 'password');  // OAuth2 密码模式需要
    formData.append('remember', remember ? 'true' : 'false');

    try {
      const response = await request<LoginApiResponse>({
        url: 'auth/login',
        method: 'POST',
        data: formData,
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      });
      
      // 验证响应数据
      if (!response.data.access_token || !response.data.user) {
        throw new Error('服务器返回无效的响应数据');
      }

      // 保存 token
      localStorage.setItem('token', response.data.access_token);
      
      return response.data;
    } catch (error: any) {
      console.error('Login error:', error);
      // 处理特定的错误情况
      if (error.response?.status === 401) {
        throw new Error('用户名或密码错误');
      }
      if (error.response?.status === 422) {
        console.error('Validation error:', error.response.data);
        throw new Error('登录失败：' + error.response.data.detail[0].msg);
      }
      throw new Error('登录失败：' + (error.message || '服务器返回无效的响应数据'));
    }
  },

  // 注册
  register: withRetry(async (userData: RegisterParams) => {
    return request<LoginApiResponse>({
      url: 'auth/register',
      method: 'POST',
      data: userData,
    });
  }),

  // 获取当前用户信息
  getCurrentUser: withRetry(async () => {
    return request<User>({
      url: 'users/me',
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
      },
    });
  }),

  // 更新用户信息
  updateUser: withRetry(async (userData: Partial<User>) => {
    return request<User>({
      url: 'users/me',
      method: 'PUT',
      data: userData,
    });
  }),

  // 修改密码
  changePassword: withRetry(async (passwords: { oldPassword: string; newPassword: string }) => {
    return request<void>({
      url: 'users/change-password',
      method: 'POST',
      data: passwords,
    });
  }),

  // 重置密码请求
  requestPasswordReset: withRetry(async (email: { email: string }) => {
    return request<void>({
      url: 'auth/reset-password-request',
      method: 'POST',
      data: email,
    });
  }),

  // 重置密码
  resetPassword: withRetry(async (data: { token: string; newPassword: string }) => {
    return request<void>({
      url: 'auth/reset-password',
      method: 'POST',
      data: data,
    });
  }),

  // 注销
  logout: withRetry(async () => {
    return request<void>({
      url: 'auth/logout',
      method: 'POST',
    });
  }),
}; 