import { IStrategyNode } from '../components/strategy/StrategyCanvas';

interface Point {
  x: number;
  y: number;
}

interface Size {
  width: number;
  height: number;
}

const GRID_SIZE = 20;
const NODE_SIZE = { width: 280, height: 160 };
const PADDING = 40;

/**
 * 计算节点之间的距离
 */
const calculateDistance = (p1: Point, p2: Point): number => {
  const dx = p2.x - p1.x;
  const dy = p2.y - p1.y;
  return Math.sqrt(dx * dx + dy * dy);
};

/**
 * 检查新位置是否与现有节点重叠
 */
const hasOverlap = (position: Point, existingNodes: IStrategyNode[]): boolean => {
  return existingNodes.some(node => {
    const dx = Math.abs(node.position.x - position.x);
    const dy = Math.abs(node.position.y - position.y);
    return dx < NODE_SIZE.width + PADDING && dy < NODE_SIZE.height + PADDING;
  });
};

/**
 * 将坐标对齐到网格
 */
const snapToGrid = (position: Point): Point => {
  return {
    x: Math.round(position.x / GRID_SIZE) * GRID_SIZE,
    y: Math.round(position.y / GRID_SIZE) * GRID_SIZE
  };
};

/**
 * 计算最佳节点位置
 */
export const calculateOptimalPosition = (
  existingNodes: IStrategyNode[],
  canvasSize: Size,
  mousePosition?: Point
): Point => {
  // 如果没有现有节点，使用画布中心或鼠标位置
  if (existingNodes.length === 0) {
    if (mousePosition) {
      return snapToGrid(mousePosition);
    }
    return {
      x: Math.round(canvasSize.width / 2 - NODE_SIZE.width / 2),
      y: Math.round(canvasSize.height / 2 - NODE_SIZE.height / 2)
    };
  }

  // 计算现有节点的边界
  const bounds = existingNodes.reduce(
    (acc, node) => ({
      minX: Math.min(acc.minX, node.position.x),
      maxX: Math.max(acc.maxX, node.position.x),
      minY: Math.min(acc.minY, node.position.y),
      maxY: Math.max(acc.maxY, node.position.y)
    }),
    {
      minX: Infinity,
      maxX: -Infinity,
      minY: Infinity,
      maxY: -Infinity
    }
  );

  // 尝试不同的候选位置
  const candidates: Point[] = [
    // 右侧
    { x: bounds.maxX + NODE_SIZE.width + PADDING, y: bounds.minY },
    // 下方
    { x: bounds.minX, y: bounds.maxY + NODE_SIZE.height + PADDING },
    // 右下
    { x: bounds.maxX + NODE_SIZE.width + PADDING, y: bounds.maxY + NODE_SIZE.height + PADDING },
    // 左侧
    { x: bounds.minX - NODE_SIZE.width - PADDING, y: bounds.minY },
    // 上方
    { x: bounds.minX, y: bounds.minY - NODE_SIZE.height - PADDING }
  ];

  // 如果提供了鼠标位置，将其添加到候选位置中
  if (mousePosition) {
    candidates.unshift(mousePosition);
  }

  // 找到最佳位置
  let bestPosition = candidates[0];
  let minOverlaps = Infinity;

  for (const position of candidates) {
    // 确保位置在画布范围内
    if (
      position.x < 0 ||
      position.y < 0 ||
      position.x + NODE_SIZE.width > canvasSize.width ||
      position.y + NODE_SIZE.height > canvasSize.height
    ) {
      continue;
    }

    // 计算与现有节点的重叠数
    const overlaps = existingNodes.filter(node =>
      hasOverlap(position, [node])
    ).length;

    if (overlaps < minOverlaps) {
      minOverlaps = overlaps;
      bestPosition = position;
    }

    // 如果找到没有重叠的位置，直接使用
    if (overlaps === 0) {
      break;
    }
  }

  // 对齐到网格
  return snapToGrid(bestPosition);
};

/**
 * 自动布局所有节点
 */
export const autoLayout = (
  nodes: IStrategyNode[],
  canvasSize: Size
): IStrategyNode[] => {
  if (nodes.length === 0) return nodes;

  const centerX = canvasSize.width / 2;
  const centerY = canvasSize.height / 2;
  const radius = Math.min(canvasSize.width, canvasSize.height) * 0.3;

  return nodes.map((node, index) => {
    const angle = (2 * Math.PI * index) / nodes.length;
    const x = centerX + radius * Math.cos(angle) - NODE_SIZE.width / 2;
    const y = centerY + radius * Math.sin(angle) - NODE_SIZE.height / 2;

    return {
      ...node,
      position: snapToGrid({ x, y })
    };
  });
};

export const getNodeSize = () => NODE_SIZE;
export const getGridSize = () => GRID_SIZE;
export const getPadding = () => PADDING; 
 