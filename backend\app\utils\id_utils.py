from typing import Optional
from bson import ObjectId
from datetime import datetime

class MongoIDHandler:
    """MongoDB ID处理工具类"""
    
    @staticmethod
    def validate_and_convert(id_str: str) -> Optional[ObjectId]:
        """验证并转换ID字符串为ObjectId"""
        try:
            if not id_str or not ObjectId.is_valid(str(id_str)):
                return None
            return ObjectId(str(id_str))
        except Exception:
            return None

    @staticmethod
    def to_string(obj_id: ObjectId) -> str:
        """将ObjectId转换为字符串"""
        return str(obj_id)

    @staticmethod
    def generate_id() -> ObjectId:
        """生成新的ObjectId"""
        return ObjectId()

    @staticmethod
    def get_timestamp(obj_id: ObjectId) -> datetime:
        """从ObjectId获取时间戳"""
        return obj_id.generation_time 