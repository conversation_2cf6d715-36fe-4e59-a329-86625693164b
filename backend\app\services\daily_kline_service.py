"""
日K线数据服务模块

提供东方财富日K线数据的获取和管理功能，支持日、周、月线
"""
import logging
from typing import List, Optional, Dict, Any, Union, Tuple
import pandas as pd
import asyncio
from datetime import datetime, timedelta
import traceback

from ..core.data.db.base import db_manager
# 代理服务暂时注释，保留未来使用可能性
# from .proxy_service import ProxyService

logger = logging.getLogger(__name__)

class DailyKlineService:
    """日K线数据服务，用于获取东方财富日K线数据"""
    
    VALID_PERIODS = ['daily', 'weekly', 'monthly']
    
    _instances = {}
    
    @classmethod
    def get_instance(cls, *args, **kwargs):
        """获取单例实例"""
        if cls not in cls._instances:
            cls._instances[cls] = cls(*args, **kwargs)
        return cls._instances[cls]
    
    def __init__(self):
        """初始化日K线数据服务"""
        self.table_name = "daily_klines"
        # 初始化内存缓存
        self._symbol_cache = {}  # 按股票代码缓存的完整数据
        self._last_fetch_time = {}  # 记录每个股票+周期的上次获取时间
        # 代理服务相关代码暂时注释，保留未来使用可能性
        # # 获取代理服务实例
        # self.proxy_service = ProxyService.get_instance()
        # # 是否启用代理
        # self.use_proxy = True
        # # 代理失败重试次数
        # self.proxy_retry_count = 3
        
    def _get_table_name(self, period: str) -> str:
        """
        获取表名
        
        Args:
            period: K线周期，如 daily, weekly, monthly
            
        Returns:
            str: 表名
        """
        # 验证周期
        if period not in self.VALID_PERIODS:
            raise ValueError(f"不支持的K线周期: {period}，支持的周期: {', '.join(self.VALID_PERIODS)}")
        
        return f"kline_{period}"

    async def get_klines_batch(self, symbols: List[str], 
                              period: str = 'daily',
                              start_date: Optional[str] = None,
                              end_date: Optional[str] = None,
                              limit: int = 1000,
                              incremental: bool = False,
                              adjust: str = "qfq") -> Dict[str, pd.DataFrame]:
        """
        批量获取多个标的的K线数据
        
        Args:
            symbols: 股票代码列表
            period: K线周期，如 daily, weekly, monthly
            start_date: 开始日期，格式为YYYYMMDD，如果为None且incremental=True，则使用上次获取的时间
            end_date: 结束日期，格式为YYYYMMDD，默认为当前日期
            limit: 每个标的返回的最大记录数
            incremental: 是否使用增量模式，只获取上次之后的新数据
            adjust: 复权模式，qfq-前复权，hfq-后复权，空字符串-不复权
            
        Returns:
            Dict[str, pd.DataFrame]: 标的代码到K线数据的映射
        """
        if not symbols:
            return {}
            
        # 设置默认结束日期为当前日期
        if not end_date:
            end_date = datetime.now().strftime("%Y%m%d")
            
        # 获取表名
        table_name = self._get_table_name(period)
        
        # 创建结果容器
        result = {}
        cache_key = f"klines_{','.join(symbols)}_{period}"
        
        # 增量模式：使用上次获取数据的日期作为起始日期
        if incremental:
            for symbol in symbols:
                symbol_period_key = f"{symbol}_{period}"
                if symbol_period_key in self._last_fetch_time:
                    # 获取上次获取时间之后的数据
                    last_time = self._last_fetch_time[symbol_period_key]
                    start_date_inc = (last_time - timedelta(days=2)).strftime("%Y%m%d")
                    logger.info(f"增量获取标的 {symbol} 的 {period} 数据，上次时间: {last_time}")
                    
                    # 获取单个标的的增量数据
                    df = await self._fetch_single_kline(symbol, period, start_date_inc, end_date, adjust)
                    
                    if df is not None and not df.empty:
                        # 如果已有缓存，则合并新数据
                        if symbol_period_key in self._symbol_cache:
                            # 合并数据并去重
                            cached_df = self._symbol_cache[symbol_period_key]
                            combined_df = pd.concat([cached_df, df]).drop_duplicates(subset=['time']).sort_values('time')
                            # 更新缓存
                            self._symbol_cache[symbol_period_key] = combined_df
                            # 返回完整数据集
                            result[symbol] = combined_df
                        else:
                            # 没有缓存，直接使用新获取的数据
                            self._symbol_cache[symbol_period_key] = df
                            result[symbol] = df
                    elif symbol_period_key in self._symbol_cache:
                        # 没有新数据但有缓存，使用缓存
                        result[symbol] = self._symbol_cache[symbol_period_key]
                else:
                    # 没有上次获取记录，进行全量获取
                    if start_date is None:
                        # 默认获取最近365天数据
                        start_date = (datetime.now() - timedelta(days=365)).strftime("%Y%m%d")
                    
                    df = await self._fetch_single_kline(symbol, period, start_date, end_date, adjust)
                    if df is not None and not df.empty:
                        symbol_period_key = f"{symbol}_{period}"
                        self._symbol_cache[symbol_period_key] = df
                        result[symbol] = df
        else:
            # 非增量模式：普通批量获取
            # 优先从缓存获取数据
            cache_data = db_manager.get_questdb_cache(cache_key)
            if cache_data:
                cache_time = cache_data.get('time', datetime.min)
                # 缓存有效期30分钟
                if (datetime.now() - cache_time).total_seconds() < 1800:
                    logger.info(f"从缓存获取数据: {len(symbols)}个标的, 期间={period}")
                    return cache_data.get('data', {})
                
            # 没有缓存或缓存过期，从API获取数据
            logger.info(f"从API获取K线数据: {symbols}, 周期={period}")
            
            # 设置默认开始日期
            if not start_date:
                # 默认获取最近365天数据
                start_date = (datetime.now() - timedelta(days=365)).strftime("%Y%m%d")
            
            # 并行获取多个标的的数据
            tasks = []
            for symbol in symbols:
                task = asyncio.create_task(self._fetch_single_kline(symbol, period, start_date, end_date, adjust))
                tasks.append((symbol, task))
            
            # 等待所有任务完成
            for symbol, task in tasks:
                try:
                    df = await task
                    if df is not None and not df.empty:
                        # 更新最后获取时间
                        symbol_period_key = f"{symbol}_{period}"
                        self._last_fetch_time[symbol_period_key] = datetime.now()
                        
                        # 缓存完整数据集
                        self._symbol_cache[symbol_period_key] = df
                        
                        result[symbol] = df
                        # 异步保存数据到数据库
                        asyncio.create_task(self._async_save_data(df, self._get_table_name(period)))
                except Exception as e:
                    logger.error(f"获取标的 {symbol} 的K线数据失败: {str(e)}")
            
            # 缓存结果
            if result:
                db_manager.set_questdb_cache(cache_key, {
                    'time': datetime.now(),
                    'data': result
                })
                # 设置缓存过期（30分钟后）
                asyncio.create_task(self._expire_cache(cache_key, 1800))
        
        return result
    
    async def _expire_cache(self, key: str, seconds: int):
        """异步过期缓存"""
        await asyncio.sleep(seconds)
        db_manager.clear_questdb_cache(key)
        
    async def _fetch_single_kline(self, symbol: str, period: str, 
                                 start_date: str,
                                 end_date: str,
                                 adjust: str = "qfq") -> Optional[pd.DataFrame]:
        """
        获取单个标的的K线数据
        
        Args:
            symbol: 股票代码
            period: 周期，如 daily, weekly, monthly
            start_date: 开始日期，格式为YYYYMMDD
            end_date: 结束日期，格式为YYYYMMDD
            adjust: 复权模式，qfq-前复权，hfq-后复权，空字符串-不复权
            
        Returns:
            Optional[pd.DataFrame]: K线数据，获取失败则返回None
        """
        # 导入AKShare
        import akshare as ak
        
        logger.info(f"获取标的 {symbol} 的 {period} 数据")
        
        # 处理周期格式
        freq_map = {
            'daily': '',  # 默认是日线
            'weekly': '周',
            'monthly': '月'
        }
        freq = freq_map.get(period, '')
        
        # 转换日期格式 (YYYYMMDD -> YYYY-MM-DD)
        start_date_formatted = f"{start_date[:4]}-{start_date[4:6]}-{start_date[6:8]}"
        end_date_formatted = f"{end_date[:4]}-{end_date[4:6]}-{end_date[6:8]}"
        
        # 代理服务相关代码暂时注释，保留未来使用可能性
        # # 如果启用代理，则使用代理获取数据
        # retry_count = self.proxy_retry_count if self.use_proxy else 1
        # last_exception = None
        # 
        # for attempt in range(retry_count):
        #     try:
        #         proxies = self.proxy_service.get_request_proxies() if self.use_proxy else None
        #         if proxies:
        #             logger.info(f"尝试 #{attempt+1}: 使用代理 {proxies.get('http')} 获取标的 {symbol} 的 {period} 数据")
        #         
        #         logger.info(f"调用AKShare获取数据: symbol={symbol}, period={period}, adjust={adjust}, 时间范围={start_date_formatted}至{end_date_formatted}")
        #         
        #         # 使用asyncio.to_thread在线程池中执行同步函数
        #         stock_data = await asyncio.to_thread(
        #             ak.stock_zh_a_hist,
        #             symbol=symbol, 
        #             period=freq,
        #             start_date=start_date_formatted,
        #             end_date=end_date_formatted,
        #             adjust=adjust,  # 前复权
        #             proxies=proxies  # 传递代理
        #         )
        #         
        #         if stock_data is None or stock_data.empty:
        #             logger.warning(f"API返回的标的 {symbol} 的 {period} 数据为空")
        #             if self.use_proxy and proxies:
        #                 # 报告代理失败并尝试获取新代理
        #                 proxy_str = proxies.get('http', '').replace('http://', '')
        #                 self.proxy_service.report_proxy_failure(proxy_str)
        #             continue
        #             
        #         logger.info(f"成功获取标的 {symbol} 的 {period} 数据，共 {len(stock_data)} 条记录")
        #         
        #         # 转换列名与数据库字段对应
        #         stock_data = stock_data.rename(columns={
        #             '日期': 'time',
        #             '开盘': 'open',
        #             '收盘': 'close',
        #             '最高': 'high',
        #             '最低': 'low',
        #             '成交量': 'volume',
        #             '成交额': 'amount',
        #             '涨跌幅': 'change_pct',
        #             '换手率': 'turnover',
        #             '振幅': 'amplitude'
        #         })
        #         
        #         # 添加股票代码和其他信息
        #         stock_data['symbol'] = symbol
        #         stock_data['adjust_type'] = adjust
        #         stock_data['period'] = period
        #         stock_data['data_source'] = "eastmoney"
        #         
        #         # 确保时间列是datetime类型
        #         stock_data['time'] = pd.to_datetime(stock_data['time'])
        #         
        #         # 确保数值列是数值类型
        #         numeric_columns = ['open', 'high', 'low', 'close', 'volume', 'amount', 'change_pct', 'turnover', 'amplitude']
        #         for col in numeric_columns:
        #             if col in stock_data.columns:
        #                 stock_data[col] = pd.to_numeric(stock_data[col], errors='coerce')
        #         
        #         return stock_data
        #         
        #     except Exception as e:
        #         last_exception = e
        #         logger.warning(f"尝试 #{attempt+1}: 获取标的 {symbol} 的 {period} 数据失败: {str(e)}")
        #         
        #         if self.use_proxy and proxies:
        #             # 报告代理失败并尝试获取新代理
        #             proxy_str = proxies.get('http', '').replace('http://', '')
        #             self.proxy_service.report_proxy_failure(proxy_str)
        # 
        # # 所有重试都失败
        # logger.error(f"获取标的 {symbol} 的 {period} 数据失败，已重试 {retry_count} 次: {str(last_exception)}")
        # return None
        
        # 使用原有代码，不使用代理
        try:
            logger.info(f"调用AKShare获取数据: symbol={symbol}, period={period}, adjust={adjust}, 时间范围={start_date_formatted}至{end_date_formatted}")
            
            # 使用asyncio.to_thread在线程池中执行同步函数
            stock_data = await asyncio.to_thread(
                ak.stock_zh_a_hist,
                symbol=symbol, 
                period=freq,
                start_date=start_date_formatted,
                end_date=end_date_formatted,
                adjust=adjust  # 前复权
            )
            
            if stock_data is None or stock_data.empty:
                logger.warning(f"API返回的标的 {symbol} 的 {period} 数据为空")
                return None
                
            logger.info(f"成功获取标的 {symbol} 的 {period} 数据，共 {len(stock_data)} 条记录")
            
            # 转换列名与数据库字段对应
            stock_data = stock_data.rename(columns={
                '日期': 'time',
                '开盘': 'open',
                '收盘': 'close',
                '最高': 'high',
                '最低': 'low',
                '成交量': 'volume',
                '成交额': 'amount',
                '涨跌幅': 'change_pct',
                '换手率': 'turnover',
                '振幅': 'amplitude'
            })
            
            # 添加股票代码和其他信息
            stock_data['symbol'] = symbol
            stock_data['adjust_type'] = adjust
            stock_data['period'] = period
            stock_data['data_source'] = "eastmoney"
            
            # 确保时间列是datetime类型
            stock_data['time'] = pd.to_datetime(stock_data['time'])
            
            # 确保数值列是数值类型
            numeric_columns = ['open', 'high', 'low', 'close', 'volume', 'amount', 'change_pct', 'turnover', 'amplitude']
            for col in numeric_columns:
                if col in stock_data.columns:
                    stock_data[col] = pd.to_numeric(stock_data[col], errors='coerce')
            
            return stock_data
            
        except Exception as e:
            logger.error(f"获取标的 {symbol} 的 {period} 数据失败: {str(e)}")
            logger.error(f"错误堆栈: {traceback.format_exc()}")
            return None
    
    async def _async_save_data(self, df: pd.DataFrame, table_name: str) -> None:
        """
        异步保存数据到数据库
        
        Args:
            df: 数据框
            table_name: 表名
        """
        try:
            if df.empty:
                return
                
            # 获取列名
            columns = df.columns.tolist()
            
            # 将DataFrame转换为记录列表
            records = df.to_dict('records')
            
            # 构建INSERT语句
            columns_str = ", ".join(columns)
            placeholders = ", ".join(["%s"] * len(columns))
            
            insert_query = f"""
            INSERT INTO {table_name} ({columns_str})
            VALUES ({placeholders})
            """
            
            # 批量插入数据，每500条一批
            batch_size = 500
            total_records = len(records)
            
            # 获取数据库连接
            conn = await db_manager.get_questdb_connection()
            
            for i in range(0, total_records, batch_size):
                batch = records[i:i+batch_size]
                
                with conn.cursor() as cursor:
                    for record in batch:
                        values = [record[col] for col in columns]
                        cursor.execute(insert_query, values)
                        
                    conn.commit()
                    
            logger.info(f"成功保存 {total_records} 条数据到 {table_name}")
            
        except Exception as e:
            logger.error(f"异步保存数据失败: {str(e)}")
    
    async def get_klines_db(self, symbols: List[str], 
                           period: str = 'daily',
                           from_time: Optional[datetime] = None,
                           to_time: Optional[datetime] = None,
                           limit: int = 1000) -> Dict[str, pd.DataFrame]:
        """
        从数据库获取K线数据
        
        Args:
            symbols: 股票代码列表
            period: K线周期，如 daily, weekly, monthly
            from_time: 开始时间
            to_time: 结束时间，默认为当前时间
            limit: 每个标的返回的最大记录数
            
        Returns:
            Dict[str, pd.DataFrame]: 标的代码到K线数据的映射
        """
        if not symbols:
            return {}
            
        if not to_time:
            to_time = datetime.now()
            
        if not from_time:
            from_time = to_time - timedelta(days=365)
            
        try:
            # 获取表名
            table_name = self._get_table_name(period)
            
            # 创建结果容器
            result = {}
            
            # 构建SQL查询
            placeholders = ','.join(["%s"] * len(symbols))
            query = f"""
            SELECT * FROM {table_name} 
            WHERE symbol IN ({placeholders})
            AND time >= %s AND time <= %s
            ORDER BY symbol, time
            LIMIT {limit}
            """
            
            params = symbols + [from_time, to_time]
            
            # 执行查询
            df = db_manager.execute_questdb_query(query, params)
            
            if df.empty:
                return {}
                
            # 按股票代码分组
            for symbol in symbols:
                symbol_df = df[df['symbol'] == symbol]
                if not symbol_df.empty:
                    result[symbol] = symbol_df
                    
            return result
            
        except Exception as e:
            logger.error(f"从数据库获取K线数据失败: {str(e)}")
            return {}
    
    async def get_last_kline_time(self, symbol: str, period: str) -> Optional[datetime]:
        """获取数据库中最后一条K线数据的时间"""
        try:
            table_name = self._get_table_name(period)
            
            query = f"""
            SELECT MAX(time) FROM {table_name}
            WHERE symbol = '{symbol}'
            """
            
            # 执行查询
            conn = await db_manager.get_questdb_connection()
            with conn.cursor() as cursor:
                cursor.execute(query)
                result = cursor.fetchone()
                
            if result and result[0]:
                return result[0]
            return None
        except Exception as e:
            logger.error(f"获取最后K线时间失败: {str(e)}")
            return None 