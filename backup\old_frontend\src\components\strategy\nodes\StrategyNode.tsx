import React, { memo, useState, useCallback, useEffect } from 'react';
import { <PERSON><PERSON>, Position, NodeProps } from 'reactflow';
import { motion, AnimatePresence } from 'framer-motion';
import classNames from 'classnames';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Popconfirm, Divider } from 'antd';
import { SettingOutlined, DeleteOutlined, EditOutlined, CheckOutlined, CloseOutlined } from '@ant-design/icons';
import { StrategyCardTemplate } from '../../../types/api';
import InlineParameterEditor from '../InlineParameterEditor';
import { sanitizeHtml } from '../../../utils/htmlUtils';
import './StrategyNode.scss';

interface StrategyNodeData {
  id: string;
  name: string;
  description: string;
  parameters: Record<string, any>;
  template?: StrategyCardTemplate & {
    parameters: Record<string, any>;
  };
  onDelete?: (id: string) => void;
  onParameterChange?: (id: string, parameters: Record<string, any>) => void;
  groupType?: 'filter' | 'timing';
}

const StrategyNode: React.FC<NodeProps<StrategyNodeData>> = memo(({ 
  data,
  selected,
  isConnectable,
  id
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [tempParameters, setTempParameters] = useState<Record<string, any>>(data.parameters || {});

  // 当外部参数变化时更新临时参数
  useEffect(() => {
    console.log('Node received new parameters:', {
      nodeId: id,
      parameters: data.parameters
    });
    setTempParameters(data.parameters || {});
  }, [data.parameters, id]);

  const handleDelete = useCallback(() => {
    data.onDelete?.(id);
  }, [data, id]);

  const handleParameterChange = useCallback((parameters: Record<string, any>) => {
    console.log('Parameter changed in editor:', {
      nodeId: id,
      oldParams: tempParameters,
      newParams: parameters
    });
    setTempParameters({ ...parameters });  // 确保创建新的对象
  }, [id, tempParameters]);

  const handleSave = useCallback(() => {
    if (data.onParameterChange) {
      const finalParameters = { ...tempParameters };
      
      // 确保所有必需的参数都有值
      if (data.template?.parameters) {
        Object.entries(data.template.parameters).forEach(([key, param]) => {
          if (finalParameters[key] === undefined && param.default !== undefined) {
            finalParameters[key] = param.default;
          }
        });
      }
      
      console.log('Saving node parameters:', {
        nodeId: id,
        parameters: finalParameters
      });
      
      // 先更新本地状态
      setTempParameters(finalParameters);
      
      // 然后通知父组件
      data.onParameterChange(id, finalParameters);
    }
    setIsEditing(false);
  }, [data, id, tempParameters]);

  const handleCancel = useCallback(() => {
    console.log('Cancelling edit, reverting to:', data.parameters);
    setTempParameters(data.parameters || {});
    setIsEditing(false);
  }, [data.parameters]);

  const handleReadOnlyChange = useCallback(() => {
    // 只读模式下不需要处理变更
  }, []);

  const handleStartEditing = useCallback(() => {
    console.log('Starting edit with parameters:', data.parameters);
    const initialParameters = { ...data.parameters };
    
    // 确保所有参数都有初始值
    if (data.template?.parameters) {
      Object.entries(data.template.parameters).forEach(([key, param]) => {
        if (initialParameters[key] === undefined && param.default !== undefined) {
          initialParameters[key] = param.default;
        }
      });
    }
    
    console.log('Initial parameters for editing:', initialParameters);
    setTempParameters(initialParameters);
    setIsEditing(true);
  }, [data.parameters, data.template]);

  // 渲染参数预览
  const renderParameterPreview = useCallback(() => {
    if (!data.parameters || Object.keys(data.parameters).length === 0) {
      return <div className="empty-parameters">未设置参数</div>;
    }

    return (
      <div className="parameters-preview">
        {Object.entries(data.parameters).map(([key, value]) => (
          <div key={key} className="parameter-item">
            <span className="parameter-key">{key}:</span>
            <span className="parameter-value">{JSON.stringify(value)}</span>
          </div>
        ))}
      </div>
    );
  }, [data.parameters]);

  // 准备增强参数（包含特殊参数）
  const enhancedParams = {
    ...tempParameters,
    '@strategy_mode': data.groupType || 'filter',
  };

  // 准备预览用增强参数
  const enhancedPreviewParams = {
    ...data.parameters,
    '@strategy_mode': data.groupType || 'filter',
  };

  return (
    <>
      <motion.div
        className={classNames("strategy-node", { 
          selected,
          editing: isEditing 
        })}
        initial={{ scale: 0.8, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        transition={{ duration: 0.3 }}
      >
        <Handle
          type="target"
          position={Position.Top}
          isConnectable={isConnectable}
          className="node-handle target"
        />
        
        <div className="node-content">
          <div className="node-header">
            <Tooltip title={<span dangerouslySetInnerHTML={{ __html: sanitizeHtml(data.description) }} />}>
              <h4 className="node-title">{data.name}</h4>
            </Tooltip>
            <div className="node-actions">
              {!isEditing ? (
                <>
                  <Button
                    type="text"
                    size="small"
                    icon={<EditOutlined />}
                    onClick={handleStartEditing}
                  />
                  <Popconfirm
                    title="确定要删除此节点吗？"
                    onConfirm={handleDelete}
                    okText="确定"
                    cancelText="取消"
                  >
                    <Button
                      type="text"
                      size="small"
                      icon={<DeleteOutlined />}
                      className="delete-button"
                    />
                  </Popconfirm>
                </>
              ) : (
                <>
                  <Button
                    type="text"
                    size="small"
                    icon={<CheckOutlined />}
                    className="save-button"
                    onClick={handleSave}
                  />
                  <Button
                    type="text"
                    size="small"
                    icon={<CloseOutlined />}
                    className="cancel-button"
                    onClick={handleCancel}
                  />
                </>
              )}
            </div>
          </div>
          
          <AnimatePresence mode="wait">
            {isEditing ? (
              <motion.div
                className="node-editor"
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                transition={{ duration: 0.2 }}
              >
                <Divider style={{ margin: '8px 0' }} />
                {data.template && (
                  <InlineParameterEditor
                    key={`editor-${id}`}
                    parameters={data.template.parameters}
                    parameterGroups={data.template.parameterGroups}
                    values={enhancedParams}
                    onChange={handleParameterChange}
                    layout="vertical"
                    compact={true}
                    showDescription={true}
                  />
                )}
              </motion.div>
            ) : (
              <motion.div
                className="node-preview"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
              >
                {data.template && (
                  <InlineParameterEditor
                    key={`preview-${id}`}
                    parameters={data.template.parameters}
                    parameterGroups={data.template.parameterGroups}
                    values={enhancedPreviewParams}
                    onChange={handleReadOnlyChange}
                    layout="horizontal"
                    compact={true}
                    showDescription={false}
                    readOnly={true}
                  />
                )}
                <div style={{ 
                  fontSize: '10px', 
                  color: '#666', 
                  marginTop: '4px',
                  padding: '4px',
                  background: '#f5f5f5',
                  borderRadius: '4px',
                  display: 'none'  // 默认隐藏，需要时改为 'block'
                }}>
                  参数: {JSON.stringify(data.parameters)}
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        <Handle
          type="source"
          position={Position.Bottom}
          isConnectable={isConnectable}
          className="node-handle source"
        />
      </motion.div>
    </>
  );
});

StrategyNode.displayName = 'StrategyNode';

export default StrategyNode; 