.cyberpunk-chart {
  position: relative;
  width: 100%;
  height: 100%;
  background: transparent;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;

  // 添加霓虹边框效果
  &::before {
    content: '';
    position: absolute;
    inset: 0;
    padding: 1px;
    border-radius: 12px;
    background: linear-gradient(
      45deg,
      rgba(255, 51, 88, 0.6),
      rgba(189, 147, 249, 0.6),
      rgba(0, 255, 163, 0.6)
    );
    -webkit-mask: 
      linear-gradient(#fff 0 0) content-box, 
      linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
    mask-composite: exclude;
    opacity: 0;
    transition: opacity 0.5s ease;
    pointer-events: none;
  }
  
  &:hover::before {
    opacity: 1;
    animation: borderGlow 3s infinite alternate;
  }

  &.fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 1000;
    border-radius: 0;
    
    &::before {
      border-radius: 0;
    }
    
    .chart-content {
      height: calc(100vh - 130px) !important;
      transform-origin: center center !important;
      
      .js-plotly-plot {
        width: 100% !important;
        height: 100% !important;
      }
    }
  }

  .chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: rgba(19, 20, 27, 0.8);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--quantum-border);
    position: relative;
    overflow: hidden;
    height: 64px;
    
    // 添加动态背景效果
    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 200%;
      height: 100%;
      background: linear-gradient(
        90deg,
        transparent,
        rgba(189, 147, 249, 0.1),
        rgba(255, 51, 88, 0.1),
        rgba(0, 255, 163, 0.1),
        transparent
      );
      animation: headerShine 8s linear infinite;
      pointer-events: none;
    }

    .chart-title {
      display: flex;
      align-items: center;
      gap: 8px;
      min-width: 150px;
      flex-shrink: 0;

      .anticon {
        font-size: 20px;
        color: var(--quantum-primary);
        animation: pulse 2s infinite alternate;
      }

      h5.ant-typography {
        margin: 0;
        color: var(--quantum-text-light);
        text-shadow: 0 0 8px rgba(189, 147, 249, 0.5);
        transition: text-shadow 0.3s ease;
        white-space: nowrap;
        
        &:hover {
          text-shadow: 0 0 12px rgba(189, 147, 249, 0.8);
        }
      }
    }

    .chart-controls {
      flex: 1;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      gap: 12px;
      
      .board-type-toggle {
        margin-right: auto;
        display: flex;
        flex-direction: row;
        
        .ant-radio-button-wrapper {
          background: transparent;
          border-color: var(--quantum-border);
          color: var(--quantum-text-light);
          height: auto;
          padding: 4px 12px;
          display: inline-flex;
          align-items: center;
          justify-content: center;
          white-space: nowrap;
          transition: all 0.3s ease;
          min-width: 80px;

          &:hover {
            color: var(--quantum-primary);
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(189, 147, 249, 0.3);
          }

          &.ant-radio-button-wrapper-checked {
            background: var(--quantum-primary);
            border-color: var(--quantum-primary);
            color: var(--quantum-text-dark);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(189, 147, 249, 0.5);
          }
        }
      }

      .dropdown-button {
            background: transparent;
            border-color: var(--quantum-border);
        color: var(--quantum-text-light);
        height: 32px;
        padding: 0 12px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        white-space: nowrap;
        transition: all 0.3s ease;
        
        .anticon {
          margin-left: 6px;
          font-size: 12px;
        }
        
        &:hover {
          color: var(--quantum-primary);
          border-color: var(--quantum-primary);
          background: rgba(189, 147, 249, 0.1);
        }
      }
      
      .control-buttons {
        display: flex;
        align-items: center;
        gap: 4px;

      .control-button {
        color: var(--quantum-text-light);
          transition: all 0.3s ease;
        
        &:hover {
          color: var(--quantum-primary);
          background: rgba(255, 255, 255, 0.1);
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(189, 147, 249, 0.3);
          }
        }
      }
    }
  }

  .chart-content {
    position: relative;
    width: 100%;
    height: calc(100% - 130px);
    transition: all 0.3s ease;
    transform-origin: center center;
    overflow: hidden;

    .js-plotly-plot {
      width: 100% !important;
      height: 100% !important;
      transition: all 0.3s ease;
    }

    .loading-overlay,
    .empty-overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      gap: 16px;
      background: rgba(19, 20, 27, 0.8);
      backdrop-filter: blur(10px);
      z-index: 10;

      .anticon {
        font-size: 48px;
        color: var(--quantum-primary);
        animation: pulse 2s infinite alternate;
      }

      .ant-typography {
        color: var(--quantum-text-light);
        text-shadow: 0 0 8px rgba(189, 147, 249, 0.5);
      }
    }
  }

  .chart-footer {
    position: relative;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 8px 24px;
    background: rgba(19, 20, 27, 0.8);
    backdrop-filter: blur(10px);
    border-top: 1px solid var(--quantum-border);
    transition: all 0.3s ease;
    height: 66px;
    z-index: 5;

    .chart-footer-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 100%;

    .chart-legend {
      display: flex;
      align-items: center;
      gap: 24px;

        .zoom-controls {
        display: flex;
        align-items: center;
        gap: 8px;
          margin-right: 24px;
          
          .control-button {
            color: var(--quantum-text-light);
            transition: all 0.3s ease;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 4px;
            
            &:hover {
              color: var(--quantum-primary);
              background: rgba(189, 147, 249, 0.1);
              transform: translateY(-1px);
              box-shadow: 0 2px 8px rgba(189, 147, 249, 0.3);
            }
            
            .anticon {
              font-size: 16px;
            }
          }
        }

        .legend-gradient {
          display: flex;
          align-items: center;
          justify-content: space-between;
          gap: 8px;
          position: relative;

        &.change-percent {
          width: 200px;
          height: 24px;
          background: linear-gradient(
            to right,
              rgb(0, 255, 163),
            rgb(248, 248, 242),
              rgb(255, 51, 88)
          );
          border-radius: 4px;
          position: relative;
            box-shadow: 0 0 12px rgba(189, 147, 249, 0.3);
            transition: all 0.3s ease;
            
            &:hover {
              transform: scale(1.05);
              box-shadow: 0 0 16px rgba(189, 147, 249, 0.5);
            }

          .legend-label {
            position: absolute;
            color: var(--quantum-text-light);
            font-size: 12px;
            font-family: 'JetBrains Mono', monospace;
              text-shadow: 0 0 4px rgba(0, 0, 0, 0.5);
              transition: all 0.3s ease;

            &:first-child {
              left: -32px;
            }

            &:last-child {
              right: -32px;
            }
          }
        }

        &.price-strength {
          width: 200px;
          height: 24px;
          background: linear-gradient(
            to right,
            rgb(139, 233, 253),
            rgb(189, 147, 249),
            rgb(255, 121, 198)
          );
          border-radius: 4px;
            box-shadow: 0 0 12px rgba(189, 147, 249, 0.3);
            transition: all 0.3s ease;
            
            &:hover {
              transform: scale(1.05);
              box-shadow: 0 0 16px rgba(189, 147, 249, 0.5);
            }
            
            .legend-label {
              position: absolute;
              color: var(--quantum-text-light);
              font-size: 12px;
              font-family: 'JetBrains Mono', monospace;
              text-shadow: 0 0 4px rgba(0, 0, 0, 0.5);

              &:first-child {
                left: -20px;
              }

              &:last-child {
                right: -20px;
              }
            }
          }
          
          &.turnover {
            width: 200px;
            height: 24px;
            background: linear-gradient(
              to right,
              rgb(139, 233, 253),
              rgb(189, 147, 249)
            );
            border-radius: 4px;
            box-shadow: 0 0 12px rgba(189, 147, 249, 0.3);
            transition: all 0.3s ease;
            
            .legend-label {
              position: absolute;
              color: var(--quantum-text-light);
              font-size: 12px;
              font-family: 'JetBrains Mono', monospace;
              text-shadow: 0 0 4px rgba(0, 0, 0, 0.5);

              &:first-child {
                left: -20px;
              }

              &:last-child {
                right: -20px;
              }
            }
          }
        }
      }
      
      .data-timestamp {
        margin: 0;
        font-size: 12px;
        color: var(--quantum-text-light);
        opacity: 0.7;
        transition: opacity 0.3s ease;
        white-space: nowrap;
        
        &:hover {
          opacity: 1;
        }
      }
    }
  }

  // 悬浮提示框动画
  .js-plotly-plot .plotly .hoverlabel {
    transition: all 0.2s ease;
    opacity: 0;
    transform: translateY(10px);
    border-radius: 6px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);

    &.entered {
      opacity: 1;
      transform: translateY(0);
    }
  }

  // 板块切换动画
  .js-plotly-plot .plotly .trace {
    transition: all 0.3s ease;
  }
}

// 下拉菜单样式
.ant-dropdown {
  .ant-dropdown-menu {
  background: rgba(19, 20, 27, 0.95) !important;
  backdrop-filter: blur(10px);
  border: 1px solid var(--quantum-border);
    border-radius: 8px;
    overflow: hidden;
    animation: dropdownFadeIn 0.3s ease;

    .ant-dropdown-menu-item {
    color: var(--quantum-text-light);
    transition: all 0.2s ease;
      padding: 8px 16px;

    &:hover {
      background: rgba(255, 255, 255, 0.1);
        color: var(--quantum-primary);
      }
    }
  }
}

// 删除自定义悬浮提示框样式
.cyberpunk-heatmap-tooltip,
.heatmap-custom-tooltip {
  display: none; // 隐藏自定义提示框
}

// 增强Plotly默认提示框样式
.js-plotly-plot .plotly .hoverlabel {
  background-color: rgba(40, 42, 54, 0.95) !important;
  border: 1px solid #44475a !important;
  border-radius: 6px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
  transition: all 0.2s cubic-bezier(0.23, 1, 0.32, 1) !important;
  font-family: 'JetBrains Mono', monospace !important;
  
  .hovertext {
    font-family: 'JetBrains Mono', monospace !important;
    font-size: 12px !important;
    padding: 8px !important;
  }
}

// 动画定义
@keyframes pulse {
  0% {
    opacity: 0.7;
    transform: scale(1);
  }
  100% {
    opacity: 1;
    transform: scale(1.05);
  }
}

@keyframes borderGlow {
  0% {
    opacity: 0.3;
  }
  100% {
    opacity: 1;
  }
}

@keyframes headerShine {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(50%);
  }
}

@keyframes dropdownFadeIn {
  0% {
    opacity: 0;
    transform: translateY(-10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

// 媒体查询，确保在小屏幕上的响应式布局
@media (max-width: 992px) {
  .cyberpunk-chart {
    .chart-header {
      height: auto;
      flex-wrap: wrap;
      padding: 12px;
      
      .chart-title {
        margin-bottom: 8px;
      }
      
      .chart-controls {
        width: 100%;
        flex-wrap: wrap;
        gap: 8px;
        
        .board-type-toggle {
          margin-right: 0;
          width: auto;
          
          .ant-radio-button-wrapper {
            min-width: 60px;
          }
        }
      }
    }
    
    .chart-content {
      height: calc(100% - 160px);
    }

    .chart-footer {
      .chart-footer-content {
        flex-direction: column;
        gap: 8px;
        
        .chart-legend {
          justify-content: center;
        }
        
        .data-timestamp {
          text-align: center;
        }
      }
    }
  }
}

.heatmap-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 12px;
  height: 34px; // 减少标题栏高度
  border-bottom: 1px solid #2b2d3a;
  background-color: #191a21;
  
  .heatmap-title {
    font-size: 14px;
    color: #f8f8f2;
    user-select: none;
  }
  
  .heatmap-title-placeholder {
    width: 30px;
    height: 14px;
  }
  
  .heatmap-controls {
    .ant-btn {
      color: #6272a4;
      
      &:hover {
        color: #bd93f9;
      }
    }
    
    .ant-radio-group {
      margin-right: 10px;
      
      .ant-radio-button-wrapper {
        border-color: #44475a;
        background-color: #282a36;
        color: #6272a4;
        font-size: 12px;
        height: 24px;
        line-height: 22px;
        padding: 0 8px;
        
        &:hover {
          color: #bd93f9;
        }
        
        &.ant-radio-button-wrapper-checked {
          border-color: #bd93f9;
          background-color: #bd93f9;
          color: #f8f8f2;
          
          &::before {
            background-color: #bd93f9;
          }
        }
      }
    }
    
    .ant-select {
      margin-right: 4px;
      
      .ant-select-selector {
        border-color: #44475a;
        background-color: #282a36;
        height: 24px !important;
        
        .ant-select-selection-item {
          color: #f8f8f2;
          line-height: 22px;
          font-size: 12px;
        }
      }
      
      &.ant-select-focused .ant-select-selector {
        border-color: #bd93f9;
        box-shadow: 0 0 0 2px rgba(189, 147, 249, 0.2);
      }
      
      .ant-select-arrow {
        color: #6272a4;
      }
    }
  }
}

/* 修改Select组件样式防止闪烁 */
.heatmap-controls {
  .ant-select {
    margin-right: 4px;
    
    .ant-select-selector {
      border-color: #44475a;
      background-color: #282a36;
      height: 24px !important;
      transition: all 0.2s cubic-bezier(0.23, 1, 0.32, 1) !important;
      
      .ant-select-selection-item {
        color: #f8f8f2;
        line-height: 22px;
        font-size: 12px;
      }
    }
    
    &.ant-select-focused .ant-select-selector {
      border-color: #bd93f9;
      box-shadow: 0 0 0 2px rgba(189, 147, 249, 0.2);
    }
    
    .ant-select-arrow {
      color: #6272a4;
      transition: transform 0.2s cubic-bezier(0.23, 1, 0.32, 1);
    }
    
    &.ant-select-open .ant-select-arrow {
      transform: rotate(180deg);
    }
  }
}

// 添加新的稳定的下拉菜单样式
:global(.ant-select-dropdown.cyberpunk-dropdown) {
  background-color: #282a36 !important;
  border: 1px solid #44475a !important;
  border-radius: 4px !important;
  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.3) !important;
  transform: translateZ(0) !important; // 启用硬件加速
  will-change: opacity, transform !important; // 提前准备动画
  animation: none !important;
  opacity: 1 !important;
  transition: none !important; // 禁用可能导致闪烁的默认过渡效果

  :global(.ant-select-item) {
    color: #f8f8f2 !important;
    padding: 5px 8px !important;
    font-size: 12px !important;
    transition: background-color 0.1s ease !important;

    &:hover {
      background-color: rgba(189, 147, 249, 0.1) !important;
    }

    &.ant-select-item-option-selected {
      background-color: rgba(189, 147, 249, 0.2) !important;
      font-weight: 500 !important;
    }
  }
}

// 修改Select本身的样式，稳定下拉箭头和边框
.heatmap-controls {
  .ant-select {
    &:hover .ant-select-selector {
      border-color: #bd93f9 !important;
      box-shadow: none !important;
    }

    .ant-select-selector {
      transition: none !important; // 禁用可能导致闪烁的过渡效果
    }

    .ant-select-arrow {
      transition: none !important; // 禁用可能导致闪烁的过渡效果
    }
  }
}

