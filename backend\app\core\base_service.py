from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
from bson import ObjectId
from ..utils.error_utils import <PERSON>rrorHandler
from ..utils.response_utils import ResponseFormatter

class BaseService:
    """基础服务类"""
    
    @staticmethod
    def validate_id(id_str: str, entity: str = "对象") -> ObjectId:
        """验证并转换ID"""
        try:
            if not ObjectId.is_valid(id_str):
                ErrorHandler.raise_invalid_id(entity, id_str)
            return ObjectId(id_str)
        except Exception as e:
            ErrorHandler.raise_invalid_id(entity, id_str)
    
    @staticmethod
    async def paginate_query(
        model,
        query: Dict[str, Any],
        skip: int = 0,
        limit: int = 100,
        sort_field: str = "created_at",
        sort_order: int = -1
    ) -> Tuple[List[Dict[str, Any]], int]:
        """通用分页查询"""
        try:
            # 构建聚合管道
            pipeline = [
                {"$match": query},
                {"$sort": {sort_field: sort_order}},
                {"$skip": skip},
                {"$limit": limit}
            ]
            
            # 执行查询
            results = await model.aggregate(pipeline).to_list(length=None)
            total = await model.count_documents(query)
            
            # 格式化响应
            formatted_results = [
                ResponseFormatter.format_document(doc) for doc in results
            ]
            
            return formatted_results, total
            
        except Exception as e:
            ErrorHandler.raise_operation_failed(
                operation="查询",
                entity=model.__name__,
                details={"error": str(e)}
            )
    
    @staticmethod
    def handle_time_range(
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        field_name: str = "timestamp"
    ) -> Dict[str, Any]:
        """处理时间范围查询"""
        query = {}
        if start_time or end_time:
            query[field_name] = {}
            if start_time:
                query[field_name]["$gte"] = start_time
            if end_time:
                query[field_name]["$lte"] = end_time
        return query
    
    @staticmethod
    def handle_error(func):
        """错误处理装饰器"""
        async def wrapper(*args, **kwargs):
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                ErrorHandler.raise_operation_failed(
                    operation=func.__name__,
                    entity=func.__qualname__.split('.')[0],
                    details={"error": str(e)}
                )
        return wrapper 