from typing import Optional, List, Dict, Any, Literal
from datetime import datetime
from pydantic import BaseModel, Field
from ..core.data.db.base import MongoModel

class LogEntry(BaseModel):
    """日志条目"""
    timestamp: datetime
    level: Literal["info", "warning", "error", "success"]
    message: str
    details: Optional[Dict[str, Any]] = None

class ExecutionLog(MongoModel):
    """执行日志模型"""
    strategy_group_id: str = Field(..., description="策略组ID")
    execution_id: str = Field(..., description="执行ID")
    start_time: datetime = Field(default_factory=datetime.utcnow)
    end_time: Optional[datetime] = None
    status: Literal["pending", "running", "completed", "failed"] = Field(
        default="pending",
        description="执行状态"
    )
    execution_type: Literal["onetime", "continuous"] = Field(
        ...,
        description="执行类型"
    )
    logs: List[LogEntry] = Field(default_factory=list)
    performance_metrics: Dict[str, Any] = Field(default_factory=dict)
    error: Optional[Dict[str, Any]] = None

    class Config:
        collection = "execution_logs"
        schema_extra = {
            "example": {
                "strategy_group_id": "group1",
                "execution_id": "exec1",
                "execution_type": "onetime",
                "logs": [
                    {
                        "timestamp": "2024-01-20T10:00:00Z",
                        "level": "info",
                        "message": "开始执行策略"
                    }
                ]
            }
        } 