"""
择时策略数据管理器模块

提供择时策略组数据管理功能，实现统一K线周期获取、缓存和共享
"""
import logging
import asyncio
from typing import List, Dict, Any, Optional
import pandas as pd
from datetime import datetime, timedelta

from .minute_kline_service import MinuteKlineService
from .daily_kline_service import DailyKlineService
from .realtime_quote_service import RealTimeQuoteService

logger = logging.getLogger(__name__)

class TimingDataManager:
    """
    择时策略数据管理器
    
    负责管理择时策略组数据的获取、缓存和清理，实现优化数据流程:
    1. 策略组层面获取并共享数据，避免每个策略卡重复获取
    2. 首次运行初始化历史数据，后续仅更新最新数据
    """
    
    _instance = None
    
    @classmethod
    def get_instance(cls) -> 'TimingDataManager':
        """获取单例实例"""
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance
        
    def __init__(self):
        """初始化择时策略数据管理器"""
        self.minute_service = MinuteKlineService.get_instance()
        self.daily_service = DailyKlineService.get_instance()
        self.realtime_service = RealTimeQuoteService.get_instance()
        
        # 缓存数据
        self._data_cache = {}  # 格式: {group_id: {symbol: pd.DataFrame}}
        self._period_cache = {}  # 格式: {group_id: period}
        self._initialized = set()  # 已初始化的策略组ID集合
        self._last_update_time = {}  # 格式: {group_id: datetime}
        self._ttl_settings = {}  # 格式: {group_id: timedelta}

    def _get_period_timedelta(self, period: str) -> timedelta:
        """获取K线周期对应的时间间隔"""
        period_map = {
            '1min': timedelta(minutes=1),
            '5min': timedelta(minutes=5),
            '15min': timedelta(minutes=15),
            '30min': timedelta(minutes=30),
            '60min': timedelta(minutes=60),
            'daily': timedelta(days=1),
            'weekly': timedelta(days=7),
            'monthly': timedelta(days=30)
        }
        return period_map.get(period, timedelta(minutes=5))

    async def get_or_update_group_data(self, group_id: str, symbols: List[str], period: str, days: Optional[int] = None, force_update: bool = False) -> Dict[str, pd.DataFrame]:
        """
        统一的数据获取或更新入口 - 根据状态自动选择初始化或更新
        
        Args:
            group_id: 策略组ID
            symbols: 标的列表
            period: K线周期
            days: 回溯天数
            force_update: 是否强制更新（忽略缓存）
            
        Returns:
            Dict[str, pd.DataFrame]: 股票代码到K线数据的映射
        """
        if not symbols:
            logger.warning(f"获取策略组[{group_id}]数据失败: 标的列表为空")
            return {}
        
        # 检查是否已初始化且是否需要强制更新
        if not self.is_group_initialized(group_id) or force_update:
            # 首次初始化或强制刷新
            return await self.initialize_group_data(group_id, symbols, period, days)
        else:
            # 增量更新现有数据
            return await self.update_group_data(group_id, symbols)
        
    async def initialize_group_data(self, group_id: str, symbols: List[str], period: str, days: Optional[int] = None) -> Dict[str, pd.DataFrame]:
        """
        初始化策略组数据 - 获取历史K线数据并缓存
        
        Args:
            group_id: 策略组ID
            symbols: 标的列表
            period: K线周期
            days: 回溯天数
            
        Returns:
            Dict[str, pd.DataFrame]: 股票代码到K线数据的映射
        """
        if not symbols:
            logger.warning(f"初始化策略组[{group_id}]数据失败: 标的列表为空")
            return {}
        
        # 记录K线周期
        self._period_cache[group_id] = period
        
        # 自动设置回溯天数
        if days is None:
            days_lookup = {'1min': 1, '5min': 3, '15min': 7, '30min': 14, '60min': 30}
            days = days_lookup.get(period, 7)
        
        # 获取历史数据
        try:
            from_time = datetime.now() - timedelta(days=days)
            
            # 首次运行只获取K线数据，不获取实时行情
            kline_data = {}
            
            # 根据周期选择数据服务
            if period in MinuteKlineService.VALID_PERIODS:
                    kline_data = await self.minute_service.get_klines_batch(
                        symbols=symbols,
                        period=period,
                        from_time=from_time,
                    to_time=None,
                    save_to_db=False  # 不保存到数据库，提高性能
                    )
            elif period in DailyKlineService.VALID_PERIODS:
                    start_date = from_time.strftime("%Y%m%d")
                    kline_data = await self.daily_service.get_klines_batch(
                        symbols=symbols,
                        period=period,
                        start_date=start_date,
                    end_date=None,
                    save_to_db=False  # 不保存到数据库，提高性能
                    )
            else:
                logger.error(f"不支持的K线周期: {period}")
                return {}
            
            # 更新缓存
            self._data_cache[group_id] = kline_data
            self._initialized.add(group_id)
            self._last_update_time[group_id] = datetime.now()
            
            logger.info(f"策略组[{group_id}]数据初始化完成: {len(kline_data)}个标的, 周期={period}")
            return kline_data
            
        except Exception as e:
            logger.error(f"初始化策略组[{group_id}]数据失败: {str(e)}")
            return {}
    
    async def update_group_data(self, group_id: str, symbols: List[str]) -> Dict[str, pd.DataFrame]:
        """
        更新策略组数据 - 只获取最新报价，并与历史数据合并
        
        Args:
            group_id: 策略组ID
            symbols: 标的列表
            
        Returns:
            Dict[str, pd.DataFrame]: 更新后的完整数据
        """
        # 检查是否已初始化
        if group_id not in self._initialized:
            logger.warning(f"策略组[{group_id}]尚未初始化，无法更新数据")
            return {}
            
        # 获取周期
        period = self.get_group_period(group_id)
        if not period:
            logger.warning(f"策略组[{group_id}]未设置周期，无法更新数据")
            return {}
        
        try:
            # 更新模式下只获取实时行情数据，不获取增量K线
            quotes_data = await self.realtime_service.get_realtime_quotes(
                symbols=symbols,
                save_to_db=False  # 不保存到数据库，提高性能
            )
            
            # 获取现有缓存
            cached_data = self._data_cache.get(group_id, {})
            
            # 使用实时行情更新缓存的K线数据
            for symbol in symbols:
                # 获取缓存的历史数据
                df = cached_data.get(symbol, pd.DataFrame())
                if df.empty:
                    continue
                
                # 获取实时行情并合并
                quote = quotes_data.get(symbol)
                if quote is not None and not quote.empty:
                        # 获取最新报价
                    latest_quote = quote.iloc[0]
                    latest_time = datetime.now().replace(second=0, microsecond=0)
                    
                    # 根据K线周期向下取整时间
                    period_minutes = int(period.replace('min', '')) if 'min' in period else 0
                    if period_minutes > 0:
                        latest_time = latest_time.replace(minute=latest_time.minute // period_minutes * period_minutes)
                    
                    # 检查是否需要更新最后一根K线或添加新K线
                    last_time = df['time'].max() if not df.empty else None
                    
                    if last_time and latest_time == last_time:
                        # 更新最后一根K线
                        last_index = df['time'].idxmax()
                        df.loc[last_index, 'close'] = latest_quote.get('latest_price', df.loc[last_index, 'close'])
                        df.loc[last_index, 'high'] = max(df.loc[last_index, 'high'], latest_quote.get('high', df.loc[last_index, 'high']))
                        df.loc[last_index, 'low'] = min(df.loc[last_index, 'low'], latest_quote.get('low', df.loc[last_index, 'low']))
                        df.loc[last_index, 'volume'] += latest_quote.get('volume', 0)
                    else:
                        # 添加新K线
                        new_row = pd.DataFrame([{
                            'time': latest_time,
                            'symbol': symbol,
                            'open': latest_quote.get('open', df['close'].iloc[-1] if not df.empty else 0),
                            'high': latest_quote.get('high', df['close'].iloc[-1] if not df.empty else 0),
                            'low': latest_quote.get('low', df['close'].iloc[-1] if not df.empty else 0),
                            'close': latest_quote.get('latest_price', df['close'].iloc[-1] if not df.empty else 0),
                            'volume': latest_quote.get('volume', 0),
                            'amount': latest_quote.get('amount', 0)
                        }])
                        
                        # 合并并去重
                        df = pd.concat([df, new_row]).drop_duplicates(subset=['time']).sort_values('time')
                
                # 更新缓存
                if not df.empty:
                    cached_data[symbol] = df
            
            # 更新完整缓存
            self._data_cache[group_id] = cached_data
            self._last_update_time[group_id] = datetime.now()
            
            logger.info(f"策略组[{group_id}]数据更新完成: {len(cached_data)}个标的")
            return cached_data
            
        except Exception as e:
            logger.error(f"更新策略组[{group_id}]数据失败: {str(e)}")
            return self._data_cache.get(group_id, {})
    
    def _get_latest_kline_time(self, group_id: str) -> Optional[datetime]:
        """获取策略组中最新K线的时间"""
        cached_data = self._data_cache.get(group_id, {})
        latest_time = None
        
        for symbol, df in cached_data.items():
            if df is not None and not df.empty:
                symbol_latest = df['time'].max()
                if latest_time is None or symbol_latest > latest_time:
                    latest_time = symbol_latest
        
        return latest_time
    
    def get_group_data(self, group_id: str) -> Dict[str, pd.DataFrame]:
        """获取策略组数据"""
        return self._data_cache.get(group_id, {})
    
    def get_group_period(self, group_id: str) -> Optional[str]:
        """获取策略组K线周期"""
        return self._period_cache.get(group_id)
    
    def is_group_initialized(self, group_id: str) -> bool:
        """检查策略组是否已初始化数据"""
        return group_id in self._initialized
    
    def get_last_update_time(self, group_id: str) -> Optional[datetime]:
        """获取策略组最后更新时间"""
        return self._last_update_time.get(group_id)
    
    def set_group_ttl(self, group_id: str, minutes: int = 5) -> None:
        """设置策略组数据缓存的生存时间"""
        self._ttl_settings[group_id] = timedelta(minutes=minutes)
        logger.info(f"设置策略组[{group_id}]缓存TTL: {minutes}分钟")
    
    async def clean_expired_data(self, max_age_hours: int = 24) -> None:
        """清理过期数据"""
        now = datetime.now()
        expired_groups = []
        
        for group_id, update_time in self._last_update_time.items():
            # 检查自定义TTL
            if group_id in self._ttl_settings:
                ttl = self._ttl_settings[group_id]
                if (now - update_time) > ttl:
                    expired_groups.append(group_id)
                    continue
            
            # 默认过期时间
            if (now - update_time).total_seconds() > max_age_hours * 3600:
                expired_groups.append(group_id)
        
        # 清理过期数据
        for group_id in expired_groups:
            self.release_group_data(group_id)
        
        logger.info(f"数据清理完成，清理了 {len(expired_groups)} 个策略组的过期数据")
    
    def release_group_data(self, group_id: str) -> bool:
        """
        释放策略组数据缓存
        
        Args:
            group_id: 策略组ID
            
        Returns:
            bool: 是否成功释放
        """
        if group_id not in self._data_cache:
            logger.warning(f"释放策略组[{group_id}]数据失败: 缓存不存在")
            return False
            
        # 清理缓存
        if group_id in self._data_cache:
            del self._data_cache[group_id]
        if group_id in self._period_cache:
            del self._period_cache[group_id]
        if group_id in self._initialized:
            self._initialized.remove(group_id)
        if group_id in self._last_update_time:
            del self._last_update_time[group_id]
        if group_id in self._ttl_settings:
            del self._ttl_settings[group_id]
            
        logger.info(f"已释放策略组[{group_id}]数据缓存")
        return True