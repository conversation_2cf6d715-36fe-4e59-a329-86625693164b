import React from 'react';
import { Card, Radio, Empty, Space, Tooltip } from 'antd';
import { DeleteOutlined } from '@ant-design/icons';
import { motion } from 'framer-motion';
import { StrategyCardTemplate, StrategyCardInstance } from '../../types/api';
import QuantumButton from '../common/QuantumButton';
import InlineParameterEditor from './InlineParameterEditor';
import { sanitizeHtml } from '../../utils/htmlUtils';
import './SimpleList.scss';
import classNames from 'classnames';

interface SimpleListProps {
  cards: StrategyCardTemplate[];
  selectedCards: StrategyCardInstance[];
  executionMode: 'sequential' | 'parallel';
  onCardsChange: (cards: StrategyCardInstance[]) => void;
  onExecutionModeChange: (mode: 'sequential' | 'parallel') => void;
  onParamUpdate: (cardId: string, params: Record<string, any>) => void;
  debuggingCardId: string | null;
  groupType?: 'filter' | 'timing';
}

const SimpleList: React.FC<SimpleListProps> = ({
  cards,
  selectedCards,
  executionMode,
  onCardsChange,
  onExecutionModeChange,
  onParamUpdate,
  debuggingCardId,
  groupType = 'filter',
}) => {
  // 移除策略卡片
  const handleRemoveCard = (index: number) => {
    const newCards = selectedCards.filter((_, i) => i !== index);
    onCardsChange(newCards);
  };

  // 处理参数更新
  const handleParamChange = (cardId: string, values: Record<string, any>) => {
    console.log('Parameter Update:', { cardId, values });
    onParamUpdate(cardId, values);
  };

  // 将API的StrategyParameter类型转换为InlineParameterEditor需要的类型
  const convertParameters = (parameters: Record<string, any> | any[]) => {
    // 如果参数是对象格式（新版本格式）
    if (typeof parameters === 'object' && !Array.isArray(parameters)) {
      const converted: Record<string, any> = {};
      Object.entries(parameters).forEach(([key, param]) => {
        converted[key] = {
          ...param,
          type: param.type === 'string' && param.options ? 'select' : param.type,
          options: param.options?.map((opt: any) => 
            typeof opt === 'object' ? opt : { label: String(opt), value: opt }
          ),
          step: param.validation?.step || 1,
          min: param.validation?.min,
          max: param.validation?.max
        };
      });
      return converted;
    }
    
    // 如果参数是数组格式（旧版本格式）
    if (Array.isArray(parameters)) {
      const converted: Record<string, any> = {};
      parameters.forEach(param => {
        if (param.name) {
          converted[param.name] = {
            type: param.type === 'string' && param.options ? 'select' : param.type,
            label: param.label || param.name,
            description: param.description,
            required: param.required,
            default: param.default,
            options: param.options?.map((opt: any) => 
              typeof opt === 'object' ? opt : { label: String(opt), value: opt }
            ),
            min: param.validation?.min,
            max: param.validation?.max,
            step: param.validation?.step || 1,
            unit: param.unit,
            group: param.group,
            visibleWhen: param.visibleWhen
          };
        }
      });
      return converted;
    }
    
    return {};
  };

  return (
    <div className="simple-list">
      <div className="list-header">
        <Radio.Group
          value={executionMode}
          onChange={(e) => onExecutionModeChange(e.target.value)}
          buttonStyle="solid"
          className="execution-mode-toggle"
        >
          <Radio.Button value="sequential">串行执行</Radio.Button>
          <Radio.Button value="parallel">并行执行</Radio.Button>
        </Radio.Group>
      </div>

      <div className="grid-view">
        {selectedCards.length === 0 ? (
          <Empty
            description="尚未添加策略卡片"
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          />
        ) : (
          selectedCards.map((card, index) => {
            // 使用卡片中保存的模板信息
            const template = card.template;
            if (!template) {
              console.warn('Template not found:', card.template?.id);
              return null;
            }

            // 准备参数
            const parameters = template.parameters;
            
            // 调试信息
            if (process.env.NODE_ENV === 'development') {
              console.group('Strategy Card Debug');
              console.log('Template:', {
                id: template.id,
                name: template.name,
                parameters: template.parameters,
                parameterGroups: template.parameterGroups,
                ui: template.ui
              });
              console.log('Converted Parameters:', parameters);
              
              // 检查参数组配置
              if (template.parameterGroups) {
                Object.entries(template.parameterGroups).forEach(([groupName, group]) => {
                  console.log(`Parameter Group [${groupName}]:`, {
                    parameters: group.parameters,
                    displayMode: group.displayMode,
                    prefix: group.prefix,
                    separator: group.separator,
                    layout: group.layout
                  });
                });
              }
              
              console.groupEnd();
            }

            // 确保参数组配置符合接口定义
            const parameterGroups = template.parameterGroups ? 
              Object.entries(template.parameterGroups).reduce((acc, [key, group]) => ({
                ...acc,
                [key]: {
                  parameters: group.parameters || [],
                  displayMode: group.displayMode || 'inline',
                  prefix: group.prefix,
                  separator: group.separator,
                  layout: group.layout || 'horizontal'
                }
              }), {}) : undefined;

            // 获取 UI 配置
            const uiConfig = template.ui?.form || {
              layout: 'horizontal',
              compact: true,
              showDescription: false
            };

            // 注入特殊参数值，用于visibleWhen条件判断
            const enhancedParameters = {
              ...card.parameters,
              '@strategy_mode': groupType,
            };

            return (
              <motion.div
                key={card.id}
                animate={{
                  scale: debuggingCardId === card.id ? [1, 1.02, 1] : 1,
                  boxShadow: debuggingCardId === card.id ? [
                    "0 0 0 0 rgba(99, 102, 241, 0)",
                    "0 0 0 4px rgba(99, 102, 241, 0.4)",
                    "0 0 0 0 rgba(99, 102, 241, 0)"
                  ] : "none"
                }}
                transition={{
                  duration: 1,
                  repeat: debuggingCardId === card.id ? Infinity : 0,
                  ease: "easeInOut"
                }}
              >
                <Card
                  className={classNames("strategy-card", {
                    "debugging": debuggingCardId === card.id
                  })}
                  title={
                    <Space>
                      {executionMode === 'sequential' && (
                        <span className="card-order">#{index + 1}</span>
                      )}
                      <span>{card.name}</span>
                    </Space>
                  }
                  extra={
                    <Space>
                      <Tooltip title="移除">
                        <QuantumButton
                          variant="outline"
                          buttonSize="small"
                          icon={<DeleteOutlined />}
                          onClick={() => handleRemoveCard(index)}
                        />
                      </Tooltip>
                    </Space>
                  }
                >
                  <div className="card-content">
                    <p dangerouslySetInnerHTML={{ __html: sanitizeHtml(card.description) }} />
                    <div className="param-editor">
                      <InlineParameterEditor
                        parameters={parameters}
                        parameterGroups={parameterGroups}
                        values={enhancedParameters}
                        onChange={(values) => handleParamChange(card.id, values)}
                        layout={uiConfig.layout || 'horizontal'}
                        compact={uiConfig.compact ?? true}
                        showDescription={uiConfig.showDescription ?? false}
                      />
                    </div>
                  </div>
                </Card>
              </motion.div>
            );
          })
        )}
      </div>
    </div>
  );
};

export default SimpleList; 