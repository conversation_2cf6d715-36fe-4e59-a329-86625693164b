# QuantCard 平台变更日志

## [未发布]

### 添加
- 增加了择时策略支持，将 TRADE 模式重命名为 TIMING 模式
- 创建了QuestDB数据服务，用于高频数据的获取
- 添加了均线交叉择时策略模板，作为择时策略的第一个可用策略
- 添加了用于执行择时策略的API端点 `/api/v1/strategies/timing`
- 更新了现有的策略执行API端点，支持择时模式
- 创建了QuestDB初始化脚本
- 创建了移除模式映射的脚本 `remove_mode_mapping.py`
- 创建了更新配置文件的脚本 `update_config_files.py`

### 变更
- 升级了策略执行请求模型，支持实时数据和数据回溯天数参数
- 修改了所有策略模板的模式映射，将'trade'改为'timing'
- 更新了策略执行器以支持择时模式
- 性能优化：移除了策略文件中的模式映射逻辑，直接使用执行模式作为数据源配置
- 性能优化：简化了择时策略执行逻辑，移除不必要的行情数据获取步骤
- 架构优化：删除了过于复杂的 ANALYSIS 模式，简化了系统结构

### 修复
- 修复了策略执行器在处理多种模式时的潜在问题
- 修复了缓存键计算方式，使用执行模式而非映射后的值

## 开发指南

### 择时策略开发
1. 择时策略应继承 `UnifiedStrategyCard` 基类
2. 应使用 QuestDB 数据服务获取高频数据
3. 策略文件组织应遵循与现有过滤策略相同的模式
4. 执行模式应设置为 `StrategyMode.TIMING`
5. 策略config.yaml中的数据源配置节点名称应与执行模式名称一致

### QuestDB 数据服务使用
1. 使用 `QuestDBService` 类获取高频数据
2. 支持查询K线数据和实时行情数据
3. 可以设置数据回溯天数和其他参数

### 执行模式与数据源配置
1. 执行模式(`filter`, `timing`, `backtest`)直接对应数据源配置
2. 在策略的config.yaml文件中确保有相应名称的数据源配置节点
3. 不再需要使用模式映射，简化了代码结构 