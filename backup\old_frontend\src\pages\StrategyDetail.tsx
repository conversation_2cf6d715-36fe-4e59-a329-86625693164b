import React, { useState, useEffect } from 'react';
import { Card, Descriptions, Button, Space, Tag, Tabs, message, Spin } from 'antd';
import { useParams, useNavigate } from 'react-router-dom';
import PageTitle from '../components/common/PageTitle';
import { strategyGroupApi } from '../services/strategyService';
import { StrategyGroup, StrategyCardTemplate, StrategyCardInstance } from '../types/api';

const { TabPane } = Tabs;

// 响应数据类型
interface StrategyDetailResponse {
  group: StrategyGroup;
  templates: StrategyCardTemplate[];
}

const StrategyDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [strategy, setStrategy] = useState<StrategyGroup | null>(null);
  const [templates, setTemplates] = useState<StrategyCardTemplate[]>([]);

  // 构建完整的策略卡实例数据（包含模板信息）
  const buildCompleteCards = (group: StrategyGroup, templates: StrategyCardTemplate[]): StrategyCardInstance[] => {
    if (!group.cards) return [];
    
    return group.cards.map(card => {
      // 查找对应的模板
      const templateId = card.template_id || (card.template && card.template.id);
      const template = templates.find(t => t.id === templateId);
      
      // 如果找到模板信息，构建完整的卡片实例
      if (template) {
        return {
          ...card,
          template: template
        } as StrategyCardInstance;
      }
      
      // 如果已经有模板信息，直接返回
      if (card.template) {
        return card as StrategyCardInstance;
      }
      
      // 没有模板信息，构建一个部分实例
      return {
        ...card,
        template: {
          id: templateId || '',
          name: card.template_name || card.name || '未知模板',
          description: card.description || '',
          parameters: {},
          tags: [],
          version: '',
          author: '',
          stars: 0,
          created_at: '',
          updated_at: ''
        }
      } as StrategyCardInstance;
    });
  };

  useEffect(() => {
    const fetchStrategy = async () => {
      if (!id) return;
      try {
        setLoading(true);
        const data = await strategyGroupApi.getGroupById(id);
        setStrategy(data);
      } catch (error) {
        console.error('获取策略详情失败:', error);
        message.error('获取策略详情失败');
      } finally {
        setLoading(false);
      }
    };

    fetchStrategy();
  }, [id]);

  if (loading) {
    return <Spin size="large" />;
  }

  if (!strategy) {
    return <div>策略不存在</div>;
  }

  const handleStartStop = async () => {
    try {
      if (strategy.status === 'inactive') {
        await strategyGroupApi.startGroup(strategy.id);
        message.success('策略已启动');
      } else if (strategy.status === 'active') {
        await strategyGroupApi.stopGroup(strategy.id);
        message.success('策略已停止');
      }
      // 重新加载策略详情
      const data = await strategyGroupApi.getGroupById(strategy.id);
      setStrategy(data);
    } catch (error) {
      message.error(strategy.status === 'inactive' ? '启动策略失败' : '停止策略失败');
    }
  };

  return (
    <div>
      <PageTitle
        title={strategy.name}
        subtitle="查看策略详情和性能表现"
        breadcrumbs={[
          { label: '策略管理', path: '/strategies' },
          { label: strategy.name },
        ]}
        extra={[
          <Button
            key="backtest"
            type="primary"
            onClick={() => navigate(`/backtest?strategyId=${id}`)}
          >
            运行回测
          </Button>,
          <Button
            key="startstop"
            type={strategy.status === 'active' ? 'default' : 'primary'}
            onClick={handleStartStop}
          >
            {strategy.status === 'active' ? '停止' : '启动'}
          </Button>,
          <Button
            key="edit"
            onClick={() => navigate(`/strategies/${id}/edit`)}
          >
            编辑策略
          </Button>,
        ]}
      />

      <Tabs defaultActiveKey="overview">
        <TabPane tab="概览" key="overview">
          <Space direction="vertical" style={{ width: '100%' }} size={16}>
            <Card title="基本信息">
              <Descriptions column={{ xs: 1, sm: 2, md: 3 }}>
                <Descriptions.Item label="策略ID">{strategy.id}</Descriptions.Item>
                <Descriptions.Item label="创建时间">
                  {new Date(strategy.created_at).toLocaleString()}
                </Descriptions.Item>
                <Descriptions.Item label="更新时间">
                  {new Date(strategy.updated_at).toLocaleString()}
                </Descriptions.Item>
                <Descriptions.Item label="状态">
                  <Tag color={
                    strategy.status === 'active' ? 'success' :
                    strategy.status === 'error' ? 'error' : 'default'
                  }>
                    {
                      strategy.status === 'active' ? '运行中' :
                      strategy.status === 'error' ? '错误' : '已停止'
                    }
                  </Tag>
                </Descriptions.Item>
                <Descriptions.Item label="策略卡片数量">
                  {strategy.cards.length}
                </Descriptions.Item>
                {strategy.schedule && (
                  <Descriptions.Item label="执行计划">
                    {strategy.schedule.type === 'cron' ? 'Cron表达式' : '固定间隔'}: {strategy.schedule.value}
                  </Descriptions.Item>
                )}
              </Descriptions>
            </Card>

            <Card title="性能指标">
              <Descriptions column={{ xs: 1, sm: 2, md: 4 }}>
                <Descriptions.Item label="总收益率">
                  <span style={{ color: strategy.performance_metrics.total_returns >= 0 ? '#3f8600' : '#cf1322' }}>
                    {strategy.performance_metrics.total_returns?.toFixed(2)}%
                  </span>
                </Descriptions.Item>
                <Descriptions.Item label="夏普比率">
                  {strategy.performance_metrics.sharpe_ratio?.toFixed(2) || '-'}
                </Descriptions.Item>
                <Descriptions.Item label="最大回撤">
                  <span style={{ color: '#cf1322' }}>
                    {strategy.performance_metrics.max_drawdown?.toFixed(2)}%
                  </span>
                </Descriptions.Item>
                <Descriptions.Item label="胜率">
                  {strategy.performance_metrics.win_rate?.toFixed(2)}%
                </Descriptions.Item>
              </Descriptions>
            </Card>

            <Card title="风险控制">
              <Descriptions column={{ xs: 1, sm: 2, md: 4 }}>
                <Descriptions.Item label="最大回撤限制">
                  {strategy.risk_settings.max_drawdown}%
                </Descriptions.Item>
                <Descriptions.Item label="止损比例">
                  {strategy.risk_settings.stop_loss}%
                </Descriptions.Item>
                <Descriptions.Item label="止盈比例">
                  {strategy.risk_settings.take_profit}%
                </Descriptions.Item>
              </Descriptions>
            </Card>

            <Card title="执行日志">
              <div style={{ 
                maxHeight: '300px', 
                overflowY: 'auto',
                backgroundColor: '#f5f5f5',
                padding: '16px',
                borderRadius: '4px',
                fontFamily: '"JetBrains Mono", monospace',
                fontSize: '14px',
              }}>
                {strategy.execution_logs.map((log, index) => (
                  <div key={index} style={{ 
                    marginBottom: '8px',
                    color: log.level === 'error' ? '#cf1322' : 
                           log.level === 'warning' ? '#faad14' : 
                           log.level === 'success' ? '#52c41a' : '#000000'
                  }}>
                    [{new Date(log.timestamp).toLocaleString()}] [{log.level.toUpperCase()}] {log.message}
                    {log.details && (
                      <pre style={{ marginTop: '4px', marginBottom: '4px' }}>
                        {JSON.stringify(log.details, null, 2)}
                      </pre>
                    )}
                  </div>
                ))}
              </div>
            </Card>
          </Space>
        </TabPane>

        <TabPane tab="回测历史" key="backtest">
          {/* 这里后续添加回测历史列表 */}
          <div style={{ padding: '32px', textAlign: 'center' }}>
            回测历史列表
          </div>
        </TabPane>

        <TabPane tab="实盘记录" key="live">
          {/* 这里后续添加实盘交易记录 */}
          <div style={{ padding: '32px', textAlign: 'center' }}>
            实盘交易记录
          </div>
        </TabPane>
      </Tabs>
    </div>
  );
};

export default StrategyDetail; 