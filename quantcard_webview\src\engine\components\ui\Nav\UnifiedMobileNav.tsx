/**
 * 🧭 统一移动端导航组�?
 * 集成顶部导航条、返回按钮、底部导航的一体化解决方案
 */

import React from 'react'
import styled from 'styled-components'
import { motion } from 'framer-motion'
import { useUIState, useGameState } from '../../../../store/hooks'

export type BottomNavKey = 'Home' | 'Codex' | 'Shop' | 'Profile'

interface UnifiedMobileNavProps {
  title: string
  showBackButton?: boolean
  backAction?: () => void
  bottomNavActive?: BottomNavKey
  showBottomNav?: boolean
  showUserInfo?: boolean
  children?: React.ReactNode
}

// 🎨 主容�?
const Container = styled.div<{ theme: any }>`
  min-height: 100vh;
  background: ${p => p.theme.colors.background.void};
  color: ${p => p.theme.colors.text.primary};
  display: flex;
  flex-direction: column;
  position: relative;
`

// 📱 顶部导航�?
const TopNavBar = styled.div<{ theme: any }>`
  background: ${p => p.theme.colors.background.card};
  backdrop-filter: blur(15px);
  border-bottom: 1px solid ${p => p.theme.colors.border.primary};
  padding: 1rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: sticky;
  top: 0;
  z-index: 100;
  flex-shrink: 0;
  box-shadow: ${p => p.theme.shadows.medium};
  
  /* 状态栏适配 */
  padding-top: calc(1rem + env(safe-area-inset-top));
`

// ⬅️ 返回按钮
const BackButton = styled(motion.button)<{ theme: any }>`
  background: ${p => p.theme.colors.background.surface};
  border: 1px solid ${p => p.theme.colors.border.secondary};
  color: ${p => p.theme.colors.text.primary};
  border-radius: 12px;
  padding: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 600;
  font-family: ${p => p.theme.typography.fonts.primary};
  transition: all 0.2s ease;
  min-width: 80px;
  
  &:hover {
    background: ${p => p.theme.colors.primaryColors.cyan}20;
    border-color: ${p => p.theme.colors.primaryColors.cyan};
    transform: translateX(-2px);
  }
  
  .icon {
    font-size: 1.1rem;
  }
`

// 🏷�?页面标题
const PageTitle = styled.h1<{ theme: any }>`
  font-family: ${p => p.theme.typography.fonts.primary};
  font-size: 1.25rem;
  font-weight: 700;
  color: ${p => p.theme.colors.text.primary};
  margin: 0;
  text-align: center;
  flex: 1;
  
  /* 深色主题特殊效果 */
  ${p => p.theme.mode === 'dark' && `
    background: linear-gradient(135deg, ${p.theme.colors.primaryColors.cyan}, ${p.theme.colors.primaryColors.magenta});
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 0 20px ${p.theme.colors.primaryColors.cyan}40;
  `}
`

// 🔘 占位空间（保持布局平衡�?
const Spacer = styled.div`
  min-width: 80px;
`

// 💰 用户信息区域
const UserInfoArea = styled.div<{ theme: any }>`
  display: flex;
  align-items: center;
  gap: 0.75rem;
  min-width: 80px;
  justify-content: flex-end;
`

const CurrencyDisplay = styled.div<{ theme: any }>`
  display: flex;
  align-items: center;
  gap: 0.25rem;
  background: ${p => p.theme.colors.background.surface};
  border: 1px solid ${p => p.theme.colors.border.secondary};
  border-radius: 16px;
  padding: 0.25rem 0.75rem;
  font-size: 0.8rem;
  font-weight: 600;
  color: ${p => p.theme.colors.text.primary};
  font-family: ${p => p.theme.typography.fonts.primary};
  
  .icon {
    font-size: 0.9rem;
  }
  
  .value {
    font-family: 'monospace';
  }
`

const UserAvatar = styled.div<{ theme: any }>`
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: ${p => p.theme.colors.primaryColors.cyan}20;
  border: 2px solid ${p => p.theme.colors.primaryColors.cyan};
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background: ${p => p.theme.colors.primaryColors.cyan}30;
    transform: scale(1.05);
  }
`

// 📋 内容区域
const ContentArea = styled.div<{ theme: any }>`
  flex: 1;
  overflow-y: auto;
  position: relative;
  
  /* 自定义滚动条 */
  &::-webkit-scrollbar {
    width: 4px;
  }
  
  &::-webkit-scrollbar-track {
    background: ${p => p.theme.colors.background.secondary};
  }
  
  &::-webkit-scrollbar-thumb {
    background: ${p => p.theme.colors.primaryColors.cyan};
    border-radius: 2px;
  }
`

// 📱 底部导航
const BottomNavBar = styled.div<{ theme: any }>`
  background: ${p => p.theme.colors.background.card};
  backdrop-filter: blur(15px);
  border-top: 1px solid ${p => p.theme.colors.border.primary};
  padding: 0.75rem 1rem;
  padding-bottom: calc(0.75rem + env(safe-area-inset-bottom));
  display: flex;
  justify-content: space-around;
  align-items: center;
  position: sticky;
  bottom: 0;
  z-index: 100;
  flex-shrink: 0;
  box-shadow: ${p => p.theme.shadows.medium};
`

const NavItem = styled(motion.button)<{ $active?: boolean, theme: any }>`
  background: ${p => p.$active ? `${p.theme.colors.primaryColors.cyan}20` : 'transparent'};
  border: ${p => p.$active ? `1px solid ${p.theme.colors.primaryColors.cyan}` : '1px solid transparent'};
  border-radius: 12px;
  padding: 0.5rem 1rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 60px;
  
  .icon {
    font-size: 1.2rem;
  }
  
  .label {
    font-size: 0.7rem;
    font-weight: 600;
    color: ${p => p.$active ? p.theme.colors.primaryColors.cyan : p.theme.colors.text.secondary};
    font-family: ${p => p.theme.typography.fonts.primary};
  }
  
  &:hover {
    background: ${p => p.theme.colors.primaryColors.cyan}15;
    transform: translateY(-2px);
  }
`

// 🧭 统一导航组件
export default function UnifiedMobileNav({
  title,
  showBackButton = true,
  backAction,
  bottomNavActive = 'Home',
  showBottomNav = true,
  showUserInfo = true,
  children
}: UnifiedMobileNavProps) {
  const { switchScene } = useUIState()
  const { currency, playerLevel } = useGameState()

  const handleBack = () => {
    if (backAction) {
      backAction()
    } else {
      switchScene('WorldMap')
    }
  }

  const handleAvatarClick = () => {
    switchScene('Profile')
  }

  const navItems = [
    { key: 'Home' as BottomNavKey, icon: '🌍', label: '世界', scene: 'WorldMap' },
    { key: 'Codex' as BottomNavKey, icon: '📚', label: '图鉴', scene: 'Codex' },
    { key: 'Shop' as BottomNavKey, icon: '🛒', label: '商店', scene: 'Shop' },
    { key: 'Profile' as BottomNavKey, icon: '👤', label: '个人', scene: 'Profile' }
  ]

  const handleNavClick = (scene: string) => {
    switchScene(scene as any)
  }

  return (
    <Container>
      {/* 顶部导航�?*/}
      <TopNavBar>
        {showBackButton ? (
          <BackButton
            onClick={handleBack}
            whileTap={{ scale: 0.95 }}
            whileHover={{ scale: 1.02 }}
          >
            <span className="icon">←</span>
            <span>返回</span>
          </BackButton>
        ) : (
          <Spacer />
        )}
        
        <PageTitle>{title}</PageTitle>
        
        {showUserInfo ? (
          <UserInfoArea>
            <CurrencyDisplay>
              <span className="icon">🪙</span>
              <span className="value">{currency?.coins || 0}</span>
            </CurrencyDisplay>
            <UserAvatar onClick={handleAvatarClick}>
              👤
            </UserAvatar>
          </UserInfoArea>
        ) : (
          <Spacer />
        )}
      </TopNavBar>

      {/* 内容区域 */}
      <ContentArea>
        {children}
      </ContentArea>

      {/* 底部导航 */}
      {showBottomNav && (
        <BottomNavBar>
          {navItems.map((item) => (
            <NavItem
              key={item.key}
              $active={bottomNavActive === item.key}
              onClick={() => handleNavClick(item.scene)}
              whileTap={{ scale: 0.95 }}
              whileHover={{ scale: 1.05 }}
            >
              <span className="icon">{item.icon}</span>
              <span className="label">{item.label}</span>
            </NavItem>
          ))}
        </BottomNavBar>
      )}
    </Container>
  )
} 
