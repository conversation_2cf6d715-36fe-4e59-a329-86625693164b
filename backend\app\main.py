"""
FastAPI 应用程序入口
"""
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import logging
import asyncio

# 导入统一API路由
from .api import api_router
# 移除旧的WebSocket路由，只使用统一WebSocket
# from .api.websocket import ws_router  # 旧的WebSocket路由 - 已弃用
from .api.websocket_endpoint import ws_router as unified_ws_router  # 统一WebSocket路由
from .core.config import settings
from .core.deps import get_strategy_runtime
from .core.init_db import init_db, init_database
from .core.init_data import init_strategy_templates, init_data
from .core.data.db.base import db_manager
from .services.market_data_service import market_data_service
from .services.stock_info_service import stock_info_service
# 移除旧的websocket_service导入，使用unified_websocket中的连接管理
# from .services.websocket_service import websocket_manager  # 旧版本 - 已弃用
from .services.in_memory_queue import in_memory_queue

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(
    title=settings.PROJECT_NAME,
    description=settings.DESCRIPTION,
    version=settings.VERSION,
    openapi_url=f"{settings.API_V1_STR}/openapi.json"
)

# 配置 CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,  # 允许携带认证信息
    allow_methods=["*"],  # 允许所有方法
    allow_headers=["*"],  # 允许所有头部
    expose_headers=["*"]  # 暴露所有头部
)

# 注册API路由，增加前缀
app.include_router(api_router, prefix="/api")

# 注册统一WebSocket路由
app.include_router(unified_ws_router)

# 定义数据库连接清理任务
async def cleanup_connections_task():
    """定期清理数据库连接"""
    try:
        while True:
            await asyncio.sleep(300)  # 每5分钟清理一次
            logger.info("执行数据库连接清理")
            await db_manager.cleanup_idle_connections()
            
            # 强制执行垃圾回收
            import gc
            gc.collect()
    except asyncio.CancelledError:
        logger.info("数据库连接清理任务已取消")
    except Exception as e:
        logger.error(f"数据库连接清理任务出错: {str(e)}", exc_info=True)

# 定义清理任务变量
cleanup_task = None

@app.on_event("startup")
async def startup_event():
    """应用启动时的初始化操作"""
    global cleanup_task
    
    try:
        logger.info("正在启动应用...")
        
        # 初始化数据库结构
        await init_database()
        
        # 初始化基础数据
        await init_db()
        
        # 初始化其他数据
        await init_data()
        
        # 初始化交易日历
        from .services.trading_calendar import trading_calendar
        await trading_calendar.load_calendar()
        logger.info("交易日历初始化完成")
        
        # 初始化策略运行时
        strategy_runtime = await get_strategy_runtime()
        
        # 初始化WebSocket管理器
        # 移除旧的websocket_service导入，使用unified_websocket中的连接管理
        # await websocket_manager.initialize() # 旧版本 - 已弃用
        logger.info("WebSocket管理器初始化完成")
        
        # 连接内存队列和WebSocket管理器 - 使用统一的消息处理方法
        from .api.unified_websocket import websocket_manager as unified_websocket_manager
        in_memory_queue.subscribe("strategy_updates", unified_websocket_manager.handle_message)
        in_memory_queue.subscribe("trade_signals", unified_websocket_manager.handle_message)
        in_memory_queue.subscribe("market_updates", unified_websocket_manager.handle_message)
        in_memory_queue.subscribe("quote_updates", unified_websocket_manager.handle_message)
        logger.info("内存队列与WebSocket管理器连接完成")
        
        # 启动数据库连接清理任务
        cleanup_task = asyncio.create_task(cleanup_connections_task())
        logger.info("已启动数据库连接清理任务")
        
        logger.info("应用启动完成")
        
    except Exception as e:
        logger.error(f"应用启动失败: {str(e)}", exc_info=True)
        raise

@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭时的清理操作"""
    global cleanup_task
    
    try:
        logger.info("正在关闭应用...")
        
        # 取消清理任务
        if cleanup_task:
            cleanup_task.cancel()
            try:
                await cleanup_task
            except asyncio.CancelledError:
                pass
        
        # 停止内存队列
        await in_memory_queue.stop()
        logger.info("内存队列已停止")
            
        # 关闭策略运行时
        strategy_runtime = await get_strategy_runtime()
        await strategy_runtime.stop()
        
        # 关闭各服务
        await market_data_service.close()
        await stock_info_service.close()
        
        # 关闭数据库连接
        await db_manager.close()
        
        # 强制执行垃圾回收
        import gc
        gc.collect()
        
        logger.info("应用关闭完成")
    except Exception as e:
        logger.error(f"应用关闭失败: {str(e)}", exc_info=True)

# 添加测试路由
@app.get("/")
async def root():
    """测试路由"""
    return {"message": "QuantCard API 服务正在运行"}