"""
运行时类型定义
"""
from datetime import datetime
from enum import Enum
from typing import Dict, List, Any, Optional, Union
from pydantic import BaseModel, Field

class SignalType(str, Enum):
    """信号类型"""
    PRICE = "price"  # 价格信号
    VOLUME = "volume"  # 成交量信号
    TECHNICAL = "technical"  # 技术指标信号
    FUNDAMENTAL = "fundamental"  # 基本面信号
    SENTIMENT = "sentiment"  # 情绪信号
    CUSTOM = "custom"  # 自定义信号

class StrategyMode(str, Enum):
    """策略模式"""
    FILTER = "filter"      # 过滤模式
    BACKTEST = "backtest"  # 回测模式
    TIMING = "timing"      # 择时模式

class ExecutionMode(str, Enum):
    """执行模式"""
    SEQUENTIAL = "sequential"  # 串行执行
    PARALLEL = "parallel"      # 并行执行

class Signal(BaseModel):
    """统一信号模型"""
    # 基础信号字段
    symbol: str
    name: str = ""
    direction: str = "BUY"
    confidence: float = 0.8
    type: str = "fundamental"
    trigger_condition: str = ""

    # 市场数据字段 - 直接作为顶层字段，避免嵌套
    price: float = 0.0
    change_pct: float = 0.0
    volume: int = 0
    market_cap: Optional[float] = None

    # 择时信号专用字段
    signal_strength: Optional[float] = None

    # 内部使用字段
    id: str = ""
    strategy_id: str = ""
    group_id: str = ""
    timestamp: datetime = Field(default_factory=datetime.utcnow)

    # 元数据字段，用于存储额外信息
    metadata: Dict[str, Any] = Field(default_factory=dict, description="信号元数据")

    class Config:
        arbitrary_types_allowed = True
        json_encoders = {
            datetime: lambda dt: dt.isoformat() if dt else None
        }

    @property
    def reason(self) -> str:
        """获取信号原因"""
        return self.metadata.get("reason", self.trigger_condition or "无")

    @staticmethod
    def from_strategy_signal(symbol: str, strategy_id: str, **kwargs) -> "Signal":
        """从策略创建信号"""
        now = datetime.utcnow()
        return Signal(
            symbol=symbol,
            strategy_id=strategy_id,
            id=f"{strategy_id}_{symbol}_{now.timestamp()}",
            **kwargs
        )

class RuntimeContext(BaseModel):
    """运行时上下文"""
    
    # 基本信息
    strategy_id: str = Field(..., description="策略ID")
    user_id: str = Field(..., description="用户ID")
    name: str = Field(..., description="策略名称")
    type: str = Field(..., description="策略类型")
    description: Optional[str] = Field(None, description="策略描述")
    
    # 执行配置
    parameters: Dict[str, Any] = Field(default_factory=dict, description="策略参数")
    mode: StrategyMode = Field(default=StrategyMode.FILTER, description="执行模式")
    debug: bool = Field(default=False, description="是否是调试模式")
    
    # 元数据和状态管理
    request_context: Dict[str, Any] = Field(default_factory=dict, description="请求上下文")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="元数据")
    data_cache: Dict[str, Any] = Field(default_factory=dict, description="数据缓存")
    logs: List[str] = Field(default_factory=list, description="执行日志")
    signals: List[Signal] = Field(default_factory=list, description="策略产生的信号")
    
    # 运行时核心引用
    runtime_core: Optional[Any] = Field(default=None, description="运行时核心引用")
    
    class Config:
        arbitrary_types_allowed = True
        
    def to_db_model(self) -> Dict[str, Any]:
        """转换为数据库存储格式"""
        return {
            "user_id": self.user_id,
            "name": self.name,
            "type": self.type,
            "description": self.description,
            "parameters": self.parameters,
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow()
        }
        
    @classmethod
    def from_db_model(cls, strategy_data: Dict[str, Any], mode: StrategyMode = StrategyMode.FILTER) -> "RuntimeContext":
        """从数据库模型创建运行时上下文"""
        return cls(
            strategy_id=str(strategy_data.get("_id")),
            user_id=strategy_data["user_id"],
            name=strategy_data["name"],
            type=strategy_data["type"],
            description=strategy_data.get("description", ""),
            mode=mode,
            parameters=strategy_data.get("parameters", {}),
            metadata={
                "created_at": strategy_data.get("created_at"),
                "updated_at": strategy_data.get("updated_at")
            }
        )
        
    def get_previous_signals(self) -> List[Signal]:
        """获取前一个策略的信号
        
        Returns:
            List[Signal]: 前一个策略的信号列表
        """
        previous_signals = self.metadata.get("previous_signals", [])
        return previous_signals

class ExecutionResult(BaseModel):
    """执行结果"""
    strategy_id: str
    signals: Union[List[Signal], Dict[str, Any]] = Field(default_factory=list)
    success: bool = True
    error: Optional[str] = None
    metadata: Dict[str, Any] = Field(default_factory=dict)
    execution_time: float = Field(..., description="执行时间(ms)")
    logs: List[str] = Field(default_factory=list, description="策略执行过程中的日志信息")
