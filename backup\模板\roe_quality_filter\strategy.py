"""
ROE质量筛选策略
基于净资产收益率(ROE)进行股票质量筛选，支持多维度ROE分析
"""
import logging
import pandas as pd
import numpy as np
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import yaml
import json
from pathlib import Path

from app.core.strategy.base import UnifiedStrategyCard
from app.core.runtime.types import RuntimeContext, Signal, SignalType, StrategyMode
from app.core.data.db.base import db_manager
from app.core.runtime.cache import cache_manager
from sqlalchemy import text

logger = logging.getLogger(__name__)

class ROEQualityFilterStrategy(UnifiedStrategyCard):
    """ROE质量筛选策略
    
    该策略从多个维度分析ROE质量：
    1. 绝对ROE水平筛选
    2. ROE稳定性分析（过去N年ROE标准差）
    3. ROE趋势分析（ROE是否持续改善）
    4. 行业相对ROE排名
    5. ROE构成分析（净利润率 * 资产周转率 * 权益乘数）
    """
    
    def __init__(self, context: Optional[RuntimeContext] = None):
        """初始化策略"""
        super().__init__(context)
        
        # 加载配置文件
        config_path = Path(__file__).parent / "config.yaml"
        with open(config_path, "r", encoding="utf-8") as f:
            self.config = yaml.safe_load(f)

    async def prepare_data(self, context: RuntimeContext) -> pd.DataFrame:
        """准备数据
        
        Args:
            context: 运行时上下文
            
        Returns:
            pd.DataFrame: 包含ROE相关财务数据的DataFrame
        """
        try:
            # 检查串行执行模式
            if not self._check_sequential_mode(context):
                return pd.DataFrame()
            
            # 获取模式配置
            mode = context.mode.value if context.mode else "filter"
            self._log(f"执行模式: {mode}")
            
            # 获取股票代码列表
            symbols = self._get_stock_symbols(context)
            
            # 检查缓存
            cache_key = f"roe_quality_{mode}_{','.join(sorted(symbols)) if symbols else 'all'}"
            cache = cache_manager["market_data"]
            cached_data = cache.get(cache_key)
            if cached_data is not None:
                self._log(f"从缓存获取ROE数据: {len(cached_data)}条记录")
                return cached_data
            
            # 获取数据
            mode_config = self.config["data_sources"].get(mode, self.config["data_sources"]["filter"])
            data = await self._get_financial_data(mode_config, symbols)
            
            if data.empty:
                self._log("未获取到财务数据", "warning")
                return pd.DataFrame()
            
            # 计算ROE质量指标
            data = await self._calculate_roe_metrics(data)
            
            # 缓存数据
            cache.set(cache_key, data, ttl=3600)  # 缓存1小时
            self._log(f"成功准备ROE质量数据: {len(data)}条记录")
            return data
            
        except Exception as e:
            self._log(f"准备数据失败: {str(e)}", "error")
            return pd.DataFrame()
    
    async def _get_financial_data(self, mode_config: Dict[str, Any], symbols: List[str]) -> pd.DataFrame:
        """获取财务数据"""
        try:
            table = mode_config["database"]["table"]
            fields = mode_config.get("fields", [])
            
            # 构建SQL查询
            fields_str = ", ".join(fields)
            
            if symbols:
                async with db_manager.get_session() as session:
                    # 获取最近3年的财务数据用于趋势和稳定性分析
                    sql_query = f"""
                    SELECT {fields_str}
                    FROM {table} 
                    WHERE 股票代码 = ANY(:symbols) 
                    AND 报告期 >= :start_date
                    ORDER BY 股票代码, 报告期 DESC
                    """
                    start_date = (datetime.now() - timedelta(days=1095)).strftime('%Y-%m-%d')  # 3年前
                    result = await session.execute(text(sql_query), {
                        'symbols': symbols,
                        'start_date': start_date
                    })
                    rows = result.fetchall()
            else:
                async with db_manager.get_session() as session:
                    sql_query = f"""
                    SELECT {fields_str}
                    FROM {table} 
                    WHERE 报告期 >= :start_date
                    ORDER BY 股票代码, 报告期 DESC
                    """
                    start_date = (datetime.now() - timedelta(days=1095)).strftime('%Y-%m-%d')
                    result = await session.execute(text(sql_query), {'start_date': start_date})
                    rows = result.fetchall()
            
            df = pd.DataFrame(rows, columns=fields)
            
            # 数据类型转换
            numeric_columns = ['ROE', '净利润率', '资产周转率', '权益乘数', '总资产', '净资产', '净利润']
            for col in numeric_columns:
                if col in df.columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce')
            
            return df
            
        except Exception as e:
            self._log(f"获取财务数据失败: {str(e)}", "error")
            return pd.DataFrame()
    
    async def _calculate_roe_metrics(self, data: pd.DataFrame) -> pd.DataFrame:
        """计算ROE质量指标"""
        try:
            if data.empty:
                return data
            
            # 按股票代码分组计算指标
            result_rows = []
            
            for stock_code in data['股票代码'].unique():
                stock_data = data[data['股票代码'] == stock_code].copy()
                stock_data = stock_data.sort_values('报告期')
                
                if len(stock_data) == 0:
                    continue
                
                # 最新数据
                latest = stock_data.iloc[-1]
                
                # 1. 当前ROE
                current_roe = latest.get('ROE', 0)
                
                # 2. ROE稳定性（标准差，越小越稳定）
                roe_std = stock_data['ROE'].std() if len(stock_data) > 1 else 0
                
                # 3. ROE趋势（线性回归斜率）
                if len(stock_data) >= 3:
                    x = np.arange(len(stock_data))
                    y = stock_data['ROE'].values
                    # 简单线性回归计算斜率
                    roe_trend = np.polyfit(x, y, 1)[0] if not np.isnan(y).all() else 0
                else:
                    roe_trend = 0
                
                # 4. ROE持续性（连续正ROE的季度数）
                positive_roe_streak = 0
                for roe in reversed(stock_data['ROE'].tolist()):
                    if roe > 0:
                        positive_roe_streak += 1
                    else:
                        break
                
                # 5. 杜邦分析 - ROE质量评估
                net_margin = latest.get('净利润率', 0)  # 净利润率
                asset_turnover = latest.get('资产周转率', 0)  # 资产周转率
                equity_multiplier = latest.get('权益乘数', 1)  # 权益乘数
                
                # ROE质量评分（综合评分）
                quality_score = self._calculate_quality_score(
                    current_roe, roe_std, roe_trend, positive_roe_streak,
                    net_margin, asset_turnover, equity_multiplier
                )
                
                result_rows.append({
                    '股票代码': stock_code,
                    '股票名称': latest.get('股票名称', stock_code),
                    '当前ROE': current_roe,
                    'ROE稳定性': roe_std,
                    'ROE趋势': roe_trend,
                    '连续正ROE季度': positive_roe_streak,
                    '净利润率': net_margin,
                    '资产周转率': asset_turnover,
                    '权益乘数': equity_multiplier,
                    'ROE质量评分': quality_score,
                    '数据更新日期': latest.get('报告期', '')
                })
            
            result_df = pd.DataFrame(result_rows)
            
            # 计算行业相对排名（这里简化处理，实际应该按行业分组）
            if not result_df.empty:
                result_df['ROE排名百分位'] = result_df['当前ROE'].rank(pct=True)
                result_df['质量排名百分位'] = result_df['ROE质量评分'].rank(pct=True)
            
            return result_df
            
        except Exception as e:
            self._log(f"计算ROE指标失败: {str(e)}", "error")
            return pd.DataFrame()
    
    def _calculate_quality_score(self, roe: float, roe_std: float, roe_trend: float,
                               positive_streak: int, net_margin: float,
                               asset_turnover: float, equity_multiplier: float) -> float:
        """计算ROE质量综合评分
        
        评分标准：
        - ROE水平 (30%权重): ROE越高得分越高
        - ROE稳定性 (25%权重): 标准差越小得分越高
        - ROE趋势 (20%权重): 上升趋势得分更高
        - ROE持续性 (15%权重): 连续正ROE季度越多得分越高
        - 杜邦分析 (10%权重): 净利润率贡献度更高的ROE质量更好
        """
        try:
            score = 0
            
            # 1. ROE水平评分 (0-30分)
            if roe >= 20:
                roe_score = 30
            elif roe >= 15:
                roe_score = 25
            elif roe >= 10:
                roe_score = 20
            elif roe >= 5:
                roe_score = 15
            elif roe > 0:
                roe_score = 10
            else:
                roe_score = 0
            
            # 2. ROE稳定性评分 (0-25分)
            if roe_std <= 2:
                stability_score = 25
            elif roe_std <= 5:
                stability_score = 20
            elif roe_std <= 10:
                stability_score = 15
            elif roe_std <= 15:
                stability_score = 10
            else:
                stability_score = 5
            
            # 3. ROE趋势评分 (0-20分)
            if roe_trend > 2:
                trend_score = 20
            elif roe_trend > 1:
                trend_score = 15
            elif roe_trend > 0:
                trend_score = 12
            elif roe_trend > -1:
                trend_score = 8
            else:
                trend_score = 0
            
            # 4. ROE持续性评分 (0-15分)
            persistence_score = min(positive_streak * 2, 15)
            
            # 5. 杜邦分析评分 (0-10分)
            # 更偏好由净利润率驱动的ROE，而非高财务杠杆
            if net_margin > 0 and equity_multiplier > 0:
                # 净利润率贡献比例
                margin_contribution = net_margin * asset_turnover / max(roe, 0.01)
                if margin_contribution > 0.7:  # 净利润率贡献占主导
                    dupont_score = 10
                elif margin_contribution > 0.5:
                    dupont_score = 8
                elif margin_contribution > 0.3:
                    dupont_score = 6
                else:
                    dupont_score = 3
            else:
                dupont_score = 0
            
            score = roe_score + stability_score + trend_score + persistence_score + dupont_score
            
            return round(score, 2)
            
        except Exception as e:
            self._log(f"计算质量评分失败: {str(e)}", "error")
            return 0.0

    async def generate_signal(self, data: pd.DataFrame, params: Dict[str, Any]) -> List[Signal]:
        """生成信号
        
        Args:
            data: ROE质量数据
            params: 策略参数
            
        Returns:
            List[Signal]: 生成的信号列表
        """
        try:
            if data.empty:
                return []
            
            # 获取筛选参数
            min_roe = float(params.get("min_roe", 10))  # 最小ROE要求
            max_roe_std = float(params.get("max_roe_std", 10))  # 最大ROE标准差
            min_quality_score = float(params.get("min_quality_score", 60))  # 最小质量评分
            min_positive_streak = int(params.get("min_positive_streak", 4))  # 最小连续正ROE季度
            min_roe_percentile = float(params.get("min_roe_percentile", 0.7))  # 最小ROE百分位
            
            self._log(f"ROE质量筛选条件: ROE>={min_roe}%, 标准差<={max_roe_std}%, "
                     f"质量评分>={min_quality_score}, 连续正ROE>={min_positive_streak}季度, "
                     f"ROE排名>={min_roe_percentile*100}%")
            
            # 应用筛选条件
            filtered_data = data[
                (data['当前ROE'] >= min_roe) &
                (data['ROE稳定性'] <= max_roe_std) &
                (data['ROE质量评分'] >= min_quality_score) &
                (data['连续正ROE季度'] >= min_positive_streak) &
                (data['ROE排名百分位'] >= min_roe_percentile)
            ].copy()
            
            # 按质量评分排序
            filtered_data = filtered_data.sort_values('ROE质量评分', ascending=False)
            
            self._log(f"ROE质量筛选结果: {len(filtered_data)}只股票通过筛选")
            
            # 生成信号
            signals = []
            for _, row in filtered_data.iterrows():
                # 根据质量评分确定置信度
                confidence = min(0.9, 0.5 + (row['ROE质量评分'] / 200))
                
                signal = self.create_signal(
                    symbol=row['股票代码'],
                    name=row['股票名称'],
                    direction="BUY",  # ROE质量好的股票建议买入
                    signal_type="fundamental",
                    confidence=confidence,
                    trigger_condition=f"ROE质量评分{row['ROE质量评分']:.1f}分",
                    current_roe=float(row['当前ROE']),
                    roe_stability=float(row['ROE稳定性']),
                    roe_trend=float(row['ROE趋势']),
                    positive_streak=int(row['连续正ROE季度']),
                    quality_score=float(row['ROE质量评分']),
                    roe_percentile=float(row['ROE排名百分位'])
                )
                signals.append(signal)
            
            return signals
            
        except Exception as e:
            self._log(f"生成信号失败: {str(e)}", "error")
            return []

    async def execute(self, context: RuntimeContext) -> List[Signal]:
        """执行策略"""
        try:
            self._log("开始执行ROE质量筛选策略")
            
            # 准备数据
            data = await self.prepare_data(context)
            
            if data.empty:
                self._log("未获取到有效数据", "warning")
                return []
            
            # 生成信号
            signals = await self.generate_signal(data, context.parameters)
            
            # 记录执行结果
            self._log(f"ROE质量筛选策略执行完成，生成{len(signals)}个信号")
            
            return signals
            
        except Exception as e:
            self._log(f"策略执行失败: {str(e)}", "error")
            return []