/**
 * 📡 监控室页面组件
 * 实时监控策略组的执行状态、性能指标和运行日志
 */

import React, { useState, useEffect } from 'react'
import styled from 'styled-components'
import { motion } from 'framer-motion'
import { useStrategyState } from '../../../store/hooks'

// 🎨 样式组件
const PageSection = styled.div`
  margin-bottom: 2rem;
`

const SectionTitle = styled.h3`
  margin: 0 0 1rem 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
  display: flex;
  align-items: center;
  gap: 0.5rem;
`

const StatusIndicator = styled(motion.div)<{ $status: 'online' | 'offline' }>`
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: ${props => props.$status === 'online' ? '#10b981' : '#ef4444'};
  box-shadow: 0 0 8px ${props => props.$status === 'online' ? '#10b98150' : '#ef444450'};
`

const StatsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
`

const StatCard = styled(motion.div)`
  background: #ffffff;
  border-radius: 12px;
  padding: 1rem;
  border: 1px solid #e2e8f0;
  text-align: center;
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  }
`

const StatValue = styled.div`
  font-size: 1.5rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 0.25rem;
`

const StatLabel = styled.div`
  font-size: 0.8rem;
  color: #666;
  font-weight: 500;
`

const MonitoringList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
`

const MonitorCard = styled(motion.div)`
  background: #ffffff;
  border-radius: 12px;
  padding: 1rem;
  border: 1px solid #e2e8f0;
  position: relative;
`

const MonitorHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
`

const StrategyName = styled.h4`
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: #333;
`

const StatusBadge = styled.span<{ $status: 'running' | 'stopped' | 'error' }>`
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  
  ${props => {
    switch (props.$status) {
      case 'running':
        return `
          background: #10b98120;
          color: #10b981;
        `
      case 'stopped':
        return `
          background: #6b728020;
          color: #6b7280;
        `
      case 'error':
        return `
          background: #ef444420;
          color: #ef4444;
        `
      default:
        return ''
    }
  }}
`

const MetricsRow = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
  gap: 0.75rem;
  margin-bottom: 0.75rem;
`

const MetricItem = styled.div`
  text-align: center;
`

const MetricValue = styled.div<{ $color?: string }>`
  font-size: 0.9rem;
  font-weight: 600;
  color: ${props => props.$color || '#333'};
  margin-bottom: 0.25rem;
`

const MetricLabel = styled.div`
  font-size: 0.7rem;
  color: #666;
`

const EmptyState = styled.div`
  text-align: center;
  padding: 3rem 1rem;
  color: #666;
`

const PlaceholderCard = styled.div`
  padding: 2rem;
  text-align: center;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
`

// 🎮 监控室页面组件
function MonitoringRoomPage() {
  const { groups } = useStrategyState()
  const [isOnline, setIsOnline] = useState(true)

  // 🔄 模拟在线状态
  useEffect(() => {
    const interval = setInterval(() => {
      setIsOnline(prev => !prev)
    }, 3000)
    return () => clearInterval(interval)
  }, [])

  // 📊 计算统计数据
  const stats = {
    totalStrategies: groups.length,
    activeStrategies: groups.filter(g => g.status === 'active').length,
    totalSignals: groups.reduce((sum, group) => sum + (group.cards?.length || 0) * 5, 0),
    successRate: groups.length > 0 
      ? groups.reduce((sum, group) => sum + (group.performance_metrics?.win_rate || 0), 0) / groups.length
      : 0
  }

  // 🎯 活跃策略列表
  const activeStrategies = groups.filter(g => g.status === 'active').slice(0, 3)

  // 🎨 状态指示器动画
  const statusVariants = {
    online: {
      scale: [1, 1.2, 1],
      transition: {
        duration: 2,
        repeat: Infinity,
        ease: "easeInOut"
      }
    },
    offline: {
      scale: 1
    }
  }

  return (
    <PageSection>
      <SectionTitle>
        📡 监控室
        <StatusIndicator
          $status={isOnline ? 'online' : 'offline'}
          variants={statusVariants}
          animate={isOnline ? 'online' : 'offline'}
        />
      </SectionTitle>

      {/* 统计概览 */}
      <StatsGrid>
        <StatCard
          whileHover={{ scale: 1.02 }}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <StatValue>{stats.totalStrategies}</StatValue>
          <StatLabel>总策略数</StatLabel>
        </StatCard>
        
        <StatCard
          whileHover={{ scale: 1.02 }}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <StatValue>{stats.activeStrategies}</StatValue>
          <StatLabel>运行中</StatLabel>
        </StatCard>
        
        <StatCard
          whileHover={{ scale: 1.02 }}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <StatValue>{stats.totalSignals}</StatValue>
          <StatLabel>今日信号</StatLabel>
        </StatCard>
        
        <StatCard
          whileHover={{ scale: 1.02 }}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <StatValue>{stats.successRate.toFixed(1)}%</StatValue>
          <StatLabel>成功率</StatLabel>
        </StatCard>
      </StatsGrid>

      {/* 活跃策略监控 */}
      {activeStrategies.length > 0 ? (
        <MonitoringList>
          <h4 style={{ margin: '0 0 1rem 0', color: '#333', fontSize: '1rem' }}>
            🎯 活跃策略
          </h4>
          {activeStrategies.map((strategy, index) => (
            <MonitorCard
              key={strategy.id}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 }}
              whileHover={{ scale: 1.01 }}
            >
              <MonitorHeader>
                <StrategyName>{strategy.name}</StrategyName>
                <StatusBadge $status="running">运行中</StatusBadge>
              </MonitorHeader>

              <MetricsRow>
                <MetricItem>
                  <MetricValue 
                    $color={(strategy.performance_metrics?.total_return || 0) >= 0 ? '#10b981' : '#ef4444'}
                  >
                    {(strategy.performance_metrics?.total_return || 0) >= 0 ? '+' : ''}
                    {(strategy.performance_metrics?.total_return || 0).toFixed(2)}%
                  </MetricValue>
                  <MetricLabel>收益率</MetricLabel>
                </MetricItem>
                
                <MetricItem>
                  <MetricValue>
                    {(strategy.performance_metrics?.win_rate || 0).toFixed(1)}%
                  </MetricValue>
                  <MetricLabel>胜率</MetricLabel>
                </MetricItem>
                
                <MetricItem>
                  <MetricValue>
                    {strategy.cards?.length || 0}
                  </MetricValue>
                  <MetricLabel>信号数</MetricLabel>
                </MetricItem>
                
                <MetricItem>
                  <MetricValue>
                    {strategy.updated_at ? new Date(strategy.updated_at).toLocaleTimeString() : '未知'}
                  </MetricValue>
                  <MetricLabel>最后更新</MetricLabel>
                </MetricItem>
              </MetricsRow>
            </MonitorCard>
          ))}
        </MonitoringList>
      ) : (
        <PlaceholderCard>
          <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>📡</div>
          <div style={{ color: '#666' }}>暂无运行中的策略</div>
        </PlaceholderCard>
      )}
    </PageSection>
  )
}

export default MonitoringRoomPage
