import React, { useMemo, useEffect, useState } from 'react';
import { Typography, Table, Tooltip, Tag, Space } from 'antd';
import { motion } from 'framer-motion';
import { getInitialMarketData, getQuotes, StockQuote } from '../services/realTimeQuoteService';
import './SignalTable.scss'; // 新增样式文件引用

const { Text } = Typography;

// 类型定义
export interface Stock {
  code: string;
  symbol?: string;
  name?: string;
  price?: number;
  market_data?: {
    name?: string;
    price?: number;
    market_cap?: number;
    day60_change?: number;
    industry?: string;
    [key: string]: any;
  };
  [key: string]: any;
}

interface SignalTableProps {
  signals: Stock[];
  className?: string;
}

// 工具函数
const formatNumber = (value: any, unit: string = '', decimalPlaces: number = 2): string => {
  if (value === undefined || value === null) return '-';
  
  const numValue = typeof value === 'string' ? parseFloat(value) : Number(value);
  if (isNaN(numValue)) return '-';
  
  return numValue.toFixed(decimalPlaces) + unit;
};

const formatMarketCap = (value: any): string => {
  if (value === undefined || value === null) return '-';
  
  const numValue = typeof value === 'string' ? parseFloat(value) : Number(value);
  if (isNaN(numValue)) return '-';
  
  if (numValue >= 100000000) {
    return (numValue / 100000000).toFixed(2) + '亿';
  } else if (numValue >= 10000) {
    return (numValue / 10000).toFixed(2) + '万';
  } 
  return numValue.toFixed(2);
};

const getChangeColor = (value: number): string => {
  return value > 0 ? '#50fa7b' : (value < 0 ? '#ff5555' : '#f8f8f2');
};

const SignalTable: React.FC<SignalTableProps> = ({ signals = [], className = '' }) => {
  // 添加状态来跟踪是否正在加载行情数据
  const [isLoadingMarketData, setIsLoadingMarketData] = useState(false);
  
  // 当signals变化时获取行情数据
  useEffect(() => {
    // 仅在有信号且没有市场数据时获取市场数据
    const needMarketData = signals.length > 0 && 
      signals.some(stock => !stock.market_data || !stock.market_data.name);
    
    if (needMarketData && !isLoadingMarketData) {
      const fetchMarketData = async () => {
        setIsLoadingMarketData(true);
        try {
          // 提取所有股票代码
          const symbols = signals.map(stock => stock.symbol || stock.code);
          console.log('正在获取股票行情数据:', symbols);
          
          // 调用API获取行情数据
          await getInitialMarketData(symbols);
          
          console.log('股票行情数据获取成功');
        } catch (error) {
          console.error('获取股票行情数据失败:', error);
        } finally {
          setIsLoadingMarketData(false);
        }
      };
      
      fetchMarketData();
    }
  }, [signals]);
  
  // 标准化处理信号数据
  const stockList = useMemo(() => {
    // 开发环境下记录调试信息
    if (process.env.NODE_ENV !== 'production' && signals.length > 0) {
      console.log('信号数据示例:', signals[0]);
    }
    
    // 从行情服务获取最新数据
    const marketData = getQuotes(signals.map(s => s.symbol || s.code), 'object') as Record<string, StockQuote>;
    
    return signals.map((stock: any) => {
      // 获取股票代码
      const symbol = stock.symbol || stock.code;
      
      // 优先使用后端整合好的market_data
      const stockMarketData = stock.market_data || {};
      
      // 尝试从行情服务获取更完整的数据
      const quoteData = marketData[symbol] || {};
      
      // 获取60日涨跌幅 - 修复数据来源问题，查看多个可能的字段名
      let day60Change = 0;
      if (stockMarketData.day60_change !== undefined) {
        day60Change = stockMarketData.day60_change;
      } else if (quoteData.change_60d !== undefined) {
        day60Change = quoteData.change_60d;
      } else if (quoteData.day60_change !== undefined) {
        day60Change = quoteData.day60_change;
      }
      
      // 确保涨跌幅是数值类型，并且按百分比格式化（如果已经是百分比则不需要再除以100）
      if (typeof day60Change === 'string') {
        day60Change = parseFloat(day60Change);
      }
      
      // 检查数值是否在合理范围内 (大于1可能是百分比值，如34.5表示34.5%)
      if (Math.abs(day60Change) > 1 && Math.abs(day60Change) < 100) {
        // 已经是百分比值，不需要再处理
      } else if (Math.abs(day60Change) >= 100) {
        // 可能是放大了100倍的百分比值，转换回来
        day60Change = day60Change / 100;
      }
      
      // 合并数据，优先使用后端数据，其次使用行情服务数据
      const mergedMarketData = {
        ...quoteData,
        ...stockMarketData,
        // 明确设置优先级最高的字段
        name: stockMarketData.name || quoteData.name || symbol,
        price: stockMarketData.price || quoteData.price || 0,
        market_cap: stockMarketData.market_cap || quoteData.market_cap || 0,
        day60_change: day60Change,
      };
      
      // 创建标准化的股票对象
      return {
        key: symbol,
        code: symbol,
        symbol: symbol,
        name: mergedMarketData.name,
        price: mergedMarketData.price,
        marketCap: mergedMarketData.market_cap,
        day60_change: mergedMarketData.day60_change,
        // 行情数据保留，但不再显示
        quoteData: mergedMarketData
      };
    });
  }, [signals, isLoadingMarketData]); // 添加isLoadingMarketData作为依赖
  
  // 动态生成表格列
  const columns = useMemo(() => {
    // 基础列配置 - 删除行业和行情数据列
    const baseColumns = [
      {
        title: '代码',
        dataIndex: 'code',
        key: 'code',
        fixed: 'left' as const,
        width: 90,
        render: (code: string) => (
          <Text className="stock-code">
            {code}
          </Text>
        ),
      },
      {
        title: '名称',
        dataIndex: 'name',
        key: 'name',
        fixed: 'left' as const,
        width: 100,
        render: (name: string) => (
          <Text className="stock-name">
            {name}
          </Text>
        ),
      },
      {
        title: '价格',
        dataIndex: 'price',
        key: 'price',
        width: 90,
        render: (price: number) => (
          <Text className="stock-price">
            {price === undefined || price === null ? '-' : `¥${formatNumber(price)}`}
          </Text>
        ),
      },
      {
        title: '总市值',
        dataIndex: 'marketCap',
        key: 'marketCap',
        width: 90,
        render: (value: any) => (
          <Text className="stock-market-cap">
            {formatMarketCap(value)}
          </Text>
        ),
      },
      {
        title: '60日涨跌幅',
        dataIndex: 'day60_change',
        key: 'day60_change',
        width: 100,
        render: (value: number) => {
          const numValue = typeof value === 'string' ? parseFloat(value) : Number(value);
          
          return (
            <Text style={{ 
              color: getChangeColor(numValue),
              fontFamily: 'monospace', 
              fontSize: '13px' 
            }}>
              {value === undefined || value === null ? '0.00%' : 
              `${numValue > 0 ? '+' : ''}${formatNumber(numValue, '%')}`}
            </Text>
          );
        },
      },
    ];
    
    // 为了避免表格过宽，添加水平滚动支持
    return baseColumns;
  }, [stockList]);

  if (signals.length === 0) {
    return (
      <div className="empty-result">
        <Text className="empty-text">未找到匹配的股票</Text>
      </div>
    );
  }

  return (
    <motion.div 
      className={`signal-table-container ${className}`}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ delay: 0.2 }}
    >
      <div className="results-header">
        <Space>
          <Text strong className="results-title">执行结果</Text>
          <Tag color="#ff79c6" className="results-count">
            找到 {stockList.length} 个标的
          </Tag>
        </Space>
      </div>

      <Table 
        columns={columns}
        dataSource={stockList}
        size="small"
        pagination={{ 
          pageSize: 10,
          size: 'small',
          className: 'custom-pagination'
        }}
        rowKey="key"
        className="signals-table"
        scroll={{ x: 'max-content', y: 400 }}
      />
    </motion.div>
  );
};

export default React.memo(SignalTable); 
 