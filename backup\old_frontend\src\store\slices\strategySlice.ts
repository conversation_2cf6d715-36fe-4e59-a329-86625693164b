import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { Strategy } from '../../types/strategy';

interface StrategyState {
  strategies: Strategy[];
  selectedStrategy: Strategy | null;
  loading: boolean;
  error: string | null;
}

const initialState: StrategyState = {
  strategies: [],
  selectedStrategy: null,
  loading: false,
  error: null,
};

const strategySlice = createSlice({
  name: 'strategy',
  initialState,
  reducers: {
    setStrategies: (state, action: PayloadAction<Strategy[]>) => {
      state.strategies = action.payload;
    },
    setSelectedStrategy: (state, action: PayloadAction<Strategy>) => {
      state.selectedStrategy = action.payload;
    },
    clearSelectedStrategy: (state) => {
      state.selectedStrategy = null;
    },
    addStrategy: (state, action: PayloadAction<Strategy>) => {
      state.strategies.push(action.payload);
    },
    updateStrategy: (state, action: PayloadAction<Strategy>) => {
      const index = state.strategies.findIndex(s => s.id === action.payload.id);
      if (index !== -1) {
        state.strategies[index] = action.payload;
      }
    },
    deleteStrategy: (state, action: PayloadAction<string>) => {
      state.strategies = state.strategies.filter(s => s.id !== action.payload);
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
  },
});

export const {
  setStrategies,
  setSelectedStrategy,
  clearSelectedStrategy,
  addStrategy,
  updateStrategy,
  deleteStrategy,
  setLoading,
  setError,
} = strategySlice.actions;

export default strategySlice.reducer; 