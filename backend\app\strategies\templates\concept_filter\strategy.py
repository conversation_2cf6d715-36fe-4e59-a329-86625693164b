"""
概念名称筛选策略
"""
import logging
import pandas as pd
from typing import Dict, Any, List, Optional
from datetime import datetime
import yaml
import json
from pathlib import Path

from app.core.strategy.base import UnifiedStrategyCard
from app.core.runtime.types import RuntimeContext, Signal, SignalType
from app.core.runtime.types import StrategyMode
from app.core.data.db.base import db_manager
from sqlalchemy import text

# 从 template.json 加载配置
template_path = Path(__file__).parent / "template.json"
with open(template_path, "r", encoding="utf-8") as f:
    template = json.load(f)

logger = logging.getLogger("app")

class ConceptFilterStrategy(UnifiedStrategyCard):
    """概念名称筛选策略"""
    
    def __init__(self, context: Optional[RuntimeContext] = None):
        """初始化策略"""
        super().__init__(context)
        
        # 加载配置文件
        config_path = Path(__file__).parent / "config.yaml"
        with open(config_path, "r", encoding="utf-8") as f:
            self.config = yaml.safe_load(f)

    def _log(self, message: str, level: str = "info"):
        """记录日志"""
        getattr(logger, level)(message)
        if hasattr(self.context, 'logs') and self.context.logs is not None:
            prefix = "警告: " if level == "warning" else "错误: " if level == "error" else ""
            self.context.logs.append(f"{prefix}{message}")
    
    async def prepare_data(self, context: RuntimeContext) -> pd.DataFrame:
        """准备数据"""
        try:
            # 检查串行执行模式
            if not self._check_sequential_mode(context):
                return pd.DataFrame()
            
            # 获取模式配置 - 直接使用执行模式作为数据源配置
            mode = context.mode.value if context.mode else "filter"
            mode_config = self.config["data_sources"].get(mode, self.config["data_sources"]["filter"])
            
            # 获取股票代码列表
            symbols = self._get_stock_symbols(context)
            
            # 获取关键词
            keywords = [kw.strip() for kw in context.parameters.get("keywords", "").split(",") if kw.strip()]
            if not keywords:
                self._log("未提供有效的关键字", "warning")
                return pd.DataFrame()
            
            # 检查缓存
            cache_key = f"{self._get_cache_key(context)}_{mode}"
            cached_data = self._data_cache.get(cache_key)
            if cached_data is not None:
                if symbols and not cached_data.empty:
                    filtered_cache = cached_data[cached_data["股票代码"].isin(symbols)]
                    self._log(f"从缓存获取: {len(filtered_cache)}只股票")
                    return filtered_cache
                return cached_data
            
            # 获取数据
            db_type = mode_config["database"]["type"]
            if db_type == "postgresql":
                data = await self._get_data_from_postgresql(mode_config, symbols, keywords)
            else:
                self._log(f"不支持的数据库类型: {db_type}", "warning")
                return pd.DataFrame()
            
            if data.empty:
                return pd.DataFrame()
            
            # 统计每个概念包含的股票数量
            concept_stats = data.groupby('概念名称')['股票代码'].nunique()
            stats_msg = "概念统计:\n" + "\n".join([f"- {concept}: {count}只股票" for concept, count in concept_stats.items()])
            self._log(stats_msg)
            
            # 缓存数据
            self._data_cache.set(cache_key, data, ttl=60)
            return data
            
        except Exception as e:
            self._log(f"准备数据失败: {str(e)}", "error")
            return pd.DataFrame()
    
    async def _get_data_from_postgresql(self, mode_config: Dict[str, Any], symbols: List[str], keywords: List[str]) -> pd.DataFrame:
        """从PostgreSQL获取数据"""
        try:
            if "database" not in mode_config or "table" not in mode_config["database"]:
                raise ValueError("配置错误：数据源未定义表名")
            
            table = mode_config["database"]["table"]
            fields = ["股票代码", "股票名称", "概念名称"]
            
            # 构建SQL查询
            concept_conditions = [f"概念名称 LIKE '%{kw}%'" for kw in keywords]
            fields_str = ", ".join(fields)
            sql_query = f"SELECT DISTINCT {fields_str} FROM {table}"
            
            where_clauses = []
            if symbols:
                placeholders = ", ".join([f"'{symbol}'" for symbol in symbols])
                where_clauses.append(f"股票代码 IN ({placeholders})")
            
            if concept_conditions:
                where_clauses.append(f"({' OR '.join(concept_conditions)})")
            
            if where_clauses:
                sql_query += f" WHERE {' AND '.join(where_clauses)}"
            
            async with db_manager.get_session() as session:
                result = await session.execute(text(sql_query))
                rows = result.fetchall()
                return pd.DataFrame(rows, columns=fields)
                
        except Exception as e:
            self._log(f"从PostgreSQL获取数据失败: {str(e)}", "error")
            return pd.DataFrame()
            
    async def generate_signal(self, data: pd.DataFrame, params: Dict[str, Any]) -> List[Signal]:
        """生成信号"""
        try:
            if data.empty or "概念名称" not in data.columns:
                return []
            
            # 获取关键词
            keywords = [kw.strip() for kw in params.get("keywords", "").split(",") if kw.strip()]
            if not keywords:
                self._log("未指定关键词", "warning")
                return []
            
            self._log(f"概念名称筛选关键词: {', '.join(keywords)}")
            
            # 获取每个股票的概念信息
            stock_concepts = {}
            for _, group in data.groupby('股票代码'):
                stock_code = group['股票代码'].iloc[0]
                stock_concepts[stock_code] = {
                    "name": group['股票名称'].iloc[0],
                    "concepts": group['概念名称'].unique().tolist()
                }
            
            matched_stocks = set(stock_concepts.keys())
            self._log(f"筛选出 {len(matched_stocks)}只股票")
            
            # 使用统一的create_signal方法生成信号
            signals = []
            
            for stock_code in matched_stocks:
                stock_info = stock_concepts[stock_code]
                # 找出匹配的关键词
                matched_concepts = []
                for concept in stock_info["concepts"]:
                    for kw in keywords:
                        if kw.lower() in concept.lower():
                            matched_concepts.append(concept)
                            break
                
                signals.append(
                    self.create_signal(
                        symbol=stock_code,
                        name=stock_info["name"],
                        direction="BUY",
                        signal_type="fundamental",
                        confidence=0.8,
                        trigger_condition=f"概念名称包含: {', '.join(keywords)}",
                        concepts=matched_concepts,
                        all_concepts=stock_info["concepts"]
                    )
                )
            
            # 添加一条摘要日志，显示匹配的总数而不是每个股票的详情
            self._log(f"共有 {len(signals)} 只股票匹配概念: {', '.join(keywords)}")
            
            return signals
            
        except Exception as e:
            self._log(f"生成信号失败: {str(e)}", "error")
            return [] 