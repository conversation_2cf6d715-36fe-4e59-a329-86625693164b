/**
 * 🎯 择时信号表格组件 - 赛博朋克风格
 * 基于旧版前端TimingSignalTable重构
 */

import React, { useMemo } from 'react';
import type { TimingSignal } from './types';
import './TimingSignalTable.scss';

interface TimingSignalTableProps {
  signals: TimingSignal[];
  className?: string;
}

// 格式化数字的工具函数
const formatNumber = (value: any, unit: string = '', decimalPlaces: number = 2): string => {
  if (value === undefined || value === null) return '-';
  
  const numValue = typeof value === 'string' ? parseFloat(value) : Number(value);
  if (isNaN(numValue)) return '-';
  
  return numValue.toFixed(decimalPlaces) + unit;
};

// 获取涨跌颜色 - 统一使用Dracula主题色
const getChangeColor = (value: number): string => {
  if (value > 0) return '#50fa7b'; // Dracula的绿色
  if (value < 0) return '#ff5555'; // Dracula的红色
  return '#f8f8f2'; // Dracula的默认文本色
};

// 获取信号方向的颜色 - 统一使用Dracula主题色
const getDirectionColor = (direction: string): string => {
  if (direction === 'BUY') return '#50fa7b'; // Dracula的绿色
  if (direction === 'SELL') return '#ff5555'; // Dracula的红色
  return '#6272a4'; // Dracula的注释色
};

// 获取信号方向的中文显示
const getDirectionText = (direction: string): string => {
  switch (direction) {
    case 'BUY': return '买入';
    case 'SELL': return '卖出';
    default: return '观望';
  }
};

const TimingSignalTable: React.FC<TimingSignalTableProps> = ({ signals = [], className = '' }) => {
  // 直接使用标准化的信号数据
  const signalList = useMemo(() => {
    // 开发环境下记录调试信息
    if (process.env.NODE_ENV !== 'production' && signals.length > 0) {
      console.log('择时信号数据示例:', signals[0]);
    }

    return signals.map((signal: TimingSignal) => ({
      key: signal.symbol,
      symbol: signal.symbol,
      name: signal.name || signal.symbol,
      direction: signal.direction || 'UNKNOWN',
      trigger_condition: signal.trigger_condition || '-',
      latest_price: signal.latest_price || 0,
      ma_diff_pct: signal.ma_diff_pct || 0,
      industry: signal.industry || '-'
    }));
  }, [signals]);

  if (!signalList || signalList.length === 0) {
    return (
      <div className="timing-signal-table-container">
        <div className="results-header">
          <span className="results-title">择时信号</span>
          <span className="results-count">0 个信号</span>
        </div>
        <div className="empty-result">
          <span className="empty-text">暂无择时信号</span>
        </div>
      </div>
    );
  }

  return (
    <div className={`timing-signal-table-container ${className}`}>
      <div className="results-header">
        <span className="results-title">择时信号</span>
        <span className="results-count">{signalList.length} 个信号</span>
      </div>
      
      <div className="signals-table">
        <table className="custom-table">
          <thead>
            <tr>
              <th>代码</th>
              <th>名称</th>
              <th>信号</th>
              <th>价格</th>
              <th>均线偏离</th>
              <th>触发条件</th>
              <th>行业</th>
            </tr>
          </thead>
          <tbody>
            {signalList.map((signal) => (
              <tr key={signal.key}>
                <td>
                  <span className="stock-code">{signal.symbol}</span>
                </td>
                <td>
                  <span className="stock-name">{signal.name}</span>
                </td>
                <td>
                  <span 
                    className="direction-badge"
                    style={{ 
                      color: getDirectionColor(signal.direction),
                      backgroundColor: `${getDirectionColor(signal.direction)}20`,
                      border: `1px solid ${getDirectionColor(signal.direction)}40`
                    }}
                  >
                    {getDirectionText(signal.direction)}
                  </span>
                </td>
                <td>
                  <span className="stock-price">¥{formatNumber(signal.latest_price)}</span>
                </td>
                <td>
                  <span 
                    style={{ color: getChangeColor(signal.ma_diff_pct) }}
                    className="ma-diff"
                  >
                    {signal.ma_diff_pct > 0 ? '+' : ''}{formatNumber(signal.ma_diff_pct, '%')}
                  </span>
                </td>
                <td>
                  <span className="trigger-condition" title={signal.trigger_condition}>
                    {signal.trigger_condition.length > 20 
                      ? `${signal.trigger_condition.slice(0, 20)}...` 
                      : signal.trigger_condition
                    }
                  </span>
                </td>
                <td>
                  <span className="stock-industry">{signal.industry}</span>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
        
        {signalList.length > 10 && (
          <div className="table-pagination">
            <span>显示 1-{Math.min(10, signalList.length)} 条，共 {signalList.length} 条</span>
          </div>
        )}
      </div>
    </div>
  );
};

export default TimingSignalTable;
