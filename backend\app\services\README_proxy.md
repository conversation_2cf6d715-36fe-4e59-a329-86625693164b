# 代理服务功能说明

本项目集成了 [jhao104/proxy_pool](https://github.com/jhao104/proxy_pool) 项目，实现了在获取 K 线数据时自动使用代理 IP，以避免 API 请求限制或被封禁 IP。

## 功能特点

1. 自动从代理池获取可用代理
2. 支持代理失败重试机制
3. 代理缓存管理
4. 失败代理自动剔除
5. 支持 HTTP 和 HTTPS 代理

## 配置说明

代理服务默认配置：
- 代理池地址：http://127.0.0.1:5010
- 默认启用代理：true
- 代理失败重试次数：3次
- 代理缓存更新间隔：60秒
- 代理最大使用次数：30次

## 使用方法

### 部署代理池服务

首先需要部署 [jhao104/proxy_pool](https://github.com/jhao104/proxy_pool) 服务：

#### 使用 Docker（推荐）

```bash
# 拉取镜像
docker pull jhao104/proxy_pool

# 启动容器
docker run --env DB_CONN=redis://:password@ip:port/0 -p 5010:5010 jhao104/proxy_pool:latest
```

或使用 docker-compose：

```yaml
version: '3'
services:
  proxy_pool:
    image: jhao104/proxy_pool:latest
    container_name: proxy_pool
    restart: always
    ports:
      - "5010:5010"
    environment:
      - DB_CONN=redis://:password@redis:6379/0
  
  redis:
    image: redis:alpine
    container_name: redis
    restart: always
    ports:
      - "6379:6379"
```

#### 本地安装

```bash
# 克隆代码
<NAME_EMAIL>:jhao104/proxy_pool.git

# 安装依赖
pip install -r requirements.txt

# 修改配置
# 编辑 setting.py 文件，配置数据库连接和代理获取方法

# 启动调度程序和API服务
python proxyPool.py schedule
python proxyPool.py server
```

### 在代码中使用代理服务

代理服务已集成到 `minute_kline_service.py` 和 `daily_kline_service.py` 文件中，默认启用代理功能。

如果需要关闭代理功能，可以修改对应服务的 `use_proxy` 属性：

```python
# 在需要的地方禁用代理
kline_service = MinuteKlineService.get_instance()
kline_service.use_proxy = False

# 或修改重试次数
kline_service.proxy_retry_count = 5
```

### 代理服务 API

如需在其他服务中使用代理功能，可以直接使用 `ProxyService` 类：

```python
from services.proxy_service import ProxyService

# 获取代理服务实例
proxy_service = ProxyService.get_instance()

# 获取代理字典（用于 requests 库）
proxies = proxy_service.get_request_proxies()
if proxies:
    response = requests.get('https://api.example.com', proxies=proxies)
    
# 报告代理失败
if response.status_code != 200:
    proxy_str = proxies.get('http', '').replace('http://', '')
    proxy_service.report_proxy_failure(proxy_str)
```

## 故障排除

1. 确保代理池服务正常运行：访问 http://127.0.0.1:5010 检查
2. 检查代理池中是否有可用代理：访问 http://127.0.0.1:5010/count
3. 如果代理池中没有代理，请检查代理抓取配置或手动添加代理
4. 检查日志中的代理使用情况和失败记录

## 其他相关资源

- [jhao104/proxy_pool 项目文档](https://github.com/jhao104/proxy_pool)
- [AKShare 文档](https://akshare.xyz/) 