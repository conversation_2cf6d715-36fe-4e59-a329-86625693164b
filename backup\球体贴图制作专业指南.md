# 🌍 球体贴图制作专业指南

## 📐 球体UV映射原理

### UV坐标系统
```
球体UV映射采用等距圆柱投影 (Equirectangular Projection)
- U轴 (水平): 0-1 对应经度 -180° 到 +180°
- V轴 (垂直): 0-1 对应纬度 +90° 到 -90°

纹理尺寸比例: 2:1 (宽:高)
推荐分辨率: 2048x1024, 4096x2048, 8192x4096
```

### 地球投影变形
```
极地区域: 严重拉伸 (南北极点)
赤道区域: 最少变形 (最真实)
中纬度: 适度变形 (可接受)

设计时需要考虑变形补偿
```

## 🎨 贴图类型详解

### 1. 漫反射贴图 (Diffuse/Albedo)
```
用途: 物体的基础颜色
格式: JPG/PNG (RGB)
分辨率: 2048x1024 (标准)
色彩空间: sRGB

地球内容:
- 海洋: 深蓝色 #1e3a8a 到 #3b82f6
- 陆地: 绿色 #22c55e 到 #16a34a  
- 沙漠: 黄褐色 #d97706 到 #92400e
- 雪地: 白色 #f8fafc 到 #e2e8f0
- 城市: 灰色 #6b7280
```

### 2. 法线贴图 (Normal Map)
```
用途: 表面凹凸细节
格式: PNG (RGB)
分辨率: 2048x1024
色彩: 中性紫色 #8080ff 为平面

RGB通道含义:
- R通道: X方向法线 (左右凹凸)
- G通道: Y方向法线 (上下凹凸)  
- B通道: Z方向法线 (深度，通常为255)

地球内容:
- 山脉: 白色高光 (凸起)
- 海沟: 深色阴影 (凹陷)
- 平原: 中性紫色 (平坦)
```

### 3. 粗糙度贴图 (Roughness Map)
```
用途: 表面光滑度控制
格式: JPG (灰度)
分辨率: 1024x512 (可以较低)

灰度值含义:
- 黑色 (0): 完全光滑 (镜面反射)
- 白色 (255): 完全粗糙 (漫反射)

地球内容:
- 海洋: 深灰 #2d3748 (较光滑)
- 冰面: 黑色 #000000 (很光滑)
- 岩石: 浅灰 #a0aec0 (粗糙)
- 沙漠: 中灰 #718096 (中等)
```

### 4. 高度贴图 (Height/Displacement)
```
用途: 几何体位移
格式: PNG (灰度)
分辨率: 2048x1024

灰度值含义:
- 黑色: 最低点 (海平面以下)
- 白色: 最高点 (山峰)
- 中灰: 海平面

地球内容:
- 海洋: 深色 #1a202c
- 平原: 中灰 #4a5568  
- 丘陵: 浅灰 #a0aec0
- 山峰: 白色 #ffffff
```

### 5. 自发光贴图 (Emission Map)
```
用途: 夜景城市灯光
格式: PNG (RGB)
分辨率: 2048x1024

地球内容:
- 大部分区域: 黑色 #000000
- 城市区域: 暖黄色 #fbbf24
- 工业区: 橙红色 #f97316
- 特殊地标: 彩色光点
```

## 🛠️ 制作工具和流程

### 方案1: Photoshop制作 (推荐新手)

#### 步骤1: 创建基础文档
```
1. 新建文档: 2048x1024像素, RGB模式
2. 添加参考网格: 视图 → 新建参考线
   - 垂直线: 25%, 50%, 75% (经度参考)
   - 水平线: 25%, 50%, 75% (纬度参考)
3. 创建图层组: 海洋、陆地、细节、城市
```

#### 步骤2: 绘制海洋
```
1. 新建图层 "海洋"
2. 填充深蓝色 #1e40af
3. 添加渐变: 顶部(极地)较浅，中部(赤道)较深
4. 使用云彩滤镜添加变化: 滤镜 → 渲染 → 云彩
5. 调整不透明度到30%，混合模式"叠加"
```

#### 步骤3: 绘制大陆
```
1. 新建图层 "大陆"
2. 使用套索工具绘制大陆轮廓
   - 亚洲: 右侧中部 (U: 0.6-0.9, V: 0.2-0.7)
   - 欧洲: 中部偏右 (U: 0.5-0.65, V: 0.25-0.45)
   - 非洲: 中部 (U: 0.45-0.65, V: 0.4-0.8)
   - 北美: 左侧 (U: 0.15-0.4, V: 0.2-0.6)
   - 南美: 左侧下部 (U: 0.2-0.4, V: 0.6-0.9)
3. 填充绿色 #22c55e
4. 添加纹理: 滤镜 → 纹理 → 纹理化
```

#### 步骤4: 添加细节
```
1. 山脉: 使用画笔工具，褐色 #92400e
2. 沙漠: 黄褐色 #d97706，使用柔边画笔
3. 森林: 深绿色 #166534，纹理画笔
4. 冰川: 白色 #f8fafc，在极地区域
5. 河流: 蓝色细线，连接海洋
```

### 方案2: Blender程序化生成

#### 节点设置
```
1. 添加UV球体
2. 进入着色器编辑器
3. 创建节点组:

噪声纹理 → 色彩渐变 → 混合着色器
    ↓
坐标信息 → 分离XYZ → 数学节点(控制纬度)
    ↓
渐变纹理 → 色彩渐变 → 混合着色器
```

#### 海洋材质节点
```
纹理坐标 → 映射 → 噪声纹理
    ↓
色彩渐变(蓝色渐变) → 混合RGB
    ↓
Principled BSDF (粗糙度: 0.1)
```

#### 陆地材质节点
```
纹理坐标 → 映射 → Voronoi纹理
    ↓
色彩渐变(绿-棕渐变) → 混合RGB
    ↓
噪声纹理(细节) → 混合RGB
    ↓
Principled BSDF (粗糙度: 0.8)
```

### 方案3: 在线工具制作

#### NASA地球纹理下载
```
网站: https://visibleearth.nasa.gov/
推荐资源:
- Blue Marble: 真实地球影像
- Bathymetry: 海底地形
- Night Lights: 夜景灯光

下载后需要调整:
1. 调整尺寸到2048x1024
2. 色彩校正 (增加饱和度)
3. 卡通化处理 (减少细节)
```

#### 程序化生成网站
```
网站: https://cpetry.github.io/NormalMap-Online/
用途: 从高度图生成法线贴图

使用步骤:
1. 上传高度图 (黑白图像)
2. 调整强度 (建议0.5-1.0)
3. 下载生成的法线贴图
```

## 💻 代码集成

### 在ToonWorld3D中使用贴图
```typescript
// 修改 ToonEarth 组件
const ToonEarth = ({ useTextures = false }) => {
  const earthTextures = useMemo(() => {
    if (!useTextures) return null;
    
    const loader = new THREE.TextureLoader();
    return {
      diffuse: loader.load('/textures/earth_diffuse_2k.jpg'),
      normal: loader.load('/textures/earth_normal_2k.jpg'),
      roughness: loader.load('/textures/earth_roughness_1k.jpg'),
      emission: loader.load('/textures/earth_emission_2k.jpg')
    };
  }, [useTextures]);

  const earthMaterial = useMemo(() => {
    if (useTextures && earthTextures) {
      return new THREE.MeshStandardMaterial({
        map: earthTextures.diffuse,
        normalMap: earthTextures.normal,
        roughnessMap: earthTextures.roughness,
        emissiveMap: earthTextures.emission,
        emissiveIntensity: 0.3
      });
    }
    
    // Fallback: 程序化材质
    return new THREE.MeshToonMaterial({
      color: '#4a90e2',
      transparent: true,
      opacity: 0.9
    });
  }, [useTextures, earthTextures]);

  return (
    <mesh geometry={earthGeometry}>
      <primitive object={earthMaterial} />
    </mesh>
  );
};
```

### 纹理优化设置
```typescript
// 纹理加载优化
const optimizeTexture = (texture: THREE.Texture) => {
  texture.wrapS = texture.wrapT = THREE.RepeatWrapping;
  texture.anisotropy = 16; // 提高远距离清晰度
  texture.generateMipmaps = true; // 自动生成多级纹理
  texture.minFilter = THREE.LinearMipmapLinearFilter;
  texture.magFilter = THREE.LinearFilter;
  return texture;
};
```

## 📊 性能优化建议

### 纹理分辨率选择
```
移动设备: 1024x512 (1MB)
桌面设备: 2048x1024 (4MB)  
高端设备: 4096x2048 (16MB)

动态加载:
const textureSize = isMobile ? '1k' : '2k';
const diffuseMap = loader.load(`/textures/earth_diffuse_${textureSize}.jpg`);
```

### 压缩格式
```
WebP: 最佳压缩比，现代浏览器支持
JPEG: 通用格式，适合漫反射贴图
PNG: 支持透明度，适合法线贴图
KTX2: GPU原生格式，最佳性能 (高级)
```

---

*🎨 通过这个指南，您可以制作出专业级的球体贴图，为您的3D地球增添真实感和视觉冲击力！*
