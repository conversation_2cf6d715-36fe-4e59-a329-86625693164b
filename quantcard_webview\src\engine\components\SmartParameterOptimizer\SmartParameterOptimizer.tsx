/**
 * 🧬 智能参数调优组件 - Smart Parameter Optimizer
 * AI驱动的参数优化和可视化界面
 */

import React, { useState, useEffect, useMemo, useCallback } from 'react';
import styled from 'styled-components';
import { motion, AnimatePresence } from 'framer-motion';

// 🎯 TypeScript类型定义
interface Parameter {
  name: string;
  type: 'number' | 'range' | 'select' | 'boolean';
  current: any;
  min?: number;
  max?: number;
  step?: number;
  options?: string[];
  description: string;
  impact: 'high' | 'medium' | 'low'; // 对性能的影响程度
}

interface OptimizationMethod {
  id: string;
  name: string;
  description: string;
  icon: string;
  complexity: 'simple' | 'advanced' | 'expert';
  estimatedTime: string;
}

interface OptimizationResult {
  parameters: Record<string, any>;
  performance: {
    score: number;
    metrics: {
      return: number;
      sharpe: number;
      drawdown: number;
      volatility: number;
    };
  };
  confidence: number;
}

interface SmartOptimizerProps {
  parameters: Parameter[];
  onParametersChange: (params: Record<string, any>) => void;
  onOptimizationStart: (method: string, params: Record<string, any>) => void;
  onOptimizationStop: () => void;
  optimizationStatus: 'idle' | 'running' | 'completed' | 'error';
  progress: number;
  results?: OptimizationResult[];
}

// 🎨 主容器
const OptimizerContainer = styled.div`
  background: rgba(22, 33, 62, 0.6);
  border: 1px solid rgba(0, 245, 255, 0.2);
  border-radius: 12px;
  padding: 1.5rem;
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, 
      rgba(0, 245, 255, 0.8),
      rgba(255, 0, 255, 0.8),
      rgba(57, 255, 20, 0.8)
    );
  }
`;

// 🎮 标题区域
const OptimizerHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
`;

const Title = styled.h3`
  font-family: "Orbitron", sans-serif;
  color: #00f5ff;
  font-size: 1.2rem;
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  
  &::before {
    content: '';
    width: 4px;
    height: 20px;
    background: linear-gradient(180deg, #00f5ff, #ff00ff);
    border-radius: 2px;
  }
`;

const StatusIndicator = styled(motion.div)<{ status: string }>`
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-family: "Inter", sans-serif;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  
  ${props => {
    switch (props.status) {
      case 'idle':
        return `
          background: rgba(136, 146, 176, 0.2);
          border: 1px solid rgba(136, 146, 176, 0.4);
          color: #8892b0;
        `;
      case 'running':
        return `
          background: rgba(57, 255, 20, 0.2);
          border: 1px solid rgba(57, 255, 20, 0.4);
          color: #39ff14;
        `;
      case 'completed':
        return `
          background: rgba(0, 245, 255, 0.2);
          border: 1px solid rgba(0, 245, 255, 0.4);
          color: #00f5ff;
        `;
      case 'error':
        return `
          background: rgba(255, 7, 58, 0.2);
          border: 1px solid rgba(255, 7, 58, 0.4);
          color: #ff073a;
        `;
      default:
        return `
          background: rgba(136, 146, 176, 0.2);
          border: 1px solid rgba(136, 146, 176, 0.4);
          color: #8892b0;
        `;
    }
  }}
`;

// 📊 参数网格
const ParameterGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
`;

// 🎛️ 参数卡片
const ParameterCard = styled(motion.div)<{ impact: string }>`
  background: rgba(10, 10, 15, 0.8);
  border: 1px solid ${props => 
    props.impact === 'high' ? 'rgba(255, 0, 255, 0.3)' :
    props.impact === 'medium' ? 'rgba(0, 245, 255, 0.3)' :
    'rgba(136, 146, 176, 0.2)'
  };
  border-radius: 8px;
  padding: 1rem;
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: ${props => 
      props.impact === 'high' ? 'rgba(255, 0, 255, 0.8)' :
      props.impact === 'medium' ? 'rgba(0, 245, 255, 0.8)' :
      'rgba(136, 146, 176, 0.5)'
    };
    border-radius: 0 4px 4px 0;
  }
`;

const ParameterLabel = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
  
  .name {
    font-family: "Inter", sans-serif;
    color: #00f5ff;
    font-weight: 600;
    font-size: 0.9rem;
  }
  
  .impact {
    font-family: "Inter", sans-serif;
    font-size: 0.7rem;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    padding: 0.2rem 0.5rem;
    border-radius: 10px;
    
    &.high {
      background: rgba(255, 0, 255, 0.2);
      color: #ff00ff;
    }
    
    &.medium {
      background: rgba(0, 245, 255, 0.2);
      color: #00f5ff;
    }
    
    &.low {
      background: rgba(136, 146, 176, 0.2);
      color: #8892b0;
    }
  }
`;

const ParameterDescription = styled.p`
  font-family: "Inter", sans-serif;
  font-size: 0.8rem;
  color: #8892b0;
  margin: 0 0 1rem 0;
  line-height: 1.4;
`;

// 🎚️ 控件样式
const ControlInput = styled.input`
  width: 100%;
  padding: 0.5rem;
  background: rgba(22, 33, 62, 0.8);
  border: 1px solid rgba(0, 245, 255, 0.3);
  border-radius: 6px;
  color: #00f5ff;
  font-family: "Inter", sans-serif;
  font-size: 0.9rem;
  
  &:focus {
    outline: none;
    border-color: rgba(0, 245, 255, 0.6);
    box-shadow: 0 0 10px rgba(0, 245, 255, 0.2);
  }
  
  &[type="range"] {
    -webkit-appearance: none;
    height: 6px;
    background: rgba(22, 33, 62, 0.8);
    border-radius: 3px;
    
    &::-webkit-slider-thumb {
      -webkit-appearance: none;
      width: 18px;
      height: 18px;
      background: linear-gradient(45deg, #00f5ff, #ff00ff);
      border-radius: 50%;
      cursor: pointer;
      box-shadow: 0 0 10px rgba(0, 245, 255, 0.5);
    }
  }
`;

const ControlSelect = styled.select`
  width: 100%;
  padding: 0.5rem;
  background: rgba(22, 33, 62, 0.8);
  border: 1px solid rgba(0, 245, 255, 0.3);
  border-radius: 6px;
  color: #00f5ff;
  font-family: "Inter", sans-serif;
  font-size: 0.9rem;
  
  &:focus {
    outline: none;
    border-color: rgba(0, 245, 255, 0.6);
    box-shadow: 0 0 10px rgba(0, 245, 255, 0.2);
  }
  
  option {
    background: rgba(22, 33, 62, 0.95);
    color: #00f5ff;
  }
`;

const CheckboxContainer = styled.label`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  
  input[type="checkbox"] {
    width: 18px;
    height: 18px;
    accent-color: #00f5ff;
  }
  
  .checkbox-label {
    font-family: "Inter", sans-serif;
    color: #8892b0;
    font-size: 0.9rem;
  }
`;

// 🔬 优化方法选择器
const MethodSelector = styled.div`
  margin-bottom: 2rem;
`;

const MethodGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
`;

const MethodCard = styled(motion.div)<{ selected: boolean; complexity: string }>`
  background: ${props => props.selected ? 
    'rgba(0, 245, 255, 0.1)' : 
    'rgba(10, 10, 15, 0.8)'
  };
  border: 2px solid ${props => props.selected ? 
    '#00f5ff' : 
    'rgba(136, 146, 176, 0.2)'
  };
  border-radius: 8px;
  padding: 1rem;
  cursor: pointer;
  text-align: center;
  position: relative;
  
  &:hover {
    border-color: #00f5ff;
    box-shadow: 0 0 15px rgba(0, 245, 255, 0.2);
  }
  
  .icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
  }
  
  .name {
    font-family: "Inter", sans-serif;
    color: #00f5ff;
    font-weight: 600;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
  }
  
  .description {
    font-family: "Inter", sans-serif;
    color: #8892b0;
    font-size: 0.8rem;
    line-height: 1.3;
    margin-bottom: 0.5rem;
  }
  
  .complexity {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    font-size: 0.7rem;
    padding: 0.2rem 0.4rem;
    border-radius: 8px;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    
    &.simple {
      background: rgba(57, 255, 20, 0.2);
      color: #39ff14;
    }
    
    &.advanced {
      background: rgba(0, 245, 255, 0.2);
      color: #00f5ff;
    }
    
    &.expert {
      background: rgba(255, 0, 255, 0.2);
      color: #ff00ff;
    }
  }
  
  .time {
    font-family: "Inter", sans-serif;
    color: #8892b0;
    font-size: 0.7rem;
    margin-top: 0.5rem;
  }
`;

// ▶️ 控制按钮
const ControlButtons = styled.div`
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-bottom: 2rem;
`;

const ControlButton = styled(motion.button)<{ variant?: 'primary' | 'secondary' | 'danger' }>`
  padding: 0.75rem 1.5rem;
  border: 2px solid;
  border-color: ${props => 
    props.variant === 'primary' ? '#00f5ff' :
    props.variant === 'danger' ? '#ff073a' : '#8892b0'
  };
  background: transparent;
  color: ${props => 
    props.variant === 'primary' ? '#00f5ff' :
    props.variant === 'danger' ? '#ff073a' : '#8892b0'
  };
  border-radius: 25px;
  font-family: "Inter", sans-serif;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, 
      transparent, 
      ${props => 
        props.variant === 'primary' ? 'rgba(0, 245, 255, 0.2)' :
        props.variant === 'danger' ? 'rgba(255, 7, 58, 0.2)' : 'rgba(136, 146, 176, 0.2)'
      }, 
      transparent
    );
    transition: left 0.5s;
  }
  
  &:hover:not(:disabled)::before {
    left: 100%;
  }
  
  &:hover:not(:disabled) {
    box-shadow: 0 0 20px ${props => 
      props.variant === 'primary' ? 'rgba(0, 245, 255, 0.4)' :
      props.variant === 'danger' ? 'rgba(255, 7, 58, 0.4)' : 'rgba(136, 146, 176, 0.4)'
    };
  }
`;

// 📊 进度条
const ProgressContainer = styled.div`
  margin-bottom: 2rem;
`;

const ProgressBar = styled.div`
  width: 100%;
  height: 8px;
  background: rgba(10, 10, 15, 0.8);
  border-radius: 4px;
  overflow: hidden;
  position: relative;
`;

const ProgressFill = styled(motion.div)`
  height: 100%;
  background: linear-gradient(90deg, #00f5ff, #39ff14);
  border-radius: 4px;
`;

const ProgressText = styled.div`
  text-align: center;
  margin-top: 0.5rem;
  font-family: "Inter", sans-serif;
  color: #00f5ff;
  font-size: 0.9rem;
`;

// 🎯 主组件
const SmartParameterOptimizer: React.FC<SmartOptimizerProps> = ({
  parameters,
  onParametersChange,
  onOptimizationStart,
  onOptimizationStop,
  optimizationStatus,
  progress,
  results = []
}) => {
  const [selectedMethod, setSelectedMethod] = useState<string>('genetic');
  const [currentParams, setCurrentParams] = useState<Record<string, any>>({});

  // 🔬 优化方法配置
  const optimizationMethods: OptimizationMethod[] = [
    {
      id: 'genetic',
      name: 'Genetic Algorithm',
      description: 'Evolution-inspired optimization with crossover and mutation',
      icon: '🧬',
      complexity: 'advanced',
      estimatedTime: '5-15 min'
    },
    {
      id: 'bayesian',
      name: 'Bayesian Optimization',
      description: 'Intelligent sampling using prior knowledge',
      icon: '🎯',
      complexity: 'expert',
      estimatedTime: '10-30 min'
    },
    {
      id: 'grid',
      name: 'Grid Search',
      description: 'Exhaustive search across parameter space',
      icon: '🔬',
      complexity: 'simple',
      estimatedTime: '2-10 min'
    },
    {
      id: 'random',
      name: 'Random Search',
      description: 'Random sampling with smart bounds',
      icon: '🎲',
      complexity: 'simple',
      estimatedTime: '3-8 min'
    },
    {
      id: 'reinforcement',
      name: 'Reinforcement Learning',
      description: 'Agent-based parameter exploration',
      icon: '🤖',
      complexity: 'expert',
      estimatedTime: '15-45 min'
    },
    {
      id: 'differential',
      name: 'Differential Evolution',
      description: 'Population-based optimization with mutation',
      icon: '🔄',
      complexity: 'advanced',
      estimatedTime: '8-20 min'
    }
  ];

  // 📊 初始化参数值
  useEffect(() => {
    const initialParams = parameters.reduce((acc, param) => {
      acc[param.name] = param.current;
      return acc;
    }, {} as Record<string, any>);
    setCurrentParams(initialParams);
  }, [parameters]);

  // 🎛️ 参数值变更处理
  const handleParameterChange = useCallback((paramName: string, value: any) => {
    const newParams = { ...currentParams, [paramName]: value };
    setCurrentParams(newParams);
    onParametersChange(newParams);
  }, [currentParams, onParametersChange]);

  // 🚀 开始优化
  const handleStartOptimization = useCallback(() => {
    onOptimizationStart(selectedMethod, currentParams);
  }, [selectedMethod, currentParams, onOptimizationStart]);

  // 🎨 渲染参数控件
  const renderParameterControl = (param: Parameter) => {
    const value = currentParams[param.name] || param.current;

    switch (param.type) {
      case 'number':
        return (
          <ControlInput
            type="number"
            value={value}
            min={param.min}
            max={param.max}
            step={param.step || 1}
            onChange={(e) => handleParameterChange(param.name, parseFloat(e.target.value))}
          />
        );

      case 'range':
        return (
          <div>
            <ControlInput
              type="range"
              value={value}
              min={param.min}
              max={param.max}
              step={param.step || 1}
              onChange={(e) => handleParameterChange(param.name, parseFloat(e.target.value))}
            />
            <div style={{ 
              textAlign: 'center', 
              marginTop: '0.5rem', 
              color: '#00f5ff',
              fontSize: '0.8rem'
            }}>
              {value}
            </div>
          </div>
        );

      case 'select':
        return (
          <ControlSelect
            value={value}
            onChange={(e) => handleParameterChange(param.name, e.target.value)}
          >
            {param.options?.map(option => (
              <option key={option} value={option}>{option}</option>
            ))}
          </ControlSelect>
        );

      case 'boolean':
        return (
          <CheckboxContainer>
            <input
              type="checkbox"
              checked={value}
              onChange={(e) => handleParameterChange(param.name, e.target.checked)}
            />
            <span className="checkbox-label">Enable</span>
          </CheckboxContainer>
        );

      default:
        return <div>Unsupported parameter type</div>;
    }
  };

  return (
    <OptimizerContainer>
      <OptimizerHeader>
        <Title>🧬 Smart Parameter Optimizer</Title>
        <StatusIndicator 
          status={optimizationStatus}
          animate={{ scale: optimizationStatus === 'running' ? [1, 1.05, 1] : 1 }}
          transition={{ repeat: optimizationStatus === 'running' ? Infinity : 0, duration: 1 }}
        >
          {optimizationStatus === 'idle' && '⏸️ Ready'}
          {optimizationStatus === 'running' && '🔄 Optimizing'}
          {optimizationStatus === 'completed' && '✅ Complete'}
          {optimizationStatus === 'error' && '❌ Error'}
        </StatusIndicator>
      </OptimizerHeader>

      {/* 📊 参数网格 */}
      <div>
        <h4 style={{ 
          color: '#8892b0', 
          marginBottom: '1rem',
          fontFamily: 'Inter, sans-serif',
          fontSize: '0.9rem'
        }}>
          Strategy Parameters
        </h4>
        <ParameterGrid>
          {parameters.map((param, index) => (
            <ParameterCard
              key={param.name}
              impact={param.impact}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <ParameterLabel>
                <span className="name">{param.name}</span>
                <span className={`impact ${param.impact}`}>{param.impact}</span>
              </ParameterLabel>
              
              <ParameterDescription>{param.description}</ParameterDescription>
              
              {renderParameterControl(param)}
            </ParameterCard>
          ))}
        </ParameterGrid>
      </div>

      {/* 🔬 优化方法选择 */}
      <MethodSelector>
        <h4 style={{ 
          color: '#8892b0', 
          marginBottom: '1rem',
          fontFamily: 'Inter, sans-serif',
          fontSize: '0.9rem'
        }}>
          Optimization Method
        </h4>
        <MethodGrid>
          {optimizationMethods.map((method) => (
            <MethodCard
              key={method.id}
              selected={selectedMethod === method.id}
              complexity={method.complexity}
              onClick={() => setSelectedMethod(method.id)}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <div className="icon">{method.icon}</div>
              <div className="name">{method.name}</div>
              <div className="description">{method.description}</div>
              <div className={`complexity ${method.complexity}`}>
                {method.complexity}
              </div>
              <div className="time">Est. {method.estimatedTime}</div>
            </MethodCard>
          ))}
        </MethodGrid>
      </MethodSelector>

      {/* 📊 进度条 */}
      <AnimatePresence>
        {optimizationStatus === 'running' && (
          <ProgressContainer>
            <ProgressBar>
              <ProgressFill
                initial={{ width: 0 }}
                animate={{ width: `${progress}%` }}
                transition={{ duration: 0.3 }}
              />
            </ProgressBar>
            <ProgressText>
              Optimizing parameters... {progress.toFixed(1)}%
            </ProgressText>
          </ProgressContainer>
        )}
      </AnimatePresence>

      {/* ▶️ 控制按钮 */}
      <ControlButtons>
        <ControlButton
          variant="primary"
          disabled={optimizationStatus === 'running'}
          onClick={handleStartOptimization}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          {optimizationStatus === 'running' ? '🔄 Optimizing...' : '🚀 Start Optimization'}
        </ControlButton>
        
        {optimizationStatus === 'running' && (
          <ControlButton
            variant="danger"
            onClick={onOptimizationStop}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            ⏹️ Stop
          </ControlButton>
        )}
      </ControlButtons>

      {/* 📊 结果预览 */}
      {results.length > 0 && (
        <div>
          <h4 style={{ 
            color: '#8892b0', 
            marginBottom: '1rem',
            fontFamily: 'Inter, sans-serif',
            fontSize: '0.9rem'
          }}>
            Optimization Results ({results.length} configurations)
          </h4>
          <div style={{ 
            maxHeight: '200px', 
            overflowY: 'auto',
            background: 'rgba(10, 10, 15, 0.8)',
            border: '1px solid rgba(0, 245, 255, 0.2)',
            borderRadius: '8px',
            padding: '1rem'
          }}>
            {results.slice(0, 5).map((result, index) => (
              <div key={index} style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                padding: '0.5rem',
                borderBottom: index < 4 ? '1px solid rgba(0, 245, 255, 0.1)' : 'none',
                fontFamily: 'Inter, sans-serif',
                fontSize: '0.8rem'
              }}>
                <span style={{ color: '#8892b0' }}>
                  Config #{index + 1}
                </span>
                <div style={{ display: 'flex', gap: '1rem' }}>
                  <span style={{ color: result.performance.metrics.return > 0 ? '#39ff14' : '#ff073a' }}>
                    Return: {result.performance.metrics.return.toFixed(2)}%
                  </span>
                  <span style={{ color: '#00f5ff' }}>
                    Score: {result.performance.score.toFixed(2)}
                  </span>
                  <span style={{ color: '#ff00ff' }}>
                    Confidence: {(result.confidence * 100).toFixed(0)}%
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </OptimizerContainer>
  );
};

export default SmartParameterOptimizer;