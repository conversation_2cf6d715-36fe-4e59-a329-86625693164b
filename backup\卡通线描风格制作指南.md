# 🎨 QuantCard 卡通线描风格制作指南

## 🎯 设计理念

### 核心风格特征
- **清晰轮廓**: 所有模型都有明显的黑色描边
- **纯色块**: 使用高饱和度纯色，避免复杂渐变
- **简化光照**: 3级卡通光照 (阴影-中间调-高光)
- **几何简化**: 突出主要特征，简化细节

### 🎭 视觉统一标准
```
描边规格:
- 地标建筑: 0.02单位厚度
- 装饰树木: 0.015单位厚度  
- 车辆模型: 0.018单位厚度
- 描边颜色: 纯黑色 #000000

材质规格:
- 饱和度: >80%
- 明度: 40-80%
- 对比度: 高对比，避免相近色
- 透明度: 0.95 (微透明增加层次)
```

## 🏗️ 建筑模型制作规范

### 📐 尺寸和面数标准
```
地标建筑:
- 高度: 1.0-2.0单位
- 底面: 0.6-1.2单位直径
- 面数: 200-500三角面
- 细分: 8-16段 (圆形几何体)

装饰建筑:
- 高度: 0.3-0.8单位
- 底面: 0.2-0.6单位直径
- 面数: 50-150三角面
- 细分: 6-12段
```

### 🎨 建筑风格分类

#### 🏛️ 古典建筑 (博物馆、政府)
```
特征:
- 圆柱支撑结构
- 三角形山墙
- 对称设计
- 石材质感

色彩方案:
- 主体: 米白色 #f5f5dc
- 屋顶: 红棕色 #d2691e
- 装饰: 金色 #ffd700

Blender制作要点:
- 使用圆柱体 + 立方体组合
- 保持垂直线条清晰
- 添加台阶和柱子细节
```

#### 🏢 现代建筑 (交易所、办公楼)
```
特征:
- 垂直线条设计
- 玻璃幕墙效果
- 简洁几何形状
- 科技感外观

色彩方案:
- 主体: 科技蓝 #4a90e2
- 玻璃: 浅蓝色 #87ceeb
- 装饰: 白色 #ffffff

Blender制作要点:
- 使用立方体拉伸
- 添加楼层分割线
- 保持边缘锐利
```

#### 🗼 科技建筑 (通讯塔、实验室)
```
特征:
- 细长垂直结构
- 金属质感
- 环形装饰元素
- 发光细节

色彩方案:
- 主体: 橙金色 #ffa500
- 装饰: 红色 #ff6b35
- 发光: 青色 #00ffff

Blender制作要点:
- 使用圆锥体 + 环形
- 添加天线和装饰
- 保持细长比例
```

## 🌳 自然装饰模型

### 🌲 树木模型制作
```
设计要求:
- 简化的树冠形状 (球体或圆锥)
- 清晰的树干轮廓
- 卡通化叶片纹理
- 统一的绿色系配色

面数控制:
- 树冠: 50-80面 (低多边形球体)
- 树干: 16-24面 (圆柱体)
- 总计: <100面

色彩方案:
- 叶片: 森林绿 #228b22
- 树干: 棕色 #8b4513
- 高光: 浅绿 #90ee90
```

### 🌸 花草装饰
```
设计要求:
- 极简几何形状
- 明亮的色彩
- 小尺寸 (0.1-0.3单位)
- 群组放置

制作方法:
- 花朵: 平面 + 十字交叉
- 草地: 简单平面 + 绿色材质
- 灌木: 小球体 + 绿色
```

## 🚗 车辆装饰模型

### 🚙 简化车辆设计
```
基础结构:
- 车身: 圆角立方体
- 车轮: 圆柱体 (4个)
- 车窗: 深色立方体
- 总面数: <80面

色彩方案:
- 车身: 鲜艳色彩 (红、蓝、黄)
- 车轮: 深灰色 #333333
- 车窗: 深蓝色 #191970

动画效果:
- 车轮缓慢旋转
- 车身轻微摇摆
- 可选的移动路径
```

## 🛠️ Blender制作工作流

### 1. 项目设置
```
新建项目:
1. 删除默认立方体
2. 设置单位为米 (Scene Properties → Units)
3. 启用背面剔除 (Viewport Shading → Face Orientation)
4. 设置网格捕捉 (Snap to Grid)
```

### 2. 建模技巧
```
卡通风格建模:
1. 使用基础几何体开始
2. 保持边缘锐利 (避免过度细分)
3. 使用Loop Cut添加细节线条
4. 保持面数在目标范围内
5. 确保法线方向正确
```

### 3. 材质设置
```
卡通材质配置:
1. 添加材质节点
2. 使用Principled BSDF
3. 设置纯色Base Color
4. Roughness: 0.8 (建筑) / 0.9 (自然)
5. Metallic: 0.1 (建筑) / 0.0 (自然)
6. 不使用复杂纹理
```

### 4. 导出设置
```
GLB导出配置:
✅ Selected Objects Only
✅ Apply Modifiers
✅ +Y Up
✅ Materials: Export
❌ Cameras
❌ Lights  
❌ Animations (暂时不需要)

优化选项:
✅ Draco Compression (减小文件)
✅ Quantize Positions
```

## 📁 资源文件组织

### 目录结构
```
public/models/
├── landmarks/           # 可交互地标
│   ├── museum.glb
│   ├── exchange.glb
│   ├── tower.glb
│   ├── center.glb
│   └── adventure.glb
├── nature/             # 装饰性自然元素
│   ├── oak_tree.glb
│   ├── pine_tree.glb
│   ├── bush.glb
│   └── flowers.glb
├── vehicles/           # 装饰性车辆
│   ├── simple_car.glb
│   ├── truck.glb
│   └── bike.glb
└── props/              # 其他装饰道具
    ├── bench.glb
    ├── lamp.glb
    └── fountain.glb
```

### 文件命名规范
```
命名格式: {category}_{type}_{variant}.glb

示例:
- landmark_museum_classical.glb
- nature_tree_oak.glb  
- vehicle_car_simple.glb
- prop_bench_wooden.glb
```

## 🎯 制作优先级

### 第一批 (核心地标)
1. **museum.glb** - 策略博物馆
2. **exchange.glb** - 全球交易所
3. **tower.glb** - 宏观分析塔
4. **center.glb** - 策略控制中心
5. **adventure.glb** - 冒险地点

### 第二批 (基础装饰)
1. **oak_tree.glb** - 橡树
2. **simple_car.glb** - 简单汽车
3. **bench.glb** - 长椅
4. **lamp.glb** - 路灯

### 第三批 (丰富装饰)
1. 更多树木变种
2. 不同类型车辆
3. 城市道具
4. 特效装饰

## 🔧 技术实现要点

### 卡通材质系统
```typescript
// 统一的卡通线描材质
const createUnifiedToonMaterial = (color: string, category: ModelCategory) => {
  return new THREE.MeshToonMaterial({
    color: new THREE.Color(color),
    transparent: true,
    opacity: 0.95,
    gradientMap: toonGradientTexture  // 3级光照渐变
  });
};

// 统一的描边系统
<Outlines 
  thickness={getOutlineThickness(category)}
  color="black" 
  transparent 
  opacity={0.8}
/>
```

### 性能优化策略
```typescript
// 装饰物LOD系统
const useDecorationLOD = (distance: number) => {
  if (distance > 15) return 'hidden';    // 不显示
  if (distance > 10) return 'low';       // 低细节
  return 'normal';                       // 正常细节
};

// 批量实例化 (相同装饰物)
<InstancedMesh args={[geometry, material, count]} />
```

## 🎉 预期最终效果

### 视觉风格
- **🌍 弧形地球**: 自然的地平线弧度
- **🏗️ 特色地标**: 每个都有独特的卡通建筑风格
- **🌳 生动装饰**: 树木、车辆增加世界的生活感
- **🖼️ 统一描边**: 所有元素都有清晰的黑色轮廓

### 技术特点
- **⚡ 高性能**: 总面数控制在2000以内
- **📱 移动友好**: 支持装饰物动态显隐
- **🔧 可扩展**: 轻松添加新的模型类型
- **🎛️ 可控制**: 用户可以调整显示内容

---

*🎨 这个指南确保了所有自定义模型都符合统一的卡通线描风格，同时保持优秀的性能表现！*
