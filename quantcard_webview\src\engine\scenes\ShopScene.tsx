/**
 * 🛒 商店场景 - 移动端优先设�? * 浅色主题，支持卡包购买、单卡购买、稀有度展示
 */

import React, { useState, useMemo } from 'react'
import styled from 'styled-components'
import { motion, AnimatePresence } from 'framer-motion'
import { useGameState, useCardsState } from '../../store/hooks'
import { useTheme } from '../../styles/themes/ThemeProvider'

// 📱 移动端主容器
const ShopContainer = styled.div<{ theme: any }>`
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background: ${props => props.theme.colors.gradients.primary};
  overflow: hidden;
`

// 🎯 顶部状态栏
const ShopHeader = styled.div<{ theme: any }>`
  background: ${props => props.theme.colors.background.card};
  backdrop-filter: blur(15px);
  border-bottom: 1px solid ${props => props.theme.colors.border.primary};
  padding: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
  box-shadow: ${props => props.theme.shadows.medium};
`

const HeaderTitle = styled.h1<{ theme: any }>`
  color: ${props => props.theme.colors.text.accent};
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-family: ${props => props.theme.typography.fonts.primary};
  
  &::before {
    content: '🛒';
    font-size: 2rem;
  }
`

const CurrencyDisplay = styled.div`
  display: flex;
  gap: 1rem;
  align-items: center;
`

const CurrencyItem = styled.div<{ $type: 'coins' | 'gems' }>`
  background: ${props => props.$type === 'coins' 
    ? 'rgba(245, 158, 11, 0.2)' 
    : 'rgba(139, 92, 246, 0.2)'
  };
  border: 1px solid ${props => props.$type === 'coins' 
    ? '#f59e0b' 
    : '#8b5cf6'
  };
  color: ${props => props.$type === 'coins' 
    ? '#f59e0b' 
    : '#8b5cf6'
  };
  padding: 0.5rem 0.75rem;
  border-radius: 20px;
  font-weight: 600;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  
  &::before {
    content: ${props => props.$type === 'coins' ? "'💰'" : "'💎'"};
  }
`

// 📦 主要内容区域
const ShopContent = styled.div<{ theme: any }>`
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  
  &::-webkit-scrollbar { width: 4px; }
  &::-webkit-scrollbar-track { background: ${props => props.theme.colors.background.secondary}; }
  &::-webkit-scrollbar-thumb { background: ${props => props.theme.colors.primaryColors.cyan}; border-radius: 2px; }
`

// 🔖 分类标签
const CategoryTabs = styled.div`
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
  overflow-x: auto;
  padding-bottom: 0.5rem;
  
  &::-webkit-scrollbar {
    display: none;
  }
`

const CategoryTab = styled(motion.button)<{ $active: boolean, theme: any }>`
  background: ${props => props.$active 
    ? props.theme.colors.primaryColors.cyan 
    : props.theme.colors.background.card
  };
  border: 1px solid ${props => props.$active 
    ? props.theme.colors.primaryColors.cyan 
    : props.theme.colors.border.primary
  };
  color: ${props => props.$active 
    ? '#ffffff' 
    : props.theme.colors.text.primary
  };
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  font-weight: 600;
  white-space: nowrap;
  cursor: pointer;
  transition: ${props => props.theme.animations.transition};
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: ${props => props.theme.shadows.medium};
  }
`

// 📦 卡包区域
const PackSection = styled.div`
  margin-bottom: 2rem;
`

const SectionTitle = styled.h2<{ theme: any }>`
  color: ${props => props.theme.colors.text.primary};
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
  padding-left: 0.5rem;
  font-family: ${props => props.theme.typography.fonts.primary};
`

const PackGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1rem;
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
`

// 🎁 卡包卡片
const PackCard = styled(motion.div)<{ $featured?: boolean, theme: any }>`
  background: ${props => props.theme.colors.background.card};
  border: 1px solid ${props => props.$featured 
    ? props.theme.colors.rarity.legendary 
    : props.theme.colors.border.primary
  };
  border-radius: 16px;
  padding: 1.5rem;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
  
  ${props => props.$featured && `
    box-shadow: 0 0 20px ${props.theme.colors.rarity.legendary}40;
  `}
  
  &::before {
    content: '';
    position: absolute;
    top: 0; left: 0; right: 0; bottom: 0;
    background: ${props => props.$featured 
      ? `linear-gradient(135deg, ${props.theme.colors.rarity.legendary}10, transparent)`
      : 'transparent'
    };
    pointer-events: none;
  }
`

const PackIcon = styled.div`
  font-size: 3rem;
  text-align: center;
  margin-bottom: 1rem;
`

const PackName = styled.h3<{ theme: any }>`
  color: ${props => props.theme.colors.text.primary};
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  text-align: center;
`

const PackDescription = styled.p<{ theme: any }>`
  color: ${props => props.theme.colors.text.secondary};
  font-size: 0.9rem;
  text-align: center;
  margin-bottom: 1rem;
  line-height: 1.4;
`

const PackContents = styled.div`
  margin-bottom: 1rem;
`

const RarityBadge = styled.div<{ $rarity: string, theme: any }>`
  display: inline-block;
  background: ${props => props.theme.colors.rarity[props.$rarity as keyof typeof props.theme.colors.rarity]}20;
  border: 1px solid ${props => props.theme.colors.rarity[props.$rarity as keyof typeof props.theme.colors.rarity]};
  color: ${props => props.theme.colors.rarity[props.$rarity as keyof typeof props.theme.colors.rarity]};
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
  margin: 0.25rem;
`

const PackPrice = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 1rem;
`

const PriceTag = styled.div<{ $currency: 'coins' | 'gems' }>`
  color: ${props => props.$currency === 'coins' ? '#f59e0b' : '#8b5cf6'};
  font-weight: 700;
  font-size: 1.1rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  
  &::before {
    content: ${props => props.$currency === 'coins' ? "'💰'" : "'💎'"};
  }
`

const PurchaseButton = styled(motion.button)<{ $disabled?: boolean }>`
  background: ${props => props.$disabled 
    ? props.theme.colors.border.secondary 
    : props.theme.colors.primary
  };
  border: none;
  color: ${props => props.$disabled 
    ? props.theme.colors.text.muted 
    : '#ffffff'
  };
  padding: 0.75rem 1.5rem;
  border-radius: 12px;
  font-weight: 600;
  cursor: ${props => props.$disabled ? 'not-allowed' : 'pointer'};
  transition: ${props => props.theme.animations.transition};
  
  &:hover {
    ${props => !props.$disabled && `
      transform: translateY(-2px);
      box-shadow: ${props.theme.shadows.medium};
    `}
  }
`

// 🃏 单卡展示区域
const SingleCardsSection = styled.div`
  margin-bottom: 2rem;
`

const CardsHorizontalScroll = styled.div`
  display: flex;
  gap: 1rem;
  overflow-x: auto;
  padding-bottom: 1rem;
  
  &::-webkit-scrollbar {
    height: 4px;
  }
  
  &::-webkit-scrollbar-track {
    background: ${props => props.theme.colors.background.secondary};
  }
  
  &::-webkit-scrollbar-thumb {
    background: ${props => props.theme.colors.primary};
    border-radius: 2px;
  }
`

const SingleCard = styled(motion.div)<{ $rarity: string, theme: any }>`
  min-width: 200px;
  background: ${props => props.theme.colors.background.card};
  border: 2px solid ${props => props.theme.colors.rarity[props.$rarity as keyof typeof props.theme.colors.rarity]};
  border-radius: 12px;
  padding: 1rem;
  cursor: pointer;
  position: relative;
  
  box-shadow: 0 0 15px ${props => props.theme.colors.rarity[props.$rarity as keyof typeof props.theme.colors.rarity]}40;
`

// 📱 底部导航
const BottomNavigation = styled.div<{ theme: any }>`
  background: ${props => props.theme.colors.background.card};
  backdrop-filter: blur(15px);
  border-top: 1px solid ${props => props.theme.colors.border.primary};
  padding: 1rem;
  display: flex;
  justify-content: space-around;
  flex-shrink: 0;
`

const NavButton = styled(motion.button)<{ $active?: boolean, theme: any }>`
  background: ${props => props.$active 
    ? props.theme.colors.primaryColors.cyan 
    : 'transparent'
  };
  border: 1px solid ${props => props.$active 
    ? props.theme.colors.primaryColors.cyan 
    : props.theme.colors.border.primary
  };
  color: ${props => props.$active 
    ? '#ffffff' 
    : props.theme.colors.text.primary
  };
  padding: 0.75rem 1rem;
  border-radius: 12px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
  min-width: 80px;
  
  .icon { font-size: 1.2rem; }
  .label { font-size: 0.8rem; }
`

// 📦 卡包数据
const cardPacks = [
  {
    id: 'starter_pack',
    name: '新手礼包',
    description: '包含基础策略卡，适合新手入门',
    icon: '🎁',
    price: { coins: 500, gems: 0 },
    contents: ['common', 'common', 'common', 'rare'],
    featured: false
  },
  {
    id: 'premium_pack',
    name: '高级卡包',
    description: '更高概率获得稀有卡牌',
    icon: '💎',
    price: { coins: 0, gems: 10 },
    contents: ['rare', 'rare', 'epic', 'legendary'],
    featured: true
  },
  {
    id: 'mega_pack',
    name: '超级卡包',
    description: '保底传说级卡牌，限时优惠',
    icon: '💎',
    price: { coins: 0, gems: 25 },
    contents: ['epic', 'epic', 'legendary', 'mythic'],
    featured: true
  }
]

// 🃏 单卡数据
const featuredCards = [
  {
    id: 'ai_master',
    name: 'AI大师策略',
    rarity: 'legendary',
    price: { coins: 0, gems: 15 },
    description: '使用人工智能进行交易决策'
  },
  {
    id: 'quantum_analysis',
    name: '量子分析',
    rarity: 'mythic',
    price: { coins: 0, gems: 30 },
    description: '终极分析策略，市场洞察力MAX'
  }
]

// 🛒 商店场景组件
interface ShopSceneProps {
  sceneData?: any
}

export default function ShopScene({ sceneData }: ShopSceneProps) {
  const { switchTheme } = useTheme()
  const { currency, updateCurrency } = useGameState()
  const { addCards } = useCardsState()
  
  const [activeCategory, setActiveCategory] = useState<'packs' | 'singles' | 'special'>('packs')
  const [purchaseLoading, setPurchaseLoading] = useState<string | null>(null)

  // 🎨 切换到浅色主题
  React.useEffect(() => {
    switchTheme('Shop')
  }, [switchTheme])

  // 💰 处理购买卡包
  const handlePurchasePack = async (pack: typeof cardPacks[0]) => {
    if (pack.price.coins > currency.coins || pack.price.gems > currency.gems) {
      alert('余额不足')
      return
    }

    setPurchaseLoading(pack.id)

    try {
      // 扣除货币
      if (pack.price.coins > 0) {
        updateCurrency('coins', -pack.price.coins)
      }
      if (pack.price.gems > 0) {
        updateCurrency('gems', -pack.price.gems)
      }

      // 模拟开包过程
      await new Promise(resolve => setTimeout(resolve, 1500))

      // 添加卡牌到背包
      for (const rarity of pack.contents) {
        const cardId = `${rarity}_card_${Date.now()}_${Math.random()}`
        await addCards(cardId, 1, 'purchase')
      }

      alert(`🎉 成功购买 ${pack.name}！`)
    } catch (error) {
      alert('购买失败，请重试')
    } finally {
      setPurchaseLoading(null)
    }
  }

  // 🃏 处理购买单卡
  const handlePurchaseCard = async (card: typeof featuredCards[0]) => {
    if (card.price.coins > currency.coins || card.price.gems > currency.gems) {
      alert('余额不足')
      return
    }

    setPurchaseLoading(card.id)

    try {
      if (card.price.coins > 0) {
        updateCurrency('coins', -card.price.coins)
      }
      if (card.price.gems > 0) {
        updateCurrency('gems', -card.price.gems)
      }

      await new Promise(resolve => setTimeout(resolve, 800))
      await addCards(card.id, 1, 'purchase')

      alert(`🎉 成功购买 ${card.name}！`)
    } catch (error) {
      alert('购买失败，请重试')
    } finally {
      setPurchaseLoading(null)
    }
  }

  return (
    <ShopContainer>
      {/* 🎯 顶部状态栏 */}
      <ShopHeader>
        <HeaderTitle>策略商店</HeaderTitle>
        <CurrencyDisplay>
          <CurrencyItem $type="coins">{currency.coins}</CurrencyItem>
          <CurrencyItem $type="gems">{currency.gems}</CurrencyItem>
        </CurrencyDisplay>
      </ShopHeader>

      {/* 📦 主要内容 */}
      <ShopContent>
        {/* 🔖 分类标签 */}
        <CategoryTabs>
          <CategoryTab
            $active={activeCategory === 'packs'}
            onClick={() => setActiveCategory('packs')}
            whileTap={{ scale: 0.95 }}
          >
            卡包商店
          </CategoryTab>
          <CategoryTab
            $active={activeCategory === 'singles'}
            onClick={() => setActiveCategory('singles')}
            whileTap={{ scale: 0.95 }}
          >
            精选单卡
          </CategoryTab>
          <CategoryTab
            $active={activeCategory === 'special'}
            onClick={() => setActiveCategory('special')}
            whileTap={{ scale: 0.95 }}
          >
            限时特惠
          </CategoryTab>
        </CategoryTabs>

        {/* 📦 卡包区域 */}
        <AnimatePresence mode="wait">
          {activeCategory === 'packs' && (
            <motion.div
              key="packs"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
            >
              <PackSection>
                <SectionTitle>🎁 卡包购买</SectionTitle>
                <PackGrid>
                  {cardPacks.map((pack) => (
                    <PackCard
                      key={pack.id}
                      $featured={pack.featured}
                      whileHover={{ y: -4 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <PackIcon>{pack.icon}</PackIcon>
                      <PackName>{pack.name}</PackName>
                      <PackDescription>{pack.description}</PackDescription>
                      
                      <PackContents>
                        {pack.contents.map((rarity, index) => (
                          <RarityBadge key={index} $rarity={rarity}>
                            {rarity}
                          </RarityBadge>
                        ))}
                      </PackContents>
                      
                      <PackPrice>
                        <PriceTag $currency={pack.price.coins > 0 ? 'coins' : 'gems'}>
                          {pack.price.coins > 0 ? pack.price.coins : pack.price.gems}
                        </PriceTag>
                        
                        <PurchaseButton
                          $disabled={
                            pack.price.coins > currency.coins || 
                            pack.price.gems > currency.gems ||
                            purchaseLoading === pack.id
                          }
                          onClick={() => handlePurchasePack(pack)}
                          whileTap={{ scale: 0.95 }}
                        >
                          {purchaseLoading === pack.id ? '购买中...' : '购买'}
                        </PurchaseButton>
                      </PackPrice>
                    </PackCard>
                  ))}
                </PackGrid>
              </PackSection>
            </motion.div>
          )}

          {/* 🃏 单卡区域 */}
          {activeCategory === 'singles' && (
            <motion.div
              key="singles"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
            >
              <SingleCardsSection>
                <SectionTitle>🔥 精选策略卡</SectionTitle>
                <CardsHorizontalScroll>
                  {featuredCards.map((card) => (
                    <SingleCard
                      key={card.id}
                      $rarity={card.rarity}
                      whileHover={{ y: -4 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <h4 style={{ margin: '0 0 0.5rem 0', color: 'inherit' }}>
                        {card.name}
                      </h4>
                      <RarityBadge $rarity={card.rarity}>
                        {card.rarity}
                      </RarityBadge>
                      <p style={{ 
                        fontSize: '0.9rem', 
                        color: 'inherit', 
                        opacity: 0.8,
                        margin: '0.5rem 0 1rem 0'
                      }}>
                        {card.description}
                      </p>
                      
                      <PackPrice>
                        <PriceTag $currency={card.price.coins > 0 ? 'coins' : 'gems'}>
                          {card.price.coins > 0 ? card.price.coins : card.price.gems}
                        </PriceTag>
                        
                        <PurchaseButton
                          $disabled={
                            card.price.coins > currency.coins || 
                            card.price.gems > currency.gems ||
                            purchaseLoading === card.id
                          }
                          onClick={() => handlePurchaseCard(card)}
                          whileTap={{ scale: 0.95 }}
                        >
                          {purchaseLoading === card.id ? '购买中...' : '购买'}
                        </PurchaseButton>
                      </PackPrice>
                    </SingleCard>
                  ))}
                </CardsHorizontalScroll>
              </SingleCardsSection>
            </motion.div>
          )}
        </AnimatePresence>
      </ShopContent>

      {/* 📱 底部导航 */}
      <BottomNavigation>
        <NavButton
          whileTap={{ scale: 0.95 }}
          onClick={() => window.history.back()}
        >
          <div className="icon">🌍</div>
          <div className="label">地图</div>
        </NavButton>
        
        <NavButton $active>
          <div className="icon">🛒</div>
          <div className="label">商店</div>
        </NavButton>
        
        <NavButton>
          <div className="icon">🎒</div>
          <div className="label">背包</div>
        </NavButton>
        
        <NavButton>
          <div className="icon">👤</div>
          <div className="label">个人</div>
        </NavButton>
      </BottomNavigation>
    </ShopContainer>
  )
}
