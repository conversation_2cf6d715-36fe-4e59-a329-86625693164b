/**
 * 🌌 3D性能图表组件 - Performance Chart 3D
 * 使用Three.js实现的高级数据可视化
 */

import React, { useRef, useEffect, useState, useMemo } from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import * as THREE from 'three';

// 📊 数据类型定义
interface PerformanceData {
  timestamp: Date[];
  equity: number[];
  returns: number[];
  drawdown: number[];
  volatility: number[];
}

interface Chart3DProps {
  data: PerformanceData;
  width?: number;
  height?: number;
  theme?: 'cyberpunk' | 'neon' | 'holographic';
  interactive?: boolean;
  showGrid?: boolean;
  showAxes?: boolean;
}

// 🎨 容器样式
const ChartContainer = styled(motion.div)<{ width: number; height: number }>`
  width: ${props => props.width}px;
  height: ${props => props.height}px;
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  background: linear-gradient(135deg, 
    rgba(10, 10, 15, 0.95) 0%, 
    rgba(22, 33, 62, 0.95) 50%,
    rgba(16, 21, 46, 0.95) 100%
  );
  border: 1px solid rgba(0, 245, 255, 0.2);
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, 
      rgba(0, 245, 255, 0.8),
      rgba(255, 0, 255, 0.8),
      rgba(57, 255, 20, 0.8)
    );
    z-index: 1;
  }
`;

// 🎮 控制面板
const ControlPanel = styled.div`
  position: absolute;
  top: 1rem;
  right: 1rem;
  z-index: 10;
  display: flex;
  gap: 0.5rem;
  flex-direction: column;
`;

const ControlButton = styled(motion.button)<{ active?: boolean }>`
  width: 40px;
  height: 40px;
  border: 2px solid ${props => props.active ? '#00f5ff' : 'rgba(136, 146, 176, 0.5)'};
  background: ${props => props.active ? 'rgba(0, 245, 255, 0.1)' : 'rgba(22, 33, 62, 0.8)'};
  color: ${props => props.active ? '#00f5ff' : '#8892b0'};
  border-radius: 8px;
  cursor: pointer;
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.1rem;
  
  &:hover {
    border-color: #00f5ff;
    background: rgba(0, 245, 255, 0.1);
    color: #00f5ff;
    box-shadow: 0 0 15px rgba(0, 245, 255, 0.3);
  }
`;

// 📈 信息面板
const InfoPanel = styled.div`
  position: absolute;
  bottom: 1rem;
  left: 1rem;
  z-index: 10;
  background: rgba(22, 33, 62, 0.9);
  border: 1px solid rgba(0, 245, 255, 0.2);
  border-radius: 8px;
  padding: 1rem;
  backdrop-filter: blur(10px);
  min-width: 200px;
`;

const InfoItem = styled.div`
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
  font-family: "Arial", sans-serif;
  font-size: 0.85rem;
  
  .label {
    color: #8892b0;
  }
  
  .value {
    color: #00f5ff;
    font-weight: 600;
  }
`;

// 🎯 主组件
const PerformanceChart3D: React.FC<Chart3DProps> = ({
  data,
  width = 800,
  height = 500,
  theme = 'cyberpunk',
  interactive = true,
  showGrid = true,
  showAxes = true
}) => {
  const mountRef = useRef<HTMLDivElement>(null);
  const sceneRef = useRef<THREE.Scene | null>(null);
  const rendererRef = useRef<THREE.WebGLRenderer | null>(null);
  const cameraRef = useRef<THREE.PerspectiveCamera | null>(null);
  const animationRef = useRef<number | null>(null);
  
  const [isRotating, setIsRotating] = useState(true);
  const [showWireframe, setShowWireframe] = useState(false);
  const [selectedPoint, setSelectedPoint] = useState<{
    x: number;
    y: number;
    z: number;
    data: any;
  } | null>(null);

  // 🎨 主题配置
  const themeConfig = useMemo(() => {
    const themes = {
      cyberpunk: {
        primary: new THREE.Color(0x00f5ff),
        secondary: new THREE.Color(0xff00ff),
        accent: new THREE.Color(0x39ff14),
        background: new THREE.Color(0x0a0a0f),
        grid: new THREE.Color(0x00f5ff),
        lineOpacity: 0.8,
        pointSize: 3
      },
      neon: {
        primary: new THREE.Color(0xff0080),
        secondary: new THREE.Color(0x80ff00),
        accent: new THREE.Color(0x0080ff),
        background: new THREE.Color(0x000008),
        grid: new THREE.Color(0xff0080),
        lineOpacity: 0.9,
        pointSize: 4
      },
      holographic: {
        primary: new THREE.Color(0x40e0d0),
        secondary: new THREE.Color(0xda70d6),
        accent: new THREE.Color(0xffd700),
        background: new THREE.Color(0x0f0f23),
        grid: new THREE.Color(0x40e0d0),
        lineOpacity: 0.7,
        pointSize: 2
      }
    };
    return themes[theme];
  }, [theme]);

  // 🏗️ 场景初始化
  useEffect(() => {
    if (!mountRef.current) return;

    // 创建场景
    const scene = new THREE.Scene();
    scene.background = themeConfig.background;
    sceneRef.current = scene;

    // 创建相机
    const camera = new THREE.PerspectiveCamera(
      75,
      width / height,
      0.1,
      1000
    );
    camera.position.set(15, 10, 15);
    cameraRef.current = camera;

    // 创建渲染器
    const renderer = new THREE.WebGLRenderer({
      antialias: true,
      alpha: true,
      powerPreference: "high-performance"
    });
    renderer.setSize(width, height);
    renderer.setPixelRatio(window.devicePixelRatio);
    renderer.shadowMap.enabled = true;
    renderer.shadowMap.type = THREE.PCFSoftShadowMap;
    rendererRef.current = renderer;

    mountRef.current.appendChild(renderer.domElement);

    // 添加灯光
    const ambientLight = new THREE.AmbientLight(themeConfig.primary, 0.4);
    scene.add(ambientLight);

    const directionalLight = new THREE.DirectionalLight(themeConfig.primary, 0.8);
    directionalLight.position.set(10, 10, 5);
    directionalLight.castShadow = true;
    scene.add(directionalLight);

    const pointLight = new THREE.PointLight(themeConfig.secondary, 0.5, 50);
    pointLight.position.set(-10, 10, -10);
    scene.add(pointLight);

    return () => {
      if (mountRef.current && renderer.domElement) {
        mountRef.current.removeChild(renderer.domElement);
      }
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
      renderer.dispose();
    };
  }, [width, height, themeConfig]);

  // 📊 创建图表几何体
  useEffect(() => {
    if (!sceneRef.current || !cameraRef.current || !rendererRef.current) return;

    const scene = sceneRef.current;
    const camera = cameraRef.current;
    const renderer = rendererRef.current;

    // 清除之前的图表
    const chartsGroup = scene.getObjectByName('charts');
    if (chartsGroup) {
      scene.remove(chartsGroup);
    }

    const group = new THREE.Group();
    group.name = 'charts';

    // 🌐 创建网格
    if (showGrid) {
      const gridSize = 20;
      const gridHelper = new THREE.GridHelper(
        gridSize,
        20,
        themeConfig.grid,
        new THREE.Color(themeConfig.grid).multiplyScalar(0.3)
      );
      gridHelper.position.y = 0;
      group.add(gridHelper);

      // 垂直网格
      const verticalGrid = new THREE.GridHelper(
        gridSize,
        20,
        themeConfig.grid,
        new THREE.Color(themeConfig.grid).multiplyScalar(0.3)
      );
      verticalGrid.rotateX(Math.PI / 2);
      verticalGrid.position.z = 0;
      group.add(verticalGrid);
    }

    // 📏 创建坐标轴
    if (showAxes) {
      const axesHelper = new THREE.AxesHelper(15);
      axesHelper.setColors(
        themeConfig.primary,  // X - 时间
        themeConfig.secondary, // Y - 收益
        themeConfig.accent    // Z - 风险
      );
      group.add(axesHelper);
    }

    // 📈 创建收益曲线
    if (data.timestamp.length > 1) {
      const points: THREE.Vector3[] = [];
      const colors: THREE.Color[] = [];

      data.timestamp.forEach((time, index) => {
        const x = (index / (data.timestamp.length - 1)) * 20 - 10; // 时间
        const y = (data.equity[index] / Math.max(...data.equity)) * 10; // 收益
        const z = (data.volatility[index] / Math.max(...data.volatility)) * 8 - 4; // 波动率轴
        
        points.push(new THREE.Vector3(x, y, z));
        
        // 根据收益率着色
        const returnRate = data.returns[index];
        if (returnRate > 0) {
          colors.push(themeConfig.accent.clone()); // 绿色 - 盈利
        } else if (returnRate < 0) {
          colors.push(new THREE.Color(0xff073a)); // 红色 - 亏损
        } else {
          colors.push(themeConfig.primary.clone()); // 蓝色 - 持平
        }
      });

      // 创建曲线几何
      const geometry = new THREE.BufferGeometry().setFromPoints(points);
      const colorArray = new Float32Array(colors.length * 3);
      colors.forEach((color, index) => {
        color.toArray(colorArray, index * 3);
      });
      geometry.setAttribute('color', new THREE.BufferAttribute(colorArray, 3));

      // 创建线条材质
      const lineMaterial = new THREE.LineBasicMaterial({
        vertexColors: true,
        linewidth: 3,
        transparent: true,
        opacity: themeConfig.lineOpacity
      });

      const line = new THREE.Line(geometry, lineMaterial);
      group.add(line);

      // 🔴 创建数据点
      const pointGeometry = new THREE.SphereGeometry(0.1, 8, 6);
      points.forEach((point, index) => {
        const pointMaterial = new THREE.MeshBasicMaterial({
          color: colors[index],
          transparent: true,
          opacity: 0.8
        });
        
        const sphere = new THREE.Mesh(pointGeometry, pointMaterial);
        sphere.position.copy(point);
        sphere.scale.setScalar(themeConfig.pointSize);
        
        // 添加发光效果
        const glowMaterial = new THREE.MeshBasicMaterial({
          color: colors[index],
          transparent: true,
          opacity: 0.3
        });
        const glowSphere = new THREE.Mesh(
          new THREE.SphereGeometry(0.15, 8, 6),
          glowMaterial
        );
        glowSphere.position.copy(point);
        glowSphere.scale.setScalar(themeConfig.pointSize * 1.5);
        
        group.add(sphere);
        group.add(glowSphere);
      });

      // 🏔️ 创建回撤面积
      if (data.drawdown.length > 1) {
        const drawdownGeometry = new THREE.BufferGeometry();
        const drawdownVertices: number[] = [];
        const drawdownIndices: number[] = [];

        // 创建回撤区域的顶点
        data.timestamp.forEach((time, index) => {
          const x = (index / (data.timestamp.length - 1)) * 20 - 10;
          const y = (data.equity[index] / Math.max(...data.equity)) * 10;
          const drawdownY = y + (data.drawdown[index] / 100) * 2; // 回撤深度
          
          // 顶部与底部
          drawdownVertices.push(x, y, 0);
          drawdownVertices.push(x, drawdownY, 0);
          if (index < data.timestamp.length - 1) {
            const baseIndex = index * 2;
            // 创建四边形
            drawdownIndices.push(
              baseIndex, baseIndex + 1, baseIndex + 2,
              baseIndex + 1, baseIndex + 3, baseIndex + 2
            );
          }
        });

        drawdownGeometry.setAttribute(
          'position',
          new THREE.BufferAttribute(new Float32Array(drawdownVertices), 3)
        );
        drawdownGeometry.setIndex(drawdownIndices);

        const drawdownMaterial = new THREE.MeshBasicMaterial({
          color: new THREE.Color(0xff073a),
          transparent: true,
          opacity: 0.3,
          side: THREE.DoubleSide
        });

        const drawdownMesh = new THREE.Mesh(drawdownGeometry, drawdownMaterial);
        group.add(drawdownMesh);
      }
    }

    scene.add(group);

    // 🎮 动画循环
    const animate = () => {
      if (isRotating) {
        group.rotation.y += 0.005;
      }

      renderer.render(scene, camera);
      animationRef.current = requestAnimationFrame(animate);
    };

    animate();

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [data, showGrid, showAxes, isRotating, showWireframe, themeConfig]);

  // 🎯 交互处理
  useEffect(() => {
    if (!interactive || !mountRef.current || !rendererRef.current) return;

    const canvas = rendererRef.current.domElement;
    
    const handleMouseMove = (event: MouseEvent) => {
      // TODO: 实现鼠标悬停数据点显示详情
    };

    const handleMouseClick = (event: MouseEvent) => {
      // TODO: 实现点击数据点选择
    };

    canvas.addEventListener('mousemove', handleMouseMove);
    canvas.addEventListener('click', handleMouseClick);

    return () => {
      canvas.removeEventListener('mousemove', handleMouseMove);
      canvas.removeEventListener('click', handleMouseClick);
    };
  }, [interactive]);

  return (
    <ChartContainer width={width} height={height}>
      <div ref={mountRef} />
      
      {/* 🎮 控制面板 */}
      <ControlPanel>
        <ControlButton
          active={isRotating}
          onClick={() => setIsRotating(!isRotating)}
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.95 }}
          title="Toggle Rotation"
        >
          🔄
        </ControlButton>
        
        <ControlButton
          active={showGrid}
          onClick={() => {/* TODO: 实现网格切换 */}}
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.95 }}
          title="Toggle Grid"
        >
          📊
        </ControlButton>
        
        <ControlButton
          active={showWireframe}
          onClick={() => setShowWireframe(!showWireframe)}
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.95 }}
          title="Toggle Wireframe"
        >
          🌐
        </ControlButton>
      </ControlPanel>

      {/* 📈 信息面板 */}
      {selectedPoint && (
        <InfoPanel>
          <InfoItem>
            <span className="label">Position:</span>
            <span className="value">
              ({selectedPoint.x.toFixed(2)}, {selectedPoint.y.toFixed(2)}, {selectedPoint.z.toFixed(2)})
            </span>
          </InfoItem>
        </InfoPanel>
      )}
    </ChartContainer>
  );
};

export default PerformanceChart3D;
