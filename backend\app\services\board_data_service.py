"""
板块数据服务模块

负责获取和处理各类板块数据，包括行业板块、概念板块等
"""
import logging
import time
import pandas as pd
import akshare as ak
import asyncio
from typing import Dict, Any, List
from datetime import datetime
from sqlalchemy import text

from ..core.data.db.base import db_manager
from .cache_service import cache_service

logger = logging.getLogger(__name__)

class BoardDataService:
    """板块数据服务，负责获取和处理各类板块数据"""
    
    def __init__(self):
        """初始化板块数据服务"""
        self.cache_service = cache_service
        self._cache_ttl = 300  # 板块数据默认缓存5分钟
    
    async def get_board_data(self, board_type: str = "industry") -> Dict[str, Any]:
        """
        获取板块数据
        
        Args:
            board_type: 板块类型，industry(行业)或concept(概念)
            
        Returns:
            板块数据
        """
        try:
            # 缓存键
            cache_key = f"board_data_{board_type}"
            
            # 检查缓存
            cached_data = await self.cache_service.get(cache_key)
            if cached_data:
                logger.info(f"从缓存获取{board_type}板块数据")
                return cached_data
                
            # 从数据库获取
            try:
                async with db_manager.get_session() as session:
                    # 根据板块类型选择表名
                    table_name = "board_industry" if board_type == "industry" else "board_concept"
                    
                    sql_query = f"""
                    SELECT * FROM {table_name}
                    ORDER BY 涨跌幅 DESC
                    """
                    
                    result = await session.execute(text(sql_query))
                    rows = result.fetchall()
                    
                    if not rows:
                        logger.warning(f"未找到{board_type}板块数据，尝试使用AKShare获取")
                        raise Exception("数据库中无数据")
                        
                    # 获取列名
                    columns = result.keys()
                    
                    # 转换为DataFrame
                    df = pd.DataFrame(rows, columns=columns)
                    
                    # 转换为字典
                    board_data = {
                        "boards": df.to_dict(orient="records"),
                        "update_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    }
                    
                    # 更新缓存
                    await self.cache_service.set(cache_key, board_data, ttl=self._cache_ttl)
                    
                    return board_data
            except Exception as db_error:
                logger.warning(f"从数据库获取{board_type}板块数据失败: {str(db_error)}，尝试使用AKShare获取")
                
                # 使用AKShare获取数据作为备选方案
                try:
                    # 根据板块类型选择不同的AKShare函数
                    if board_type == "industry":
                        # 使用异步线程执行同步函数
                        df = await asyncio.to_thread(ak.stock_board_industry_name_em)
                        if df is not None and not df.empty:
                            # 处理数据格式
                            boards = []
                            for _, row in df.iterrows():
                                try:
                                    # 将DataFrame的一行转换为适合前端的格式
                                    boards.append({
                                        'name': row.get('板块名称', ''),
                                        'code': row.get('板块代码', ''),
                                        'price': float(row.get('最新价', 0)),
                                        'change': float(row.get('涨跌额', 0)),
                                        'change_percent': float(row.get('涨跌幅', 0)),
                                        'turnover_rate': float(row.get('换手率', 0)),
                                        'amount': 0, # 东财API没有提供成交额
                                        'volume': 0, # 东财API没有提供成交量
                                        'rise_count': int(row.get('上涨家数', 0)),
                                        'fall_count': int(row.get('下跌家数', 0)),
                                        'leading_stock': row.get('领涨股票', ''),
                                        'leading_stock_change': float(row.get('领涨股票-涨跌幅', 0)),
                                        'market_cap': float(row.get('总市值', 0))
                                    })
                                except Exception as e:
                                    logger.warning(f"处理行业板块数据行失败: {str(e)}")
                            
                            board_data = {
                                "boards": boards,
                                "update_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                                "data_source": "akshare"
                            }
                            
                            # 更新缓存
                            await self.cache_service.set(cache_key, board_data, ttl=self._cache_ttl)
                            
                            return board_data
                    
                    elif board_type == "concept":
                        # 使用异步线程执行同步函数
                        df = await asyncio.to_thread(ak.stock_board_concept_name_em)
                        if df is not None and not df.empty:
                            # 处理数据格式
                            boards = []
                            for _, row in df.iterrows():
                                try:
                                    # 将DataFrame的一行转换为适合前端的格式
                                    boards.append({
                                        'name': row.get('概念名称', ''),
                                        'code': row.get('代码', ''),
                                        'price': float(row.get('最新价', 0)),
                                        'change': float(row.get('涨跌额', 0)),
                                        'change_percent': float(row.get('涨跌幅', 0)),
                                        'turnover_rate': float(row.get('换手率', 0)),
                                        'amount': 0, # 东财API没有提供成交额
                                        'volume': 0, # 东财API没有提供成交量
                                        'rise_count': int(row.get('上涨家数', 0)),
                                        'fall_count': int(row.get('下跌家数', 0)),
                                        'leading_stock': row.get('领涨股票', ''),
                                        'leading_stock_change': float(row.get('领涨股票-涨跌幅', 0)),
                                        'market_cap': float(row.get('总市值', 0))
                                    })
                                except Exception as e:
                                    logger.warning(f"处理概念板块数据行失败: {str(e)}")
                            
                            board_data = {
                                "boards": boards,
                                "update_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                                "data_source": "akshare"
                            }
                            
                            # 更新缓存
                            await self.cache_service.set(cache_key, board_data, ttl=self._cache_ttl)
                            
                            return board_data
                    
                    logger.warning(f"使用AKShare获取{board_type}板块数据失败")
                    return {"boards": [], "update_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
                    
                except Exception as ak_error:
                    logger.error(f"使用AKShare获取{board_type}板块数据失败: {str(ak_error)}")
                    return {"boards": [], "update_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
                
        except Exception as e:
            logger.error(f"获取{board_type}板块数据失败: {str(e)}", exc_info=True)
            # 返回空字典
            return {"boards": [], "update_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
            
    async def get_stock_board_relation(self, symbol: str) -> Dict[str, List[str]]:
        """
        获取股票所属板块关系
        
        Args:
            symbol: 股票代码
            
        Returns:
            股票所属的行业板块和概念板块
        """
        try:
            # 缓存键
            cache_key = f"stock_board_relation_{symbol}"
            
            # 检查缓存
            cached_data = await self.cache_service.get(cache_key)
            if cached_data:
                return cached_data
                
            # 从数据库获取
            async with db_manager.get_session() as session:
                sql_query = f"""
                SELECT 板块类型, 板块名称 FROM stock_board_relation
                WHERE 股票代码 = '{symbol}'
                """
                
                result = await session.execute(text(sql_query))
                rows = result.fetchall()
                
                if not rows:
                    logger.warning(f"未找到股票 {symbol} 所属板块数据")
                    return {"industry": [], "concept": []}
                    
                # 获取列名
                columns = result.keys()
                
                # 分类处理
                industry_boards = []
                concept_boards = []
                
                for row in rows:
                    board_type = row[0]  # 板块类型
                    board_name = row[1]  # 板块名称
                    
                    if board_type == "industry":
                        industry_boards.append(board_name)
                    else:
                        concept_boards.append(board_name)
                
                # 构建结果
                result = {
                    "industry": industry_boards,
                    "concept": concept_boards
                }
                
                # 更新缓存
                await self.cache_service.set(cache_key, result, ttl=self._cache_ttl)
                
                return result
                
        except Exception as e:
            logger.error(f"获取股票 {symbol} 所属板块数据失败: {str(e)}", exc_info=True)
            return {"industry": [], "concept": []}
    
    async def get_board_stocks(self, board_name: str, board_type: str = "industry") -> List[Dict[str, Any]]:
        """
        获取板块成分股
        
        Args:
            board_name: 板块名称
            board_type: 板块类型，industry(行业)或concept(概念)
            
        Returns:
            板块成分股列表
        """
        try:
            # 缓存键
            cache_key = f"board_stocks_{board_type}_{board_name}"
            
            # 检查缓存
            cached_data = await self.cache_service.get(cache_key)
            if cached_data:
                return cached_data
                
            # 从数据库获取
            async with db_manager.get_session() as session:
                sql_query = f"""
                SELECT s.* FROM stock_list s
                JOIN stock_board_relation r ON s.代码 = r.股票代码
                WHERE r.板块名称 = '{board_name}' AND r.板块类型 = '{board_type}'
                """
                
                result = await session.execute(text(sql_query))
                rows = result.fetchall()
                
                if not rows:
                    logger.warning(f"未找到{board_type}板块 {board_name} 的成分股")
                    return []
                    
                # 获取列名
                columns = result.keys()
                
                # 转换为字典列表
                stocks = []
                for row in rows:
                    stock = {}
                    for idx, column in enumerate(columns):
                        stock[column] = row[idx]
                    stocks.append(stock)
                
                # 更新缓存
                await self.cache_service.set(cache_key, stocks, ttl=self._cache_ttl)
                
                return stocks
                
        except Exception as e:
            logger.error(f"获取{board_type}板块 {board_name} 的成分股失败: {str(e)}", exc_info=True)
            return []
            
    async def close(self):
        """关闭板块数据服务"""
        pass

# 创建单例实例
board_data_service = BoardDataService() 