"""
财务盈利筛选策略
"""
import logging
import pandas as pd
from typing import Dict, Any, List, Optional
from datetime import datetime
import yaml
import json
from pathlib import Path

from app.core.strategy.base import UnifiedStrategyCard
from app.core.runtime.types import RuntimeContext, Signal, SignalType
from app.core.runtime.types import StrategyMode
from app.core.data.db.base import db_manager
from sqlalchemy import text

logger = logging.getLogger(__name__)

# 从 template.json 加载配置
template_path = Path(__file__).parent / "template.json"
with open(template_path, "r", encoding="utf-8") as f:
    template = json.load(f)

class FinancialProfitFilterStrategy(UnifiedStrategyCard):
    """财务盈利筛选策略"""
    
    def __init__(self, context: Optional[RuntimeContext] = None):
        """初始化策略"""
        super().__init__(context)
        
        # 加载配置文件
        config_path = Path(__file__).parent / "config.yaml"
        with open(config_path, "r", encoding="utf-8") as f:
            self.config = yaml.safe_load(f)
            
        # 指标与数据库字段的映射
        self.indicator_field_map = {
            "营业收入": {"value": "营业总收入_营业总收入", "yoy": "营业总收入_同比增长"},
            "净利润": {"value": "净利润_净利润", "yoy": "净利润_同比增长"},
            "毛利率": {"value": "销售毛利率", "yoy": None}
        }

    async def prepare_data(self, context: RuntimeContext) -> pd.DataFrame:
        """准备数据"""
        try:
            # 检查串行执行模式
            if not self._check_sequential_mode(context):
                return pd.DataFrame()
            
            # 获取模式配置 - 直接使用执行模式作为数据源配置
            mode = context.mode.value if context.mode else "filter"
            mode_config = self.config["data_sources"].get(mode, self.config["data_sources"]["filter"])
            
            # 获取股票代码列表
            symbols = self._get_stock_symbols(context)
            
            # 检查缓存
            cache_key = f"{self._get_cache_key(context)}_{mode}"
            cached_data = self._data_cache.get(cache_key)
            if cached_data is not None:
                if symbols and not cached_data.empty:
                    filtered_cache = cached_data[cached_data["股票代码"].isin(symbols)]
                    self._log(f"从缓存获取: {len(filtered_cache)}条数据")
                    return filtered_cache
                return cached_data
            
            # 获取数据
            db_type = mode_config["database"]["type"]
            if db_type == "postgresql":
                data = await self._get_data_from_postgresql(mode_config, symbols)
            else:
                self._log(f"不支持的数据库类型: {db_type}", "warning")
                return pd.DataFrame()
            
            if data.empty:
                return pd.DataFrame()
            
            # 缓存数据
            self._data_cache.set(cache_key, data, ttl=60)
            return data
            
        except Exception as e:
            self._log(f"准备数据失败: {str(e)}", "error")
            return pd.DataFrame()
    
    async def _get_data_from_postgresql(self, mode_config: Dict[str, Any], symbols: List[str]) -> pd.DataFrame:
        """从PostgreSQL获取数据"""
        try:
            if "database" not in mode_config or "table" not in mode_config["database"]:
                raise ValueError("配置错误：数据源未定义表名")
            
            table = mode_config["database"]["table"]
            fields = mode_config.get("fields", ["股票代码", "股票简称", "营业总收入_营业总收入", "营业总收入_同比增长", 
                                           "净利润_净利润", "净利润_同比增长", "销售毛利率"])
            
            fields_str = ", ".join([f'"{field}"' for field in fields])
            sql_query = f'SELECT {fields_str} FROM "{table}"'
            
            # 使用全量数据，不再添加最新日期筛选条件
            
            if symbols:
                sql_query += f' WHERE "股票代码" IN ('
                sql_query += ", ".join([f"'{symbol}'" for symbol in symbols])
                sql_query += ')'
            
            async with db_manager.get_session() as session:
                result = await session.execute(text(sql_query))
                rows = result.fetchall()
                return pd.DataFrame(rows, columns=fields)
                
        except Exception as e:
            self._log(f"从PostgreSQL获取数据失败: {str(e)}", "error")
            return pd.DataFrame()
            
    async def generate_signal(self, data: pd.DataFrame, params: Dict[str, Any]) -> List[Signal]:
        """生成信号"""
        try:
            if data.empty:
                return []
            
            # 获取参数
            financial_indicator = params.get("financial_indicator", "营业收入")
            indicator_operator = params.get("indicator_operator", "大于")
            indicator_value = params.get("indicator_value")
            yoy_change_operator = params.get("yoy_change_operator", "大于")
            yoy_change_value = params.get("yoy_change_value")
            
            # 获取指标对应的字段名
            indicator_fields = self.indicator_field_map.get(financial_indicator, {})
            value_field = indicator_fields.get("value")
            yoy_field = indicator_fields.get("yoy")
            
            # 记录筛选条件
            filter_conditions = []
            if indicator_value is not None:
                if financial_indicator in ["营业收入", "净利润"]:
                    unit = "万元"
                else:
                    unit = "%"
                filter_conditions.append(f"{financial_indicator}{indicator_operator}[{indicator_value}{unit}]")
            
            if financial_indicator in ["营业收入", "净利润"] and yoy_change_value is not None:
                filter_conditions.append(f"{financial_indicator}同比变动{yoy_change_operator}[{yoy_change_value}%]")
                
            self._log(f"筛选条件: {' AND '.join(filter_conditions) if filter_conditions else '无'}")
            
            # 筛选数据
            filtered_data = data.copy()
            
            # 确保数据类型正确
            if value_field and value_field in filtered_data.columns:
                filtered_data[value_field] = pd.to_numeric(filtered_data[value_field], errors="coerce")
            
            if yoy_field and yoy_field in filtered_data.columns:
                filtered_data[yoy_field] = pd.to_numeric(filtered_data[yoy_field], errors="coerce")
            
            # 按指标值筛选
            if value_field and indicator_value is not None:
                if indicator_operator == "大于":
                    filtered_data = filtered_data[filtered_data[value_field] > indicator_value]
                elif indicator_operator == "小于":
                    filtered_data = filtered_data[filtered_data[value_field] < indicator_value]
            
            # 按同比增长率筛选（仅对营业收入和净利润有效）
            if financial_indicator in ["营业收入", "净利润"] and yoy_field and yoy_change_value is not None:
                if yoy_change_operator == "大于":
                    filtered_data = filtered_data[filtered_data[yoy_field] > yoy_change_value]
                elif yoy_change_operator == "小于":
                    filtered_data = filtered_data[filtered_data[yoy_field] < yoy_change_value]
            
            # 获取唯一的股票代码
            filtered_data = filtered_data.dropna(subset=["股票代码"])
            stock_codes = filtered_data["股票代码"].unique()
            
            self._log(f"筛选出 {len(stock_codes)}只股票")
            
            # 生成信号
            signals = []
            
            for stock_code in stock_codes:
                # 获取该股票的行数据
                stock_data = filtered_data[filtered_data["股票代码"] == stock_code]
                
                # 获取股票名称
                stock_name = stock_data["股票简称"].iloc[0] if "股票简称" in stock_data.columns else ""
                
                # 获取筛选指标的值和同比增长
                indicator_value_actual = None
                if value_field and value_field in stock_data.columns:
                    indicator_value_actual = stock_data[value_field].iloc[0]
                
                yoy_change_actual = None
                if yoy_field and yoy_field in stock_data.columns:
                    yoy_change_actual = stock_data[yoy_field].iloc[0]
                
                signal_detail = {
                    "symbol": stock_code,
                    "name": stock_name,
                    "direction": "BUY",
                    "signal_type": "fundamental",
                    "confidence": 0.8,
                    "trigger_condition": " & ".join(filter_conditions),
                    "filter_conditions": filter_conditions,
                    "financial_indicator": financial_indicator,
                    "indicator_value": indicator_value_actual,
                    "yoy_change": yoy_change_actual
                }
                
                signals.append(self.create_signal(**signal_detail))
            
            return signals
            
        except Exception as e:
            self._log(f"生成信号失败: {str(e)}", "error")
            return [] 