.cyberpunk-candlestick {
  position: relative;
  background: var(--quantum-bg-dark);
  border-radius: 12px;
  box-shadow: 0 0 30px rgba(189, 147, 249, 0.1);
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
  color: var(--quantum-text-light);
  
  &::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(
      45deg,
      rgba(189, 147, 249, 0.1),
      rgba(139, 233, 253, 0.1),
      rgba(80, 250, 123, 0.1)
    );
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
  }

  &:hover::before {
    opacity: 1;
  }

  &.fullscreen {
    position: fixed;
    inset: 0;
    z-index: 1000;
    border-radius: 0;
    transform-origin: center;
    animation: none;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    
    &::after {
      content: '';
      position: absolute;
      inset: 0;
      background: linear-gradient(
        135deg,
        rgba(139, 233, 253, 0.1),
        rgba(255, 121, 198, 0.1),
        rgba(80, 250, 123, 0.1)
      );
      opacity: 0;
      transition: opacity 0.5s ease;
      pointer-events: none;
    }
    
    &:hover::after {
      opacity: 1;
    }

    .chart-container {
      transform: none;
      opacity: 1;
      transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    }
  }

  &:not(.fullscreen) {
    .chart-container {
      transform: translateY(20px);
      opacity: 0;
      animation: chartSlideIn 0.5s cubic-bezier(0.4, 0, 0.2, 1) forwards;
    }
  }

  .chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    background: var(--quantum-bg-darker);
    border-bottom: 1px solid var(--quantum-border);
    z-index: 1;
    transform-origin: top;
    animation: headerEnter 0.5s cubic-bezier(0.4, 0, 0.2, 1);

    .chart-title {
      display: flex;
      align-items: center;
      gap: 12px;
      
      .ant-typography {
        color: var(--quantum-text-light) !important;
        font-family: var(--font-mono);
        font-size: 16px;
        font-weight: 500;
        margin: 0;
        text-shadow: 0 0 10px rgba(189, 147, 249, 0.5);
      }

      .title-icon {
        color: var(--quantum-primary);
        font-size: 18px;
        filter: drop-shadow(0 0 8px rgba(189, 147, 249, 0.5));
        animation: iconPulse 2s infinite;
      }

      &::before {
        content: '>';
        color: var(--quantum-success);
        font-weight: bold;
        animation: cursorBlink 1.2s infinite;
        filter: drop-shadow(0 0 8px rgba(80, 250, 123, 0.5));
      }
    }

    .chart-controls {
      display: flex;
      gap: 12px;
      align-items: center;
      animation: controlsEnter 0.5s cubic-bezier(0.4, 0, 0.2, 1) 0.2s both;

      .timeframe-select {
        width: 100px;
        .ant-select-selector {
          background: var(--quantum-bg-dark) !important;
          border-color: var(--quantum-border) !important;
          color: var(--quantum-text-light) !important;
          transition: all 0.3s ease;

          &:hover {
            border-color: var(--quantum-primary) !important;
            box-shadow: 0 0 0 2px rgba(189, 147, 249, 0.2);
          }
        }
      }

      .indicator-toggle {
        .ant-radio-button-wrapper {
          background: var(--quantum-bg-dark);
          border-color: var(--quantum-border);
          color: var(--quantum-text-light);
          transition: all 0.3s ease;
          
          &:hover {
            color: var(--quantum-primary);
            border-color: var(--quantum-primary);
          }
          
          &-checked {
            background: var(--quantum-primary);
            border-color: var(--quantum-primary);
            color: var(--quantum-bg-dark);
            text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
            
            &::before {
              background-color: var(--quantum-primary);
            }
          }
        }
      }

      .control-button {
        position: relative;
        color: var(--quantum-secondary);
        border: none;
        background: var(--quantum-bg-dark);
        padding: 8px;
        height: 36px;
        width: 36px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        overflow: hidden;
        
        &::before {
          content: '';
          position: absolute;
          inset: 0;
          background: var(--quantum-primary);
          opacity: 0;
          transition: opacity 0.3s ease;
          border-radius: 8px;
        }
        
        &::after {
          content: '';
          position: absolute;
          inset: -2px;
          background: linear-gradient(45deg, 
            transparent,
            rgba(189, 147, 249, 0.2),
            transparent
          );
          opacity: 0;
          transition: opacity 0.3s ease;
        }
        
        &:hover {
          color: var(--quantum-primary);
          transform: translateY(-2px);
          background: var(--quantum-bg-darker);
          box-shadow: 0 4px 12px rgba(189, 147, 249, 0.2);

          &::before {
            opacity: 0.1;
          }

          &::after {
            opacity: 1;
          }
        }

        &.active {
          color: var(--quantum-primary);
          background: var(--quantum-bg-darker);
          box-shadow: 0 0 0 2px var(--quantum-primary);

          &::before {
            opacity: 0.15;
          }

          &::after {
            opacity: 1;
          }

          .anticon {
            filter: drop-shadow(0 0 8px var(--quantum-primary));
          }
        }

        &.close-button:hover {
          color: var(--quantum-error);
          transform: rotate(90deg);
          background: rgba(255, 85, 85, 0.1);
        }

        .anticon {
          position: relative;
          z-index: 1;
          transition: transform 0.3s ease, filter 0.3s ease;
        }

        &:active .anticon {
          transform: scale(0.9);
        }
      }

      .control-divider {
        height: 24px;
        margin: 0 8px;
        border-color: var(--quantum-border);
      }

      .tool-controls {
        background: var(--quantum-bg-darker);
        border-radius: 8px;
        padding: 4px;
        box-shadow: inset 0 0 0 1px var(--quantum-border);

        .control-button {
          margin: 0 2px;

          &:first-child {
            margin-left: 0;
          }

          &:last-child {
            margin-right: 0;
          }
        }
      }
    }
  }

  .chart-container {
    position: relative;
    flex: 1;
    min-height: 400px;
    background: var(--quantum-bg-dark);
    animation: chartEnter 0.5s cubic-bezier(0.4, 0, 0.2, 1) 0.1s both;
    
    canvas {
      position: absolute;
      top: 0;
      left: 0;
      width: 100% !important;
      height: 100% !important;
      transition: opacity 0.3s ease;
    }
  }

  .chart-footer {
    padding: 12px 20px;
    background: var(--quantum-bg-darker);
    border-top: 1px solid var(--quantum-border);
    z-index: 1;
    transform-origin: bottom;
    animation: footerEnter 0.5s cubic-bezier(0.4, 0, 0.2, 1) 0.2s both;
    
    .chart-indicators {
      display: flex;
      justify-content: center;
      gap: 16px;
      font-family: var(--font-mono);
      font-size: 12px;

      .ant-space-item {
        color: var(--quantum-secondary);
        transition: all 0.3s ease;

        .ant-typography {
          color: inherit;
          transition: all 0.3s ease;
          
          &:hover {
            color: var(--quantum-primary);
            text-shadow: 0 0 10px rgba(189, 147, 249, 0.5);
          }
        }

        &:hover {
          color: var(--quantum-primary);
          transform: translateY(-1px);
        }
      }
    }
  }

  .loading-container {
    position: absolute;
    inset: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(26, 27, 36, 0.9);
    backdrop-filter: blur(8px);
    z-index: 10;
    animation: loadingEnter 0.3s ease;

    .ant-spin {
      .ant-spin-dot-item {
        background-color: var(--quantum-primary);
      }
    }
  }

  .chart-tooltip {
    background-color: #282a36;
    border: 1px solid #44475a;
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    font-family: 'JetBrains Mono', monospace;
    font-size: 12px;
    padding: 8px;
    max-width: 200px;
    z-index: 9999;
    position: absolute;
    pointer-events: none;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(2px);
    
    .tooltip-time {
      color: #8be9fd;
      margin-bottom: 5px;
      font-weight: 500;
    }
    
    .tooltip-price {
      div {
        display: flex;
        justify-content: space-between;
        margin: 3px 0;
        
        span {
          color: #6272a4;
          margin-right: 8px;
        }
        
        color: #f8f8f2;
      }
    }
    
    .tooltip-volume {
      margin-top: 5px;
      padding-top: 5px;
      border-top: 1px dashed #44475a;
      
      div {
        display: flex;
        
        span {
          color: #6272a4;
          margin-right: 8px;
        }
        
        color: #f8f8f2;
      }
    }
  }
}

.cyberpunk-dropdown {
  background: var(--quantum-bg-darker) !important;
  border: 1px solid var(--quantum-border) !important;
  border-radius: 8px;
  overflow: hidden;
  animation: dropdownEnter 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  .ant-select-item {
    color: var(--quantum-text-light) !important;
    font-family: var(--font-mono);
    padding: 8px 12px;
    transition: all 0.3s ease;
    
    &:hover {
      background: var(--quantum-bg-dark);
      color: var(--quantum-primary) !important;
    }
    
    &-selected {
      background: var(--quantum-primary) !important;
      color: var(--quantum-bg-dark) !important;
      text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
    }
  }
}

@keyframes iconPulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
    filter: drop-shadow(0 0 8px rgba(189, 147, 249, 0.5));
  }
  50% {
    opacity: 0.8;
    transform: scale(0.95);
    filter: drop-shadow(0 0 12px rgba(189, 147, 249, 0.7));
  }
}

@keyframes cursorBlink {
  0%, 100% {
    opacity: 1;
    text-shadow: 0 0 10px rgba(80, 250, 123, 0.5);
  }
  50% {
    opacity: 0.5;
    text-shadow: 0 0 5px rgba(80, 250, 123, 0.3);
  }
}

@keyframes fullscreenEnter {
  from {
    position: fixed;
    opacity: 0.8;
    clip-path: circle(0% at center);
    transform: translate(-50%, -50%) scale(0.5);
    left: 50%;
    top: 50%;
  }
  to {
    position: fixed;
    opacity: 1;
    clip-path: circle(150% at center);
    transform: translate(0%, 0%) scale(1);
    left: 0;
    top: 0;
  }
}

@keyframes fullscreenExit {
  from {
    position: fixed;
    opacity: 1;
    clip-path: circle(150% at center);
    transform: translate(0%, 0%) scale(1);
  }
  to {
    position: fixed;
    opacity: 0.8;
    clip-path: circle(0% at center);
    transform: translate(-50%, -50%) scale(0.5);
  }
}

@keyframes headerEnter {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes controlsEnter {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes chartEnter {
  from {
    opacity: 0;
    transform: scale(0.98);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes footerEnter {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes loadingEnter {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes dropdownEnter {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.cyberpunk-tooltip {
  .ant-tooltip-inner {
    background: var(--quantum-bg-darker);
    border: 1px solid var(--quantum-border);
    color: var(--quantum-text-light);
    font-family: var(--font-mono);
    padding: 8px 12px;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    animation: tooltipEnter 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .ant-tooltip-arrow {
    &::before {
      background: var(--quantum-bg-darker);
    }
  }
}

@keyframes tooltipEnter {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes chartSlideIn {
  0% {
    transform: translateY(20px);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes tooltipFadeIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(5px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

:root {
  --quantum-primary: #ff79c6;
  --quantum-secondary: #bd93f9;
  --quantum-success: #50fa7b;
  --quantum-warning: #ffb86c;
  --quantum-error: #ff5555;
  --quantum-info: #8be9fd;
  --quantum-bg-dark: #1a1b24;
  --quantum-bg-darker: #13141b;
  --quantum-border: #2b2d3a;
  --quantum-text-light: #f8f8f2;
  --quantum-text-dark: #282a36;
  --quantum-up-color: #00ffa3;
  --quantum-down-color: #ff3358;
  --quantum-chart-grid: rgba(98, 114, 164, 0.1);
  --quantum-chart-text: #8be9fd;
  --quantum-chart-border: #44475a;
  --quantum-volume-up: rgba(0, 255, 163, 0.2);
  --quantum-volume-down: rgba(255, 51, 88, 0.2);
  --quantum-ma5-color: #8be9fd;
  --quantum-ma10-color: #ff79c6;
  --quantum-ma20-color: #f1fa8c;
  --quantum-shadow-glow: 0 0 20px rgba(189, 147, 249, 0.3);
  --quantum-shadow-hover: 0 8px 32px rgba(189, 147, 249, 0.2);
}

.cyberpunk-candlestick-chart {
  display: flex;
  flex-direction: column;
  background-color: #13141b;
  border-radius: 4px;
  overflow: hidden;
  height: 100%;
  
  &.fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1000;
    border-radius: 0;
  }
  
  &.closing {
    animation: fadeOutDown 0.3s ease forwards;
  }
  
  .chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 12px;
    height: 34px;
    border-bottom: 1px solid #2b2d3a;
    background-color: #191a21;
    
    .chart-title {
      font-size: 14px;
      color: #f8f8f2;
      user-select: none;
    }
    
    .chart-title-placeholder {
      width: 30px;
      height: 14px;
    }
    
    .chart-tools {
      .ant-btn {
        color: #6272a4;
        
        &:hover {
          color: #bd93f9;
        }
        
        &.ant-btn-primary {
          background-color: #bd93f9;
          color: #f8f8f2;
        }
      }
      
      .time-select {
        margin-right: 10px;
        
        .ant-radio-button-wrapper {
          border-color: #44475a;
          background-color: #282a36;
          color: #6272a4;
          font-size: 12px;
          height: 24px;
          line-height: 22px;
          padding: 0 8px;
          
          &:hover {
            color: #bd93f9;
          }
          
          &.ant-radio-button-wrapper-checked {
            border-color: #bd93f9;
            background-color: #bd93f9;
            color: #f8f8f2;
            
            &::before {
              background-color: #bd93f9;
            }
          }
        }
      }
    }
  }
  
  .chart-content {
    flex: 1;
    position: relative;
    overflow: hidden;
    
    .loading-container {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: rgba(19, 20, 27, 0.7);
      z-index: 10;
    }
  }
  
  .chart-tooltip {
    background-color: #282a36;
    border: 1px solid #44475a;
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    font-family: 'JetBrains Mono', monospace;
    font-size: 12px;
    padding: 8px;
    max-width: 200px;
    z-index: 9999;
    position: absolute;
    pointer-events: none;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(2px);
    
    .tooltip-time {
      color: #8be9fd;
      margin-bottom: 5px;
      font-weight: 500;
    }
    
    .tooltip-price {
      div {
        display: flex;
        justify-content: space-between;
        margin: 3px 0;
        
        span {
          color: #6272a4;
          margin-right: 8px;
        }
        
        color: #f8f8f2;
      }
    }
    
    .tooltip-volume {
      margin-top: 5px;
      padding-top: 5px;
      border-top: 1px dashed #44475a;
      
      div {
        display: flex;
        
        span {
          color: #6272a4;
          margin-right: 8px;
        }
        
        color: #f8f8f2;
      }
    }
  }
}

@keyframes fadeOutDown {
  0% {
    opacity: 1;
    transform: translateY(0);
  }
  100% {
    opacity: 0;
    transform: translateY(20px);
  }
} 
 