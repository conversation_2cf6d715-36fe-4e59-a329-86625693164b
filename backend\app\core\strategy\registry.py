"""
策略注册表和加载器
提供集中的策略管理和动态加载功能
"""
import logging
import os
import importlib.util
import sys
from typing import Dict, Any, Type, Optional, List
import inspect

from ..runtime.types import RuntimeContext
from .base import BaseStrategyCard, UnifiedStrategyCard

logger = logging.getLogger(__name__)

class StrategyRegistry:
    """策略注册表
    
    提供集中的策略管理和动态加载能力
    """
    
    # 单例模式
    _instance = None
    
    @classmethod
    def get_instance(cls) -> 'StrategyRegistry':
        """获取注册表实例
        
        Returns:
            StrategyRegistry: 注册表实例
        """
        if cls._instance is None:
            cls._instance = cls()
            logger.info("创建策略注册表")
        return cls._instance
    
    def __init__(self):
        """初始化注册表"""
        self._strategies: Dict[str, Type[UnifiedStrategyCard]] = {}
        self._strategy_paths: Dict[str, str] = {}
        self._strategies_cache: Dict[str, Type[UnifiedStrategyCard]] = {}
    
    def register(self, strategy_id: str, strategy_class: Type[UnifiedStrategyCard], path: Optional[str] = None) -> None:
        """注册策略类
        
        Args:
            strategy_id: 策略ID
            strategy_class: 策略类
            path: 策略文件路径（可选）
        """
        self._strategies[strategy_id] = strategy_class
        if path:
            self._strategy_paths[strategy_id] = path
        logger.info(f"注册策略: {strategy_id} - {strategy_class.__name__}")
    
    def get_strategy_class(self, strategy_id: str) -> Optional[Type[UnifiedStrategyCard]]:
        """获取策略类
        
        Args:
            strategy_id: 策略ID
            
        Returns:
            Optional[Type[UnifiedStrategyCard]]: 策略类，如果不存在返回None
        """
        return self._strategies.get(strategy_id)
    
    def load_strategy_from_path(self, template_code: str) -> Optional[Type[UnifiedStrategyCard]]:
        """从路径加载策略类
        
        Args:
            template_code: 策略代码路径
            
        Returns:
            Optional[Type[UnifiedStrategyCard]]: 策略类，如果加载失败返回None
            
        Raises:
            ValueError: 当策略类加载失败时抛出
        """
        try:
            # 缓存检查
            if template_code in self._strategies_cache:
                return self._strategies_cache[template_code]
                
            # 导入必要的模块
            import os
            import importlib.util
            import sys
            
            # 获取项目根目录 - 修复路径计算
            base_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..'))
            logger.info(f"项目根目录: {base_dir}")
            
            # 构建策略模块路径 - 注意策略文件在backend/app/strategies/templates目录下
            app_dir = os.path.join(base_dir, "backend", "app")
            strategies_dir = os.path.join(app_dir, "strategies", "templates")
            logger.info(f"策略目录: {strategies_dir}")
            
            # 确保策略代码路径有效
            if not template_code:
                raise ValueError("没有指定代码路径")
            
            # 去除路径中的反斜杠，统一使用正斜杠
            template_code = template_code.replace("\\", "/")
            logger.info(f"策略代码路径: {template_code}")
            
            # 尝试直接加载策略文件
            # 构建策略文件的完整路径
            if "/" in template_code:
                # 如果是相对路径，如 "price_filter/strategy.py"
                strategy_type, strategy_file = template_code.split("/", 1)
                strategy_file_path = os.path.join(strategies_dir, strategy_type, strategy_file)
            else:
                # 如果只有类型，如 "price_filter"
                strategy_type = template_code
                strategy_file_path = os.path.join(strategies_dir, strategy_type, "strategy.py")
            
            logger.info(f"尝试加载策略文件: {strategy_file_path}")
            
            # 检查文件是否存在
            if not os.path.exists(strategy_file_path):
                raise ValueError(f"策略文件不存在: {strategy_file_path}")
            
            # 使用importlib.util动态加载模块
            module_name = f"app.strategies.templates.{strategy_type}.{os.path.splitext(os.path.basename(strategy_file_path))[0]}"
            logger.info(f"模块名称: {module_name}")
            
            spec = importlib.util.spec_from_file_location(module_name, strategy_file_path)
            if not spec:
                raise ValueError(f"无法从文件创建模块规范: {strategy_file_path}")
                
            module = importlib.util.module_from_spec(spec)
            sys.modules[module_name] = module
            spec.loader.exec_module(module)
            
            # 获取策略类 - 首先尝试获取Strategy类
            strategy_class = getattr(module, "Strategy", None)
            
            # 如果没有找到Strategy类，尝试根据策略类型查找特定的类
            if not strategy_class:
                # 例如，对于price_filter，尝试查找PriceFilterStrategy
                class_name = f"{strategy_type.replace('_', ' ').title().replace(' ', '')}Strategy"
                logger.info(f"尝试查找策略类: {class_name}")
                strategy_class = getattr(module, class_name, None)
            
            # 尝试查找继承自UnifiedStrategyCard的类
            if not strategy_class:
                for name, obj in inspect.getmembers(module):
                    if (inspect.isclass(obj) and 
                        issubclass(obj, UnifiedStrategyCard) and 
                        obj != UnifiedStrategyCard):
                        strategy_class = obj
                        logger.info(f"找到继承自UnifiedStrategyCard的类: {name}")
                        break
            
            if not strategy_class:
                raise ValueError(f"策略模块 {module_name} 中没有找到合适的策略类")
            
            # 检查策略类是否继承自UnifiedStrategyCard
            if not issubclass(strategy_class, UnifiedStrategyCard):
                logger.warning(f"策略类 {strategy_class.__name__} 不是UnifiedStrategyCard的子类，使用包装类")
                
                # 创建一个包装类，使其符合Strategy接口
                class StrategyWrapper(UnifiedStrategyCard):
                    def __init__(self, ctx):
                        super().__init__(ctx)
                        self.strategy = strategy_class(ctx)
                        logger.info(f"创建StrategyWrapper包装类，使用策略类: {strategy_class.__name__}")
                    
                    async def prepare_data(self, ctx):
                        return await self.strategy.prepare_data(ctx)
                    
                    async def generate_signal(self, data, parameters):
                        return await self.strategy.generate_signal(data, parameters)
                
                logger.info(f"成功加载策略类: {strategy_class.__name__}，使用包装类")
                strategy_class = StrategyWrapper
            
            # 缓存策略类
            self._strategies_cache[template_code] = strategy_class
            
            logger.info(f"成功加载策略类: {strategy_class.__name__}")
            return strategy_class
            
        except Exception as e:
            logger.error(f"加载策略类失败: {str(e)}", exc_info=True)
            raise ValueError(f"加载策略类失败: {str(e)}")
    
    def clear_cache(self) -> None:
        """清空缓存"""
        self._strategies_cache.clear()
        logger.info("清空策略类缓存")

# 全局策略注册表
strategy_registry = StrategyRegistry.get_instance() 