import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vite.dev/config/
export default defineConfig({
  plugins: [react()],
  server: {
    port: 5173,
    proxy: {
      // 代理API请求到后端
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true,
        secure: false,
      },
      // 代理WebSocket请求到后端
      '/ws': {
        target: 'ws://localhost:8000',
        changeOrigin: true,
        ws: true,
      }
    }
  }
})
