import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Layout, message, App } from 'antd';
import { RocketOutlined } from '@ant-design/icons';
import PageTitle from '../components/common/PageTitle';
import { GridStack } from 'gridstack';
import 'gridstack/dist/gridstack.min.css';
import './Dashboard.scss';
import { DashboardWidget } from '../types/dashboard';
import { generateMockData, defaultLayout } from '../data/mockData';
import { userLayoutApi } from '../services/api';
import { LayoutManager, widgetTemplates } from '../services/layoutManager';
import WidgetManager from '../components/dashboard/WidgetManager';
import WidgetRenderer from '../components/dashboard/WidgetRenderer';
import AddWidgetModal from '../components/dashboard/AddWidgetModal';

const { Content } = Layout;

/**
 * 量化控制台页面组件
 */
const Dashboard: React.FC = () => {
  // 获取Antd App上下文
  const { message: messageApi } = App.useApp();
  
  // 状态管理
  const [candlestickData, setCandlestickData] = useState(generateMockData());
  const [addModalVisible, setAddModalVisible] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [userLayout, setUserLayout] = useState<DashboardWidget[] | null>(null);
  const [activeWidgets, setActiveWidgets] = useState<DashboardWidget[]>([]);
  
  // Refs
  const gridRef = useRef<HTMLDivElement>(null);
  const gridInstance = useRef<GridStack | null>(null);

  // 获取用户的保存布局
  const fetchUserLayout = useCallback(async () => {
    setIsLoading(true);
    try {
      const { layout } = await userLayoutApi.getUserLayout();
      if (layout && Array.isArray(layout) && layout.length > 0) {
        console.log('获取到用户保存的布局:', layout);
        setUserLayout(layout);
      } else {
        console.log('未找到用户布局，使用默认布局');
        setUserLayout(null);
      }
    } catch (error) {
      console.error('获取用户布局失败:', error);
      setUserLayout(null);
    } finally {
      setIsLoading(false);
    }
  }, []);
  
  // 初始化数据
  useEffect(() => {
    setCandlestickData(generateMockData());
    fetchUserLayout();
  }, [fetchUserLayout]);
  
  // 处理添加小部件
  const handleAddWidget = useCallback((widgetType: string, title: string, settings?: Record<string, any>) => {
    if (!gridInstance.current) return;
    
    // 使用布局管理器服务添加新部件
    const { newWidget, widgetId, containerId } = LayoutManager.addWidget(
      gridInstance.current, 
      widgetType, 
      title,
      settings
    );
    
    if (newWidget) {
      // 添加删除按钮事件，确保状态更新
      const deleteButton = newWidget.querySelector('.grid-stack-item-delete');
      if (deleteButton) {
        deleteButton.addEventListener('click', function() {
          // 从activeWidgets中移除该部件
          setActiveWidgets(widgets => widgets.filter(w => w.id !== widgetId));
        });
      }
      
      // 更新活动部件列表，添加新部件
      const template = widgetTemplates[widgetType] || { w: 4, h: 4, defaultTitle: 'Widget', defaultSettings: {} };
      setActiveWidgets(widgets => [...widgets, {
        id: widgetId,
        x: parseInt(newWidget.getAttribute('gs-x') || '0'),
        y: parseInt(newWidget.getAttribute('gs-y') || '0'),
        w: parseInt(newWidget.getAttribute('gs-w') || String(template.w)),
        h: parseInt(newWidget.getAttribute('gs-h') || String(template.h)),
        title: title,
        component: widgetType,
        settings: settings || {}
      }]);
    }
    
    setAddModalVisible(false);
  }, []);

  // 保存布局
  const handleSaveLayout = useCallback(async () => {
    if (!gridInstance.current) return;
    
    setIsSaving(true);
    try {
      const success = await LayoutManager.saveLayout(gridInstance.current);
      if (success) {
        messageApi.success('布局保存成功');
      } else {
        messageApi.error('保存布局失败');
      }
    } catch (error) {
      console.error('保存布局失败:', error);
      messageApi.error('保存布局失败，请稍后重试');
    } finally {
      setIsSaving(false);
    }
  }, [messageApi]);

  // 重置布局
  const handleResetLayout = useCallback(async () => {
    if (!gridInstance.current) return;
    
    try {
      const success = await LayoutManager.resetLayout(gridInstance.current);
      if (success) {
        messageApi.success('布局已重置');
        setUserLayout(null); // 清除用户布局
        // 重新加载页面以确保完全重置状态
        window.location.reload();
      } else {
        messageApi.error('重置布局失败');
      }
    } catch (error) {
      console.error('重置布局失败:', error);
      messageApi.error('重置布局失败，请稍后重试');
    }
  }, [messageApi]);
  
  // 使用GridStack初始化网格
  useEffect(() => {
    if (!gridRef.current || gridInstance.current || isLoading) return;
    
    // 清空网格
    gridRef.current.innerHTML = '';
    
    // 使用用户保存的布局或默认布局
    const layoutToUse = userLayout || defaultLayout;
    console.log('使用布局:', layoutToUse);
    
    // 创建布局HTML
    let gridHtml = '';
    layoutToUse.forEach(widget => {
      // 确定主题类
      const themeClass = widget.component === 'sentiment' || widget.component === 'announcements' || widget.component === 'strategy-group'
        ? 'theme-light'
        : 'theme-dark';
        
      // 创建GridStack项的HTML
      gridHtml += `
        <div class="grid-stack-item ${themeClass}" 
             gs-x="${widget.x}" gs-y="${widget.y}" 
             gs-w="${widget.w}" gs-h="${widget.h}"
             data-widget-id="${widget.id}"
             data-widget-type="${widget.component}">
          <div class="grid-stack-item-content">
            <div class="grid-stack-item-title">
              ${widget.title}
              <div class="grid-stack-item-title-buttons">
                <span class="grid-stack-item-delete" title="删除">×</span>
              </div>
            </div>
            <div class="grid-stack-item-body" 
                 id="${widget.id}-container" 
                 data-widget-type="${widget.component}"
                 data-original-id="${widget.id}">
              <!-- ${widget.component}将在此渲染 -->
            </div>
          </div>
        </div>
      `;
    });
    
    // 添加HTML到网格容器
    gridRef.current.innerHTML = gridHtml;

    // 初始化GridStack
      try {
        const grid = GridStack.init({
          margin: 6,
          cellHeight: 80,
          float: true,
          draggable: { handle: '.grid-stack-item-title' },
          animate: true,
          resizable: { handles: 'e,se,s,sw,w' }
        }, gridRef.current);
        
        if (!grid) {
          console.error('GridStack初始化失败');
          return;
        }
        
        gridInstance.current = grid;
        
        // 添加删除按钮事件
        document.querySelectorAll('.grid-stack-item-delete').forEach(btn => {
          btn.addEventListener('click', (e) => {
            e.stopPropagation();
            const item = (e.target as HTMLElement).closest('.grid-stack-item');
            if (item && grid) {
            // 获取被删除部件的ID
            const widgetId = item.getAttribute('data-widget-id');
            
            // 移除DOM元素
              grid.removeWidget(item as HTMLElement);
            
            // 更新状态
            if (widgetId) {
              setActiveWidgets(widgets => widgets.filter(w => w.id !== widgetId));
            }
          }
        });
      });
      
      // 设置活动部件列表
      setActiveWidgets(layoutToUse);
      
      // 添加布局变化监听
      grid.on('change', (event, items) => {
        console.log('布局已变更');
      });
      
      // 添加删除部件监听
      grid.on('removed', (event, items) => {
        if (items && items.length > 0) {
          // 找出被删除的部件ID
          items.forEach(item => {
            const el = item.el as HTMLElement;
            const widgetId = el.getAttribute('data-widget-id');
            if (widgetId) {
              console.log('部件已被删除:', widgetId);
              setActiveWidgets(widgets => widgets.filter(w => w.id !== widgetId));
            }
          });
        }
      });
    } catch (error) {
      console.error('GridStack初始化失败:', error);
    }
    
  }, [userLayout, isLoading]);

  return (
    <Content className="dashboard-container">
      <PageTitle
        title="量化控制台"
        subtitle="实时监控您的策略表现"
        icon={<RocketOutlined style={{ color: '#bd93f9' }} />}
        breadcrumbs={[{ label: '控制台' }]}
      />

      {/* 工具栏：添加、保存、重置 */}
      <WidgetManager
        gridInstance={gridInstance.current}
        isLoading={isSaving}
        onAddWidget={() => setAddModalVisible(true)}
        onSave={handleSaveLayout}
        onReset={handleResetLayout}
      />

      {/* 网格容器 */}
      <div className="grid-stack-container">
        <div ref={gridRef} className="grid-stack"></div>
            </div>

      {/* 小部件渲染器 - 负责将React组件渲染到对应容器 */}
      <WidgetRenderer widgets={activeWidgets} candlestickData={candlestickData} />

      {/* 添加小部件模态窗口 */}
      <AddWidgetModal 
        visible={addModalVisible}
        onCancel={() => setAddModalVisible(false)}
        onAddWidget={handleAddWidget}
      />
    </Content>
  );
};

export default Dashboard;
