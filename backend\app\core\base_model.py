from typing import Dict, Any, Optional
from datetime import datetime
from pydantic import BaseModel, validator
from bson import ObjectId

from .data.db.base import MongoModel, db_manager

class BaseDBModel(MongoModel, BaseModel):
    """基础数据库模型"""
    id: Optional[str] = None
    created_at: datetime = datetime.utcnow()
    updated_at: datetime = datetime.utcnow()

    @validator('id', pre=True)
    def convert_object_id(cls, v):
        """将ObjectId转换为字符串"""
        if isinstance(v, ObjectId):
            return str(v)
        return v

    class Config:
        arbitrary_types_allowed = True
        json_encoders = {
            ObjectId: str,
            datetime: lambda v: v.isoformat()
        }

    async def save_with_timestamp(self) -> None:
        """保存时更新时间戳"""
        self.updated_at = datetime.utcnow()
        db = await db_manager.get_mongodb_database(self.get_db_name())
        data = self.dict(exclude={"id"})
        
        if hasattr(self, "id") and self.id:
            # 更新
            await db[self.get_collection_name()].update_one(
                {"_id": ObjectId(self.id)},
                {"$set": data}
            )
        else:
            # 创建
            result = await db[self.get_collection_name()].insert_one(data)
            self.id = str(result.inserted_id)

    def to_response(self) -> Dict[str, Any]:
        """转换为响应格式，优化版本"""
        data = self.dict()
        
        # 确保ID是字符串格式
        if 'id' in data and data['id']:
            data['id'] = str(data['id'])
        
        # 转换时间字段
        for field in ['created_at', 'updated_at', 'acquired_at', 'expires_at']:
            if field in data and data[field]:
                if isinstance(data[field], datetime):
                    data[field] = data[field].isoformat()
        
        return data

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'BaseDBModel':
        """从字典创建实例，优化版本"""
        if '_id' in data:
            data['id'] = str(data.pop('_id'))
        
        # 确保ObjectId字段都转换为字符串
        for key, value in data.items():
            if isinstance(value, ObjectId):
                data[key] = str(value)
        
        return cls(**data)
        
    @classmethod
    def get_db_name(cls) -> str:
        """获取数据库名称"""
        return "quantcard"

class BaseResponseModel(BaseModel):
    """基础响应模型"""
    success: bool = True
    message: Optional[str] = None
    data: Optional[Any] = None
    total: Optional[int] = None
    error_code: Optional[str] = None
    details: Optional[Dict[str, Any]] = None

    class Config:
        arbitrary_types_allowed = True
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            ObjectId: str
        } 