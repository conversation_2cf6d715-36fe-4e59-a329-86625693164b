.adventure-scene {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  color: #ffffff;
}

// 背景系统
.adventure-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  
  .bg-gradient {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, 
      #0f0f23 0%, 
      #1a1a3a 25%, 
      #2d1b69 50%, 
      #1a1a3a 75%, 
      #0f0f23 100%
    );
  }
  
  .bg-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
      radial-gradient(2px 2px at 20px 30px, rgba(0, 212, 255, 0.3), transparent),
      radial-gradient(2px 2px at 40px 70px, rgba(255, 107, 53, 0.2), transparent),
      radial-gradient(1px 1px at 90px 40px, rgba(139, 92, 246, 0.4), transparent);
    background-repeat: repeat;
    background-size: 200px 100px;
    animation: particles-drift 20s linear infinite;
    opacity: 0.6;
  }
  
  .bg-grid {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
      linear-gradient(rgba(0, 212, 255, 0.05) 1px, transparent 1px),
      linear-gradient(90deg, rgba(0, 212, 255, 0.05) 1px, transparent 1px);
    background-size: 60px 60px;
    animation: grid-pulse 4s ease-in-out infinite alternate;
  }
}

@keyframes particles-drift {
  0% { transform: translate(0px, 0px) rotate(0deg); }
  33% { transform: translate(30px, -30px) rotate(120deg); }
  66% { transform: translate(-20px, 20px) rotate(240deg); }
  100% { transform: translate(0px, 0px) rotate(360deg); }
}

@keyframes grid-pulse {
  0% { opacity: 0.3; }
  100% { opacity: 0.1; }
}

// 标题区域
.adventure-header {
  position: absolute;
  top: 2rem;
  left: 50%;
  transform: translateX(-50%);
  z-index: 10;
  text-align: center;
  
  .header-content {
    background: rgba(0, 0, 0, 0.7);
    padding: 1.5rem 2.5rem;
    border-radius: 20px;
    border: 2px solid rgba(0, 212, 255, 0.3);
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 32px rgba(0, 212, 255, 0.2);
  }
  
  .adventure-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin: 0 0 1rem 0;
    background: linear-gradient(45deg, #00d4ff, #ff6b35);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    
    .title-icon {
      margin-right: 0.5rem;
      font-size: 2.2rem;
      filter: drop-shadow(0 0 10px rgba(255, 107, 53, 0.5));
    }
  }
  
  .progress-info {
    display: flex;
    gap: 2rem;
    justify-content: center;
    
    .progress-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      
      .label {
        font-size: 0.9rem;
        color: rgba(255, 255, 255, 0.7);
        margin-bottom: 0.25rem;
      }
      
      .value {
        font-size: 1.4rem;
        font-weight: 600;
        color: #00d4ff;
        text-shadow: 0 0 8px rgba(0, 212, 255, 0.5);
      }
    }
  }
}

// 关卡地图
.level-map {
  position: relative;
  width: 100%;
  height: 100%;
  z-index: 5;
  
  .connection-paths {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    
    .connection-line {
      stroke-width: 3px;
      stroke-linecap: round;
      filter: drop-shadow(0 0 4px currentColor);
      
      &.unlocked {
        stroke: #00d4ff;
        opacity: 0.8;
        animation: line-pulse 2s ease-in-out infinite;
      }
      
      &.locked {
        stroke: #404040;
        opacity: 0.4;
        stroke-dasharray: 5, 5;
      }
    }
  }
}

@keyframes line-pulse {
  0%, 100% { opacity: 0.6; }
  50% { opacity: 1; }
}

// 关卡节点
.level-node {
  position: absolute;
  transform: translate(-50%, -50%);
  cursor: pointer;
  z-index: 10;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  .node-core {
    position: relative;
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 3px solid;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    z-index: 2;
    
    .node-icon {
      font-size: 2.5rem;
      filter: drop-shadow(0 0 8px currentColor);
    }
    
    .completion-check {
      position: absolute;
      top: -5px;
      right: -5px;
      width: 28px;
      height: 28px;
      background: #00ff88;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 1rem;
      font-weight: bold;
      color: #000;
      border: 2px solid #fff;
    }
    
    .lock-icon {
      position: absolute;
      font-size: 1.5rem;
      color: #666;
    }
  }
  
  .node-glow {
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    border-radius: 50%;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 1;
  }
  
  .node-pulse {
    position: absolute;
    top: -15px;
    left: -15px;
    right: -15px;
    bottom: -15px;
    border-radius: 50%;
    border: 2px solid #ff6b35;
    opacity: 0.8;
    animation: pulse-ring 2s ease-out infinite;
    z-index: 0;
  }
  
  .node-label {
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    margin-top: 0.5rem;
    text-align: center;
    min-width: 120px;
    
    .level-name {
      font-size: 0.9rem;
      font-weight: 600;
      color: #fff;
      margin-bottom: 0.25rem;
      text-shadow: 0 1px 3px rgba(0, 0, 0, 0.8);
    }
    
    .level-stars {
      display: flex;
      justify-content: center;
      gap: 2px;
      
      .star {
        font-size: 0.7rem;
        
        &.filled {
          color: #ffd700;
          text-shadow: 0 0 8px #ffd700;
        }
        
        &.empty {
          color: #404040;
        }
      }
    }
  }
  
  // 难度等级样式
  &.tutorial .node-core {
    border-color: #10b981;
    box-shadow: 0 0 20px rgba(16, 185, 129, 0.3);
  }
  
  &.easy .node-core {
    border-color: #3b82f6;
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }
  
  &.medium .node-core {
    border-color: #f59e0b;
    box-shadow: 0 0 20px rgba(245, 158, 11, 0.3);
  }
  
  &.hard .node-core {
    border-color: #ef4444;
    box-shadow: 0 0 20px rgba(239, 68, 68, 0.3);
  }
  
  &.expert .node-core {
    border-color: #8b5cf6;
    box-shadow: 0 0 20px rgba(139, 92, 246, 0.3);
  }
  
  &.legendary .node-core {
    border-color: #ffd700;
    box-shadow: 0 0 30px rgba(255, 215, 0, 0.5);
    animation: legendary-glow 3s ease-in-out infinite;
  }
  
  // 状态样式
  &.completed {
    .node-glow {
      background: radial-gradient(circle, rgba(0, 255, 136, 0.3) 0%, transparent 70%);
      opacity: 1;
    }
  }
  
  &.unlocked:hover {
    .node-glow {
      background: radial-gradient(circle, var(--difficulty-color, #00d4ff) 0%, transparent 70%);
      opacity: 0.4;
    }
  }
  
  &.locked {
    cursor: not-allowed;
    opacity: 0.5;
    
    .node-core {
      border-color: #404040;
      box-shadow: none;
    }
  }
  
  &.selected .node-core {
    transform: scale(1.1);
    box-shadow: 0 0 30px rgba(0, 212, 255, 0.6);
  }
}

@keyframes pulse-ring {
  0% {
    transform: scale(1);
    opacity: 0.8;
  }
  100% {
    transform: scale(1.4);
    opacity: 0;
  }
}

@keyframes legendary-glow {
  0%, 100% {
    box-shadow: 0 0 30px rgba(255, 215, 0, 0.5);
  }
  50% {
    box-shadow: 0 0 50px rgba(255, 215, 0, 0.8);
  }
}

// 关卡详情面板
.level-details-panel {
  position: absolute;
  top: 50%;
  right: 2rem;
  transform: translateY(-50%);
  width: 320px;
  background: rgba(0, 0, 0, 0.95);
  border: 2px solid rgba(0, 212, 255, 0.4);
  border-radius: 16px;
  backdrop-filter: blur(15px);
  box-shadow: 0 16px 48px rgba(0, 212, 255, 0.2);
  z-index: 100;
  
  .panel-header {
    display: flex;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    
    .level-icon {
      width: 60px;
      height: 60px;
      background: linear-gradient(135deg, rgba(0, 212, 255, 0.2), rgba(139, 92, 246, 0.2));
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 2rem;
      margin-right: 1rem;
    }
    
    .level-info {
      flex: 1;
      
      .level-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: #fff;
        margin: 0 0 0.5rem 0;
      }
      
      .level-meta {
        display: flex;
        align-items: center;
        gap: 1rem;
        
        .difficulty {
          font-size: 0.75rem;
          font-weight: 600;
          padding: 0.25rem 0.5rem;
          border-radius: 12px;
          text-transform: uppercase;
          
          &.tutorial { background: rgba(16, 185, 129, 0.2); color: #10b981; }
          &.easy { background: rgba(59, 130, 246, 0.2); color: #3b82f6; }
          &.medium { background: rgba(245, 158, 11, 0.2); color: #f59e0b; }
          &.hard { background: rgba(239, 68, 68, 0.2); color: #ef4444; }
          &.expert { background: rgba(139, 92, 246, 0.2); color: #8b5cf6; }
          &.legendary { background: rgba(255, 215, 0, 0.2); color: #ffd700; }
        }
        
        .stars {
          display: flex;
          gap: 2px;
        }
      }
    }
  }
  
  .panel-content {
    padding: 1.5rem;
    
    .level-description {
      color: rgba(255, 255, 255, 0.8);
      line-height: 1.6;
      margin-bottom: 1.5rem;
    }
    
    .rewards-section {
      margin-bottom: 1.5rem;
      
      h4 {
        color: #00d4ff;
        font-size: 1rem;
        margin-bottom: 1rem;
      }
      
      .rewards-list {
        .reward-item {
          display: flex;
          align-items: center;
          gap: 0.75rem;
          padding: 0.5rem 0;
          border-bottom: 1px solid rgba(255, 255, 255, 0.05);
          
          &:last-child {
            border-bottom: none;
          }
          
          .reward-icon {
            font-size: 1.25rem;
            width: 24px;
            text-align: center;
          }
        }
      }
    }
    
    .panel-actions {
      .action-btn {
        width: 100%;
        padding: 0.75rem 1.5rem;
        border: none;
        border-radius: 12px;
        font-size: 1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        
        &.start {
          background: linear-gradient(135deg, #00d4ff, #0080ff);
          color: #000;
          
          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 212, 255, 0.4);
          }
        }
        
        &.replay {
          background: linear-gradient(135deg, #ff6b35, #f59e0b);
          color: #fff;
          
          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 107, 53, 0.4);
          }
        }
        
        &.locked {
          background: rgba(64, 64, 64, 0.5);
          color: rgba(255, 255, 255, 0.5);
          cursor: not-allowed;
        }
      }
    }
  }
  
  .panel-close {
    position: absolute;
    top: 1rem;
    right: 1rem;
    width: 32px;
    height: 32px;
    background: rgba(239, 68, 68, 0.2);
    border: 1px solid #ef4444;
    border-radius: 50%;
    color: #ef4444;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    
    &:hover {
      background: rgba(239, 68, 68, 0.3);
      transform: scale(1.1);
    }
  }
}

// 底部控制栏
.adventure-controls {
  position: absolute;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 1rem;
  z-index: 10;
  
  .control-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.25rem;
    background: rgba(0, 0, 0, 0.8);
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 25px;
    color: #fff;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    
    .btn-icon {
      font-size: 1.25rem;
    }
    
    .btn-text {
      font-weight: 500;
      font-size: 0.9rem;
    }
    
    &:hover {
      border-color: #00d4ff;
      box-shadow: 0 4px 15px rgba(0, 212, 255, 0.3);
      transform: translateY(-2px);
    }
    
    &.back:hover {
      border-color: #10b981;
      box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
    }
    
    &.collection:hover {
      border-color: #8b5cf6;
      box-shadow: 0 4px 15px rgba(139, 92, 246, 0.3);
    }
    
    &.profile:hover {
      border-color: #f59e0b;
      box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);
    }
  }
}

// 移动端适配
@media (max-width: 768px) {
  .adventure-header {
    top: 1rem;
    left: 1rem;
    right: 1rem;
    transform: none;
    
    .header-content {
      padding: 1rem 1.5rem;
    }
    
    .adventure-title {
      font-size: 1.8rem;
    }
    
    .progress-info {
      gap: 1rem;
      
      .progress-item .value {
        font-size: 1.2rem;
      }
    }
  }
  
  .level-node {
    .node-core {
      width: 60px;
      height: 60px;
      
      .node-icon {
        font-size: 2rem;
      }
    }
    
    .node-label {
      min-width: 100px;
      
      .level-name {
        font-size: 0.8rem;
      }
    }
  }
  
  .level-details-panel {
    position: fixed;
    top: auto;
    bottom: 0;
    right: 0;
    left: 0;
    transform: none;
    width: auto;
    border-radius: 16px 16px 0 0;
    max-height: 60vh;
    overflow-y: auto;
  }
  
  .adventure-controls {
    bottom: 1rem;
    
    .control-btn {
      padding: 0.5rem 1rem;
      
      .btn-text {
        display: none;
      }
    }
  }
}

// 高性能模式
@media (prefers-reduced-motion: reduce) {
  .bg-particles,
  .bg-grid,
  .node-pulse,
  .legendary-glow {
    animation: none;
  }
  
  .level-node,
  .action-btn {
    transition: none;
  }
}
