import React, { useState, useEffect } from 'react';
import { Form, Input, Button, Card, Typography, Divider, Checkbox, Space, App } from 'antd';
import { UserOutlined, LockOutlined, GithubOutlined, GoogleOutlined, WechatOutlined } from '@ant-design/icons';
import { motion } from 'framer-motion';
import { useNavigate, Link } from 'react-router-dom';
import { authService } from '../services/authService';
import './Login.scss';

const { Title, Text } = Typography;

interface LoginForm {
  username: string;
  password: string;
  remember: boolean;
}

const LoginContent: React.FC = () => {
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [greeting, setGreeting] = useState('');
  const { message } = App.useApp();

  useEffect(() => {
    const hour = new Date().getHours();
    let newGreeting = '';
    if (hour >= 5 && hour < 12) {
      newGreeting = '早上好';
    } else if (hour >= 12 && hour < 18) {
      newGreeting = '下午好';
    } else if (hour >= 18 && hour < 22) {
      newGreeting = '晚上好';
    } else {
      newGreeting = '夜深了';
    }
    setGreeting(newGreeting);
  }, []);

  const handleSubmit = async (values: LoginForm) => {
    try {
      setLoading(true);
      console.log('提交登录表单:', values);
      
      const response = await authService.login(
        values.username,
        values.password,
        values.remember
      );

      console.log('登录响应:', response);

      if (response && response.access_token) {
        if (values.remember) {
          localStorage.setItem('remember_username', values.username);
        }
        message.success('登录成功');
        
        // 获取重定向路径
        const params = new URLSearchParams(window.location.search);
        const redirectPath = params.get('redirect') || localStorage.getItem('redirectPath') || '/';
        localStorage.removeItem('redirectPath');
        
        // 导航到重定向路径
        navigate(decodeURIComponent(redirectPath));
      }
    } catch (error: any) {
      console.error('登录错误:', error);
      message.error(error.message || '登录失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  const handleSocialLogin = (platform: string) => {
    message.info(`${platform}登录功能开发中`);
  };

  return (
    <div className="login-container">
      {/* 动态背景 */}
      <div className="animated-background">
        <div className="wave" />
        <div className="wave" />
        <div className="wave" />
      </div>

      {/* 内容区域 */}
      <div className="content-wrapper">
        {/* 欢迎区域 */}
        <motion.div
          className="welcome-section"
          initial={{ opacity: 0, x: -50 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div className="logo">
            <span className="logo-icon">📈</span>
            <span className="logo-text">QuantCard</span>
          </div>
          <h1 className="welcome-text">{greeting}，欢迎回来</h1>
          <p className="slogan">智能量化，从这里开始</p>
        </motion.div>

        {/* 登录卡片 */}
        <motion.div
          className="login-card-wrapper"
          initial={{ opacity: 0, x: 50 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <Card className="login-card" bordered={false}>
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.3 }}
            >
              <Title level={2} className="card-title">登录您的账户</Title>
            </motion.div>

            <Form
              form={form}
              onFinish={handleSubmit}
              initialValues={{
                remember: true,
                username: localStorage.getItem('remember_username') || '',
              }}
              className="login-form"
            >
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
              >
                <Form.Item
                  name="username"
                  rules={[{ required: true, message: '请输入用户名' }]}
                >
                  <Input
                    prefix={<UserOutlined />}
                    size="large"
                    placeholder="用户名"
                    autoComplete="username"
                  />
                </Form.Item>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5 }}
              >
                <Form.Item
                  name="password"
                  rules={[{ required: true, message: '请输入密码' }]}
                >
                  <Input.Password
                    prefix={<LockOutlined />}
                    size="large"
                    placeholder="密码"
                    autoComplete="current-password"
                  />
                </Form.Item>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.6 }}
              >
                <div className="form-options">
                  <Form.Item name="remember" valuePropName="checked" noStyle>
                    <Checkbox>记住我</Checkbox>
                  </Form.Item>
                  <Link to="/forgot-password" className="forgot-link">
                    忘记密码？
                  </Link>
                </div>

                <Form.Item>
                  <Button
                    type="primary"
                    htmlType="submit"
                    size="large"
                    block
                    loading={loading}
                    className="login-button"
                  >
                    登录
                  </Button>
                </Form.Item>
              </motion.div>

              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.7 }}
                className="other-options"
              >
                <Divider className="divider">
                  <Text type="secondary">其他登录方式</Text>
                </Divider>

                <Space size="large" className="social-login">
                  {[
                    { icon: <GithubOutlined />, platform: 'GitHub' },
                    { icon: <GoogleOutlined />, platform: 'Google' },
                    { icon: <WechatOutlined />, platform: '微信' },
                  ].map(({ icon, platform }) => (
                    <motion.div
                      key={platform}
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                    >
                      <Button
                        type="text"
                        icon={icon}
                        className="social-button"
                        onClick={() => handleSocialLogin(platform)}
                      />
                    </motion.div>
                  ))}
                </Space>

                <div className="register-hint">
                  <Text type="secondary">
                    还没有账号？{' '}
                    <Link to="/register" className="register-link">
                      立即注册
                    </Link>
                  </Text>
                </div>
              </motion.div>
            </Form>
          </Card>
        </motion.div>
      </div>
    </div>
  );
};

// 包装组件
const Login: React.FC = () => {
  return (
    <App>
      <LoginContent />
    </App>
  );
};

export default Login; 