"""
策略基础接口模块
"""
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List, Union
from datetime import datetime
import logging
from pydantic import BaseModel, Field
from enum import Enum
import json

from ..runtime.types import RuntimeContext, Signal
from ..runtime.cache import UnifiedCache

# 使用根日志记录器，确保日志能够被正确捕获
logger = logging.getLogger("app")

class BaseStrategyCard:
    """策略卡基类"""
    
    def __init__(self, context: Optional[RuntimeContext] = None):
        """初始化策略卡
        
        Args:
            context: 运行时上下文
        """
        self.context = context
        self._data_cache = UnifiedCache()
    
    def _log(self, message: str, level: str = "info"):
        """记录日志，确保同时发送到前端
        
        Args:
            message: 日志消息
            level: 日志级别 (info/warning/error)
        """
        getattr(logger, level)(message)
        if hasattr(self.context, 'logs') and self.context.logs is not None:
            prefix = "警告: " if level == "warning" else "错误: " if level == "error" else ""
            self.context.logs.append(f"{prefix}{message}")
    
    def _check_sequential_mode(self, context: RuntimeContext) -> bool:
        """检查是否为串行执行模式，以及是否有前置信号
        
        Args:
            context: 运行时上下文
            
        Returns:
            bool: 如果是串行模式且没有前置信号，返回False；否则返回True
        """
        previous_signals = context.metadata.get("previous_signals", [])
        is_sequential = context.metadata.get("execution_mode") == "sequential"
        is_first_strategy = context.metadata.get("is_first_strategy", False)
        
        # 如果是第一个策略，不需要前置信号
        if is_first_strategy:
            self._log("作为序列中的第一个策略，无需前置信号")
            return True
            
        # 如果是后续策略，则需要检查前置信号
        if is_sequential and not previous_signals:
            self._log("串行执行模式下，前置策略未返回信号，跳过执行", "warning")
            return False
        
        return True
    
    def _get_stock_symbols(self, context: RuntimeContext) -> List[str]:
        """获取需要处理的股票代码列表
        
        Args:
            context: 运行时上下文
            
        Returns:
            List[str]: 股票代码列表
        """
        previous_signals = context.metadata.get("previous_signals", [])
        is_sequential = context.metadata.get("execution_mode") == "sequential"
        
        # 首先检查是否有前置信号
        if is_sequential and previous_signals:
            symbols = [signal.symbol for signal in previous_signals]
            self._log(f"使用前置信号股票: {len(symbols)}只")
            return symbols
        
        # 其次检查是否有择时标的
        if context.metadata.get("timing_symbols"):
            # 从timing_symbols字段中提取股票代码
            timing_symbols = context.metadata["timing_symbols"].split(",") if isinstance(context.metadata["timing_symbols"], str) else context.metadata["timing_symbols"]
            symbols = [s.strip() for s in timing_symbols if s.strip()]
            if symbols:
                self._log(f"使用择时标的: {','.join(symbols)}")
                return symbols
        
        # 最后使用参数中的symbols
        return context.parameters.get("symbols", [])
    
    def create_signal(self, symbol: str, direction: str = "BUY", signal_type: str = "fundamental",
                     confidence: float = 0.8, name: str = "", trigger_condition: str = "",
                     price: float = 0.0, change_pct: float = 0.0, volume: int = 0,
                     market_cap: Optional[float] = None, signal_strength: Optional[float] = None,
                     metadata: Optional[Dict[str, Any]] = None, **kwargs) -> Signal:
        """创建统一格式的信号对象

        Args:
            symbol: 股票代码
            direction: 交易方向，默认BUY
            signal_type: 信号类型，默认fundamental
            confidence: 置信度，默认0.8
            name: 股票名称
            trigger_condition: 触发条件
            price: 股票价格
            change_pct: 涨跌幅
            volume: 成交量
            market_cap: 市值
            signal_strength: 信号强度（择时策略用）
            metadata: 额外元数据字典
            **kwargs: 其他元数据键值对

        Returns:
            Signal: 统一格式的信号对象
        """
        # 生成ID和时间戳
        now = datetime.utcnow()
        signal_id = f"{self.context.strategy_id}_{symbol}_{now.timestamp()}"

        # 合并元数据
        combined_metadata = {
            "trigger_time": now.isoformat()
        }

        # 添加传入的metadata
        if metadata:
            combined_metadata.update(metadata)

        # 添加其他kwargs到metadata
        combined_metadata.update(kwargs)

        # 创建信号
        return Signal(
            id=signal_id,
            strategy_id=self.context.strategy_id,
            symbol=symbol,
            name=name,
            direction=direction,
            type=signal_type,
            confidence=confidence,
            trigger_condition=trigger_condition,
            price=price,
            change_pct=change_pct,
            volume=volume,
            market_cap=market_cap,
            signal_strength=signal_strength,
            metadata=combined_metadata
        )
        
    @abstractmethod
    async def prepare_data(self, context: RuntimeContext) -> Any:
        """准备数据
        
        Args:
            context: 运行时上下文
            
        Returns:
            Any: 策略所需的数据
        """
        # 检查串行执行模式
        if not self._check_sequential_mode(context):
            return None
        return None
        
    @abstractmethod
    async def generate_signal(self, data: Any, params: Dict[str, Any]) -> List[Signal]:
        """生成信号
        
        Args:
            data: 策略数据
            params: 策略参数
            
        Returns:
            List[Signal]: 生成的信号列表
        """
        pass
        
    async def execute(self, context: RuntimeContext) -> List[Signal]:
        """执行策略
        
        Args:
            context: 运行时上下文
            
        Returns:
            List[Signal]: 策略执行结果
        """
        try:
            # 1. 准备数据
            data = await self.prepare_data(context)
            
            # 2. 生成信号
            signals = await self.generate_signal(data, context.parameters)
            
            # 3. 记录到上下文
            context.signals.extend(signals)
            
            return signals
            
        except Exception as e:
            self._log(f"策略执行失败: {str(e)}", "error")
            raise
            
    def _get_cache_key(self, context: RuntimeContext) -> str:
        """生成缓存键
        
        Args:
            context: 运行时上下文
            
        Returns:
            str: 缓存键
        """
        params_str = json.dumps(context.parameters, sort_keys=True) if context.parameters else ""
        params_hash = hash(params_str) % 10000
        return f"{self.__class__.__name__}_{context.strategy_id}_{context.mode}_{params_hash}"

# 为了保持向后兼容性，添加UnifiedStrategyCard作为BaseStrategyCard的别名
UnifiedStrategyCard = BaseStrategyCard