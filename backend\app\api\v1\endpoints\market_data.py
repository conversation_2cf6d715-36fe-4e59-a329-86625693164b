"""
市场数据API

提供实时行情、历史数据等接口
优化性能，简化数据流
"""
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, Query, HTTPException, status, Path, Request
from datetime import datetime
import logging
import math
import functools
import time
import pandas as pd

from ....services.market_data_service import MarketDataService, market_data_service
from ....services.board_data_service import BoardDataService, board_data_service
from ....core.deps import get_current_user, get_market_data_service
from ....utils.error_utils import ErrorHandler, APIError
from ....utils.response_formatter import ResponseFormatter

logger = logging.getLogger(__name__)
router = APIRouter()

# 添加获取板块数据服务依赖
def get_board_data_service():
    return board_data_service

# API错误处理装饰器
def api_error_handler(func):
    @functools.wraps(func)
    async def wrapper(*args, **kwargs):
        try:
            return await func(*args, **kwargs)
        except APIError as e:
            ErrorHandler.handle_api_error(e)
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"API错误: {str(e)}", exc_info=True)
            raise HTTPException(status_code=500, detail=str(e))
    return wrapper

def sanitize_json_data(data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """处理数据中的特殊值，确保可以被JSON序列化
    
    Args:
        data: 原始数据列表
        
    Returns:
        List[Dict[str, Any]]: 处理后的数据列表
    """
    for item in data:
        for key, value in item.items():
            # 处理特殊浮点值
            if isinstance(value, float) and (pd.isna(value) or math.isinf(value)):
                item[key] = None
            # 处理NaN字符串
            elif isinstance(value, str) and value.lower() in ['nan', 'inf', '-inf']:
                item[key] = None
    return data

@router.get("/stock-list")
@api_error_handler
async def get_stock_list(
    current_user: dict = Depends(get_current_user),
    market_data_service: MarketDataService = Depends(get_market_data_service),
    force_refresh: bool = Query(False, description="是否强制刷新股票列表")
):
    """获取股票列表"""
    try:
        stocks = await market_data_service.get_stock_list(force_update=force_refresh)
        return ResponseFormatter.success(
            data=stocks,
            message=f"成功获取 {len(stocks)} 只股票的基本信息"
        )
    except Exception as e:
        logger.error(f"获取股票列表失败: {str(e)}")
        return ResponseFormatter.error(
            message=f"获取股票列表失败: {str(e)}",
            status_code=500
        )

@router.get("")
@api_error_handler
async def get_market_data(
    symbols: Optional[List[str]] = Query(None, description="股票代码列表，为空则返回全市场数据"),
    fields: Optional[List[str]] = Query(None, description="需要返回的字段，为空返回所有字段"),
    from_db: bool = Query(False, description="是否直接从数据库获取数据")
):
    """
    统一的市场数据获取接口
    支持获取指定股票或全市场数据
    """
    try:
        start_time = time.time()
        
        # 获取行情数据
        quotes = await market_data_service.get_market_data(symbols, use_db=from_db)
        
        # 过滤字段
        if fields:
            filtered_quotes = []
            for quote in quotes:
                filtered_quote = {k: quote[k] for k in fields if k in quote}
                # 确保symbol字段始终存在
                if "symbol" not in filtered_quote and "symbol" in quote:
                    filtered_quote["symbol"] = quote["symbol"]
                filtered_quotes.append(filtered_quote)
            quotes = filtered_quotes
            
        elapsed = time.time() - start_time
        logger.info(f"获取行情数据完成，处理了 {len(quotes)} 只股票，耗时 {elapsed:.3f}秒")
        
        return ResponseFormatter.success(
            data=quotes,
            message=f"成功获取 {len(quotes)} 只股票的行情数据"
        )
            
    except Exception as e:
        logger.error(f"获取行情数据失败: {str(e)}")
        return ResponseFormatter.error(
            message=f"获取行情数据失败: {str(e)}",
            status_code=500
        )

@router.get("/daily-bars")
@api_error_handler
async def get_daily_bars(
    stock_codes: str = Query(..., description="股票代码，多个代码用逗号分隔"),
    start_date: datetime = Query(..., description="开始日期"),
    end_date: Optional[datetime] = Query(None, description="结束日期"),
    indicators: Optional[str] = Query(None, description="技术指标，多个指标用逗号分隔"),
    current_user: dict = Depends(get_current_user),
    market_data_service: MarketDataService = Depends(get_market_data_service)
):
    """获取日线数据"""
    # 解析股票代码
    stock_code_list = [code.strip() for code in stock_codes.split(',') if code.strip()]
    if not stock_code_list:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="股票代码不能为空"
        )
        
    # 解析技术指标
    indicator_list = None
    if indicators:
        indicator_list = [ind.strip() for ind in indicators.split(',') if ind.strip()]
        
    # 获取数据
    data = await market_data_service.get_historical_data(
        stock_code_list,
        start_date,
        end_date,
        "1d",
        indicator_list
    )
    
    # 转换为JSON格式
    result = data.to_dict(orient='records')
    
    # 处理不符合JSON标准的浮点数值
    result = sanitize_json_data(result)
    
    return result

@router.post("/batch")
@api_error_handler
async def batch_market_data(
    request_data: Dict[str, Any],
    current_user: dict = Depends(get_current_user)
):
    """
    批量获取市场数据接口
    接收POST请求，避免GET请求URL过长
    """
    try:
        start_time = time.time()
        
        # 从请求体获取参数
        symbols = request_data.get("symbols", [])
        from_db = request_data.get("from_db", False)
        fields = request_data.get("fields", None)
        
        if not symbols:
            return ResponseFormatter.error(
                message="股票代码列表不能为空",
                status_code=400
            )
            
        # 获取行情数据
        quotes = await market_data_service.get_market_data(symbols, use_db=from_db)
        
        # 过滤字段
        if fields:
            filtered_quotes = []
            for quote in quotes:
                filtered_quote = {k: quote[k] for k in fields if k in quote}
                # 确保symbol字段始终存在
                if "symbol" not in filtered_quote and "symbol" in quote:
                    filtered_quote["symbol"] = quote["symbol"]
                filtered_quotes.append(filtered_quote)
            quotes = filtered_quotes
        
        elapsed = time.time() - start_time
        logger.info(f"批量获取行情数据完成，处理了 {len(quotes)} 只股票，耗时 {elapsed:.3f}秒")
        
        return ResponseFormatter.success(
            data=quotes,
            message=f"成功获取 {len(quotes)} 只股票的行情数据"
        )
            
    except Exception as e:
        logger.error(f"批量获取行情数据失败: {str(e)}")
        return ResponseFormatter.error(
            message=f"批量获取行情数据失败: {str(e)}",
            status_code=500
        )

@router.get("/boards/{board_type}")
@api_error_handler
async def get_board_data(
    board_type: str = Path(..., description="板块类型：industry(行业)或concept(概念)"),
    board_data_service: BoardDataService = Depends(get_board_data_service)
):
    """
    获取板块数据
    
    Args:
        board_type: 板块类型，可选值为 industry 或 concept
        
    Returns:
        板块数据列表
    """
    try:
        # 验证板块类型
        if board_type not in ["industry", "concept"]:
            return ResponseFormatter.error(
                message=f"不支持的板块类型：{board_type}，可选值为 industry 或 concept",
                status_code=400
            )
            
        # 调用服务获取板块数据
        board_data = await board_data_service.get_board_data(board_type)
        
        # 如果board_data有boards字段，直接返回boards数组
        if "boards" in board_data:
            return board_data["boards"]
            
        # 否则返回整个对象
        return board_data
        
    except Exception as e:
        logger.error(f"获取{board_type}板块数据失败: {str(e)}", exc_info=True)
        return ResponseFormatter.error(
            message=f"获取{board_type}板块数据失败: {str(e)}",
            status_code=500
        ) 