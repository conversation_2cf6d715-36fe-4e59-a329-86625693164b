// SignalTable组件深色主题样式
.signal-table-container {
  background-color: #282a36;
  border-radius: 8px;
  box-shadow: 0 0 20px rgba(189, 147, 249, 0.1),
              0 0 40px rgba(139, 233, 253, 0.1);
  border: 1px solid rgba(189, 147, 249, 0.2);
  overflow: hidden;
  margin-bottom: 16px;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, 
      #ff79c6 0%,
      #bd93f9 33%,
      #8be9fd 66%,
      #50fa7b 100%
    );
    opacity: 0.8;
  }
  
  .results-header {
    background-color: #1e1f29;
    padding: 8px 16px;
    border-bottom: 1px solid rgba(98, 114, 164, 0.2);
    
    .results-title {
      color: #ff79c6;
      font-weight: 500;
    }
    
    .results-count {
      color: #282a36;
      font-weight: bold;
      border: none;
    }
  }
  
  .signals-table {
    // 表格深色样式
    .ant-table {
      background-color: #282a36;
      color: #f8f8f2;
      
      .ant-table-thead > tr > th {
        background-color: #1e1f29;
        color: #6272a4;
        border-bottom: 1px solid rgba(98, 114, 164, 0.2);
        font-weight: 500;
        font-size: 12px;
        text-transform: uppercase;
      }
      
      .ant-table-tbody > tr > td {
        border-bottom: 1px solid rgba(98, 114, 164, 0.1);
        background-color: #282a36;
        transition: background-color 0.3s ease;
      }
      
      .ant-table-tbody > tr:hover > td {
        background-color: #44475a !important;
      }
      
      .ant-table-tbody > tr.ant-table-row:hover > td {
        background-color: #44475a !important;
      }
      
      .ant-table-cell {
        color: #f8f8f2;
      }
      
      .ant-empty-description {
        color: #6272a4;
      }
    }
    
    // 分页样式
    .ant-pagination {
      background-color: #1e1f29;
      padding: 8px;
      margin: 0;
      border-top: 1px solid rgba(98, 114, 164, 0.2);
      
      .ant-pagination-prev .ant-pagination-item-link,
      .ant-pagination-next .ant-pagination-item-link,
      .ant-pagination-item {
        background-color: #282a36;
        border-color: rgba(98, 114, 164, 0.2);
        color: #f8f8f2;
        
        &:hover {
          border-color: #ff79c6;
          color: #ff79c6;
        }
      }
      
      .ant-pagination-item-active {
        background-color: #ff79c6;
        border-color: #ff79c6;
        
        a {
          color: #282a36;
        }
        
        &:hover {
          background-color: #ff79c6;
          
          a {
            color: #282a36;
          }
        }
      }
    }
  }
  
  // 股票信息样式
  .stock-code {
    color: #ff79c6;
    font-family: monospace;
    font-size: 13px;
  }
  
  .stock-name {
    color: #f8f8f2;
    font-size: 13px;
  }
  
  .stock-price {
    color: #8be9fd;
    font-family: monospace;
    font-size: 13px;
  }
  
  .stock-market-cap {
    color: #f8f8f2;
    font-family: monospace;
    font-size: 13px;
  }
  
  // 涨跌颜色在组件中已定义
}

// 空结果状态
.empty-result {
  padding: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #282a36;
  border-radius: 8px;
  border: 1px solid rgba(98, 114, 164, 0.2);
  
  .empty-text {
    color: #6272a4;
    font-size: 14px;
  }
} 