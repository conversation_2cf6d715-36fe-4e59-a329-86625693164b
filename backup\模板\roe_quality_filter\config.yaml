name: ROE质量筛选策略
description: 基于净资产收益率进行多维度质量分析和股票筛选
version: 1.0.0
author: QuantCard团队
tags:
  - 多因子选股
  - ROE分析
  - 基本面分析
  - 质量因子

strategy_class_name: ROEQualityFilterStrategy

parameters:
  min_roe:
    type: float
    default: 10.0
    min: 0.0
    max: 100.0
    description: 最小ROE要求（%）
  max_roe_std:
    type: float
    default: 10.0
    min: 0.0
    max: 50.0
    description: 最大ROE标准差（%），用于衡量ROE稳定性
  min_quality_score:
    type: float
    default: 60.0
    min: 0.0
    max: 100.0
    description: 最小ROE质量评分
  min_positive_streak:
    type: int
    default: 4
    min: 1
    max: 20
    description: 最小连续正ROE季度数
  min_roe_percentile:
    type: float
    default: 0.7
    min: 0.0
    max: 1.0
    description: 最小ROE排名百分位（0.7表示前30%）

data_sources:
  filter:
    database:
      type: postgresql
      name: stock_base_info
      schema: public
      table: financial_indicators
    fields:
      - 股票代码
      - 股票名称
      - 报告期
      - ROE
      - 净利润率
      - 资产周转率
      - 权益乘数
      - 总资产
      - 净资产
      - 净利润
    frequency: 1d
    window_size: 1
    
  timing:
    database:
      type: postgresql
      name: stock_base_info
      schema: public
      table: financial_indicators
    fields:
      - 股票代码
      - 股票名称
      - 报告期
      - ROE
      - 净利润率
      - 资产周转率
      - 权益乘数
      - 总资产
      - 净资产
      - 净利润
    frequency: 1d
    window_size: 1
    
  backtest:
    database:
      type: postgresql
      name: stock_base_info
      schema: public
      table: financial_indicators_historical
    fields:
      - 股票代码
      - 股票名称
      - 报告期
      - ROE
      - 净利润率
      - 资产周转率
      - 权益乘数
      - 总资产
      - 净资产
      - 净利润
    frequency: 1d
    window_size: 12  # 回测时获取更多历史数据
    
  monitor:
    database:
      type: postgresql
      name: stock_base_info
      schema: public
      table: financial_indicators
    fields:
      - 股票代码
      - 股票名称
      - 报告期
      - ROE
      - 净利润率
      - 资产周转率
      - 权益乘数
    frequency: 1d
    window_size: 1

outputs:
  filter:
    type: stock_list
    fields:
      - 股票代码
      - 股票名称
      - 当前ROE
      - ROE稳定性
      - ROE趋势
      - 连续正ROE季度
      - ROE质量评分
      - ROE排名百分位
      
  timing:
    type: alert
    fields:
      - 股票代码
      - 股票名称
      - 触发条件
      - 当前ROE
      - 质量评分
      - 触发时间
      
  backtest:
    type: backtest_result
    fields:
      - 股票代码
      - 股票名称
      - 买入时间
      - ROE质量评分
      - 信号类型
      
  monitor:
    type: alert
    fields:
      - 股票代码
      - 股票名称
      - ROE变化
      - 质量评分变化
      - 监控时间