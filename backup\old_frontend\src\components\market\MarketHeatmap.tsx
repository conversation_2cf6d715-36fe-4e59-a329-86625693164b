import React, { useState, useEffect, useCallback } from 'react';
import { App, message } from 'antd';
import CyberpunkHeatmap from '../charts/CyberpunkHeatmap';
import marketDataService, { BoardData } from '../../services/marketDataService';
import { getPublicWebSocket } from '../../services/websocketService';

interface MarketHeatmapProps {
  height?: number;
  title?: string;
  showTitle?: boolean;
  style?: React.CSSProperties;
}

const MarketHeatmap: React.FC<MarketHeatmapProps> = ({ 
  height = 500,
  title = '板块热力图',
  showTitle = true,
  style
}) => {
  const { message: messageApi } = App.useApp();
  const [boardData, setBoardData] = useState<BoardData[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [boardType, setBoardType] = useState<'industry' | 'concept'>('industry');

  // 获取板块数据
  const fetchBoardData = useCallback(async () => {
    try {
      setLoading(true);
      const data = await marketDataService.getAllBoardsLatest(boardType);
      setBoardData(data);
    } catch (error) {
      console.error('获取板块数据失败:', error);
      messageApi.error('获取板块数据失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  }, [boardType, messageApi]);

  // 处理板块类型变更
  const handleBoardTypeChange = useCallback((type: 'industry' | 'concept') => {
    setBoardType(type);
  }, []);

  // 初始化和板块类型变更时获取数据
  useEffect(() => {
    fetchBoardData();
    
    // 使用WebSocket监听更新，不再使用轮询
    const ws = getPublicWebSocket();
    
    ws.on('market_update', (data) => {
      if (data.data && data.data.boardData && data.data.boardType === boardType) {
        setBoardData(data.data.boardData);
      }
    });
    
    return () => {
      ws.off('market_update');
    };
  }, [fetchBoardData, boardType]);

  return (
    <CyberpunkHeatmap
      data={boardData}
      loading={loading}
      title={title}
      showTitle={showTitle}
      height={height}
      boardType={boardType}
      onBoardTypeChange={handleBoardTypeChange}
      onRefresh={fetchBoardData}
      style={style}
    />
  );
};

export default MarketHeatmap;
