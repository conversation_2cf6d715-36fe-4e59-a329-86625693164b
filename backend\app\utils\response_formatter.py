"""
简化版响应格式化工具
提供统一的API响应格式
"""
from typing import Any, Dict

class ResponseFormatter:
    """API响应格式化工具类"""

    @staticmethod
    def success(data: Any = None, message: str = "操作成功", code: int = 0) -> Dict[str, Any]:
        """格式化成功响应"""
        return {
            "code": code,
            "message": message,
            "data": data
        }
        
    @staticmethod
    def error(message: str = "操作失败", code: int = 500, data: Any = None) -> Dict[str, Any]:
        """格式化错误响应"""
        return {
            "code": code,
            "message": message,
            "data": data
        } 
 