declare module 'react-plotly.js' {
  import { Component } from 'react';

  interface PlotParams {
    data: Array<any>;
    layout?: any;
    config?: any;
    frames?: Array<any>;
    style?: any;
    className?: string;
    onUpdate?: (figure: any, graphDiv: any) => void;
    onPurge?: (figure: any, graphDiv: any) => void;
    onError?: (err: Error) => void;
    onInitialized?: (figure: any, graphDiv: any) => void;
  }

  export default class Plot extends Component<PlotParams> {}
} 