"""
策略运行时包

提供策略执行、策略组管理和信号处理等核心功能
"""

# 导入核心组件
from .core import RuntimeCore
from .execution import StrategyExecutor
from .group_execution import StrategyGroupExecutor
from .template_service import TemplateService
from .types import RuntimeContext, ExecutionResult, Signal, StrategyMode

# 组合所有功能的统一运行时类
class StrategyRuntime(RuntimeCore, StrategyGroupExecutor):
    """
    策略运行时
    
    整合了所有策略执行相关功能的统一接口
    """
    
    def __init__(self):
        """初始化策略运行时"""
        RuntimeCore.__init__(self)
        StrategyGroupExecutor.__init__(self)
        # StrategyGroupExecutor已经继承了StrategyExecutor，所以不需要额外初始化

# 导出公开接口
__all__ = [
    'StrategyRuntime',
    'RuntimeContext',
    'ExecutionResult',
    'Signal',
    'StrategyMode'
]
