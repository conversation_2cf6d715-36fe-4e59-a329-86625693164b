"""
回测相关API

提供回测记录的创建、查询、修改等功能
"""
from typing import Dict, Any
from fastapi import APIRouter, Depends
from ....models.user import User
from ....services.backtest import BacktestService
from ....utils.response_utils import ResponseFormatter
from ....core.deps import get_current_user

router = APIRouter()

@router.post("")
async def create_backtest(
    data: Dict[str, Any],
    current_user: Dict[str, Any] = Depends(get_current_user)
) -> Dict[str, Any]:
    """创建回测记录"""
    # 添加用户ID
    data["user_id"] = str(current_user["id"])
    
    # 创建回测记录
    record = await BacktestService.create_backtest(data)
    
    return ResponseFormatter.success(
        data=record,
        message="创建回测记录成功"
    )

@router.get("/{backtest_id}")
async def get_backtest(
    backtest_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user)
) -> Dict[str, Any]:
    """获取回测记录"""
    record = await BacktestService.get_backtest(backtest_id)
    
    return ResponseFormatter.success(
        data=record,
        message="获取回测记录成功"
    )

@router.get("")
async def list_backtests(
    skip: int = 0,
    limit: int = 100,
    current_user: Dict[str, Any] = Depends(get_current_user)
) -> Dict[str, Any]:
    """获取回测记录列表"""
    records, total = await BacktestService.list_backtests(
        user_id=str(current_user["id"]),
        skip=skip,
        limit=limit
    )
    
    return ResponseFormatter.success(
        data=records,
        total=total,
        message="获取回测记录列表成功"
    )

@router.put("/{backtest_id}")
async def update_backtest(
    backtest_id: str,
    data: Dict[str, Any],
    current_user: Dict[str, Any] = Depends(get_current_user)
) -> Dict[str, Any]:
    """更新回测记录"""
    record = await BacktestService.update_backtest(backtest_id, data)
    
    return ResponseFormatter.success(
        data=record,
        message="更新回测记录成功"
    )

@router.delete("/{backtest_id}")
async def delete_backtest(
    backtest_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user)
) -> Dict[str, Any]:
    """删除回测记录"""
    await BacktestService.delete_backtest(backtest_id)
    
    return ResponseFormatter.success(
        message="删除回测记录成功"
    )

@router.post("/{backtest_id}/start")
async def start_backtest(
    backtest_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user)
) -> Dict[str, Any]:
    """启动回测"""
    record = await BacktestService.start_backtest(backtest_id)
    
    return ResponseFormatter.success(
        data=record,
        message="启动回测成功"
    )

@router.post("/{backtest_id}/stop")
async def stop_backtest(
    backtest_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user)
) -> Dict[str, Any]:
    """停止回测"""
    record = await BacktestService.stop_backtest(backtest_id)
    
    return ResponseFormatter.success(
        data=record,
        message="停止回测成功"
    ) 
 