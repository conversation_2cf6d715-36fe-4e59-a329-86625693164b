name: 特殊数字策略
description: 检测股票价格中的特殊数字模式，如AAB、ABA、AAA等，支持选股和择时模式
version: 1.0.0
author: QuantCard团队
tags:
  - 选股
  - 择时
  - 数字模式
  - 技术面

parameters:
  lookback_days:
    type: int
    default: 20
    min: 5
    max: 60
    description: 回溯天数
  price_type:
    type: string
    default: both
    options: [high, low, both]
    description: 价格类型
  pattern_types:
    type: array
    default: [AAB, ABA, AAA]
    options: [AAB, ABA, AAA, ABB, ABAB]
    description: 特殊数字模式类型
  min_pattern_strength:
    type: float
    default: 0.5
    min: 0.1
    max: 1.0
    description: 最小模式强度
  min_price_level:
    type: float
    default: 1.0
    min: 0.1
    max: 1000.0
    description: 最小价格水平
  pattern_window:
    type: int
    default: 3
    min: 3
    max: 10
    description: 模式检测窗口大小

data_sources:
  filter:
    database:
      type: questdb
      table: daily_kline
    fields:
      - symbol
      - trade_date
      - open
      - high
      - low
      - close
      - volume
      - amount
    time_field: trade_date
  timing:
    database:
      type: questdb
      table: minute_kline
    fields:
      - symbol
      - time
      - open
      - high
      - low
      - close
      - volume
      - amount
    time_field: time