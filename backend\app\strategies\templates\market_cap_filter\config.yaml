name: 市值过滤策略
description: 根据股票市值区间进行过滤
strategy_class_name: MarketCapFilterStrategy
version: 1.0.0
data_sources:
  backtest:
    database:
      type: postgresql
      name: stock_base_info
      schema: public
      table: stock_list_history
    fields:
    - 代码
    - 名称
    - 总市值
    frequency: day
    window_size: 1
  filter:
    database:
      type: postgresql
      name: stock_base_info
      schema: public
      table: stock_list_rt
    fields:
    - 代码
    - 名称
    - 总市值
    frequency: tick
    window_size: 1
  monitor:
    database:
      type: postgresql
      name: stock_base_info
      schema: public
      table: stock_list_rt
    fields:
    - 代码
    - 名称
    - 总市值
    frequency: tick
    window_size: 1
  timing:
    database:
      type: postgresql
      name: stock_base_info
      schema: public
      table: stock_list_rt
    fields:
    - 代码
    - 名称
    - 总市值
    frequency: tick
    window_size: 1
outputs:
  backtest:
    type: stock_list
    fields:
    - 代码
    - 名称
    - 最新价
    - 总市值
    - 行业
    - 地区
    - 命中时间
    - 信号类型
  filter:
    type: stock_list
    fields:
    - 代码
    - 名称
    - 总市值
  monitor:
    type: alert
    fields:
    - 代码
    - 名称
    - 最新价
    - 总市值
    - 行业
    - 地区
    - 触发条件
    - 触发时间
  timing:
    type: signal
    fields:
    - 代码
    - 名称
    - 最新价
    - 总市值
    - 行业
    - 地区
    - 信号类型
    - 信号强度
