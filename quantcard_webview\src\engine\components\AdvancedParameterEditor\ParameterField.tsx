/**
 * 🎮 参数字段组件
 * 基于旧前端的参数字段重构，适配游戏化UI
 */

import React, { memo } from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import type { ParameterFieldProps } from './types';

const FieldContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
`;

const FieldLabel = styled.label<{ $compact: boolean }>`
  font-size: ${props => props.$compact ? '0.8rem' : '0.9rem'};
  font-weight: 600;
  color: #374151;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-family: "Inter", sans-serif;
`;

const RequiredIndicator = styled.span`
  color: #ef4444;
  font-size: 0.8rem;
`;

const HelpIcon = styled.span`
  color: #9ca3af;
  font-size: 0.8rem;
  cursor: help;
`;

const InputField = styled.input<{ $compact: boolean }>`
  padding: ${props => props.$compact ? '0.375rem 0.5rem' : '0.75rem'};
  border: 2px solid #e5e7eb;
  border-radius: ${props => props.$compact ? '6px' : '8px'};
  font-size: ${props => props.$compact ? '0.8rem' : '0.9rem'};
  font-family: "Inter", sans-serif;
  background: white;
  transition: all 0.2s ease;
  min-width: ${props => props.$compact ? '60px' : 'auto'};

  &:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  &:disabled {
    background: #f9fafb;
    color: #6b7280;
    cursor: not-allowed;
  }

  &[type="number"] {
    text-align: right;
    width: ${props => props.$compact ? '80px' : 'auto'};
  }
`;

const SelectField = styled.select<{ $compact: boolean }>`
  padding: ${props => props.$compact ? '0.375rem 0.5rem' : '0.75rem'};
  border: 2px solid #e5e7eb;
  border-radius: ${props => props.$compact ? '6px' : '8px'};
  font-size: ${props => props.$compact ? '0.8rem' : '0.9rem'};
  font-family: "Inter", sans-serif;
  background: white;
  transition: all 0.2s ease;
  cursor: pointer;
  min-width: ${props => props.$compact ? '80px' : 'auto'};

  &:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  &:disabled {
    background: #f9fafb;
    color: #6b7280;
    cursor: not-allowed;
  }
`;

const SwitchContainer = styled.div`
  display: flex;
  align-items: center;
`;

const Switch = styled(motion.button)<{ $checked: boolean; $disabled: boolean }>`
  position: relative;
  width: 48px;
  height: 24px;
  border-radius: 12px;
  border: none;
  cursor: ${props => props.$disabled ? 'not-allowed' : 'pointer'};
  background: ${props => props.$checked ? '#3b82f6' : '#d1d5db'};
  transition: background-color 0.2s ease;
  
  &::after {
    content: '';
    position: absolute;
    top: 2px;
    left: ${props => props.$checked ? '26px' : '2px'};
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: white;
    transition: left 0.2s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }
  
  &:disabled {
    opacity: 0.6;
  }
`;

const ReadOnlyValue = styled.span<{ $compact: boolean }>`
  padding: ${props => props.$compact ? '0.5rem' : '0.75rem'};
  background: #f9fafb;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 0.9rem;
  color: #6b7280;
  font-family: "Inter", sans-serif;
`;

const ParameterField: React.FC<ParameterFieldProps> = memo(({
  name,
  parameter,
  value,
  onChange,
  compact = false,
  readOnly = false
}) => {
  const renderField = () => {
    if (readOnly) {
      const displayValue = parameter.options 
        ? parameter.options.find(opt => opt.value === value)?.label || value
        : value;
      
      return (
        <ReadOnlyValue $compact={compact}>
          {displayValue}
          {parameter.unit && ` ${parameter.unit}`}
        </ReadOnlyValue>
      );
    }

    switch (parameter.type) {
      case 'select':
      case 'enum':
        return (
          <SelectField
            $compact={compact}
            value={value ?? parameter.default}
            onChange={(e) => onChange(e.target.value)}
            disabled={readOnly}
          >
            {parameter.options?.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </SelectField>
        );

      case 'number':
        return (
          <InputField
            $compact={compact}
            type="number"
            value={value ?? parameter.default}
            onChange={(e) => onChange(parseFloat(e.target.value) || parameter.default)}
            min={parameter.validation?.min}
            max={parameter.validation?.max}
            step={parameter.validation?.step}
            disabled={readOnly}
            placeholder={parameter.description}
          />
        );

      case 'boolean':
        return (
          <SwitchContainer>
            <Switch
              $checked={value ?? parameter.default}
              $disabled={readOnly}
              onClick={() => !readOnly && onChange(!(value ?? parameter.default))}
              disabled={readOnly}
              whileTap={readOnly ? {} : { scale: 0.95 }}
            />
          </SwitchContainer>
        );

      default:
        return (
          <InputField
            $compact={compact}
            type="text"
            value={value ?? parameter.default}
            onChange={(e) => onChange(e.target.value)}
            disabled={readOnly}
            placeholder={parameter.description}
          />
        );
    }
  };

  return (
    <FieldContainer>
      {parameter.label && !compact && (
        <FieldLabel $compact={compact}>
          {parameter.label}
          {parameter.validation?.required && <RequiredIndicator>*</RequiredIndicator>}
          {parameter.description && (
            <HelpIcon title={parameter.description}>ℹ️</HelpIcon>
          )}
        </FieldLabel>
      )}
      {renderField()}
    </FieldContainer>
  );
});

ParameterField.displayName = 'ParameterField';

export default ParameterField; 