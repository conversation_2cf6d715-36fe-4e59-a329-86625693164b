/**
 * HTML 工具函数
 */

/**
 * 净化 HTML 字符串，只允许特定的标签和样式
 * @param html HTML 字符串
 * @returns 净化后的 HTML 字符串
 */
export const sanitizeHtml = (html: string): string => {
  if (!html) return '';
  
  // 只允许 <span> 标签和 color 样式
  return html.replace(
    /<span\s+style=(['"])color:\s*([^'"]+)\1[^>]*>(.*?)<\/span>/gi,
    (_, quote, color, content) => {
      // 验证颜色值是否为合法的 HEX 格式
      const isValidHex = /^#[0-9A-F]{6}$/i.test(color);
      if (!isValidHex) {
        return content; // 如果颜色值不合法，只返回内容
      }
      return `<span style="color: ${color}">${content}</span>`;
    }
  );
};

/**
 * 创建带颜色的文本
 * @param text 文本内容
 * @param color 颜色（HEX格式）
 * @returns HTML 字符串
 */
export const colorText = (text: string, color: string): string => {
  return `<span style="color: ${color}">${text}</span>`;
}; 
 