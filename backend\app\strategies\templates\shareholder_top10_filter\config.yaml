data_sources:
  backtest:
    cache:
      enabled: true
      ttl: 120
    database:
      table: stock_shareholder_analyse_data
      type: postgresql
    fields:
    - 股东名称
    - 期末持股持股变动
    - 股东类型
    - 股票代码
    - 股东分类
  filter:
    cache:
      enabled: true
      ttl: 120
    database:
      table: stock_shareholder_analyse_data
      type: postgresql
    fields:
    - 股东名称
    - 期末持股持股变动
    - 股东类型
    - 股票代码
    - 股东分类
  monitor: &id001
    cache:
      enabled: true
      ttl: 60
    database:
      table: stock_shareholder_analyse_data
      type: postgresql
    fields:
    - 股东名称
    - 期末持股持股变动
    - 股东类型
    - 股票代码
    - 股东分类
  timing: *id001
execution:
  max_symbols: 3000
  timeout: 60
logging:
  format: '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
  level: info
