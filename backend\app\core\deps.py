"""
依赖注入
"""
from typing import Optional, AsyncGenerator, Dict, Any
from fastapi import Depends, HTTPException, status
from fastapi.security import OAuth2PasswordBearer
from jose import JWTError, jwt
from datetime import datetime, timedelta
import logging
from pydantic import ValidationError
from bson import ObjectId
import pandas as pd
from fastapi import WebSocket

from .runtime import StrategyRuntime
from .data.db.base import db_manager
from .config import settings
from .models import TokenPayload
from ..models.user import User
from ..services.cache_service import SimpleCacheService, cache_service
from ..services.market_data_service import MarketDataService, market_data_service
from ..services.stock_info_service import StockInfoService, stock_info_service

logger = logging.getLogger(__name__)

# 修正tokenUrl，使用完整的URL路径
oauth2_scheme = OAuth2PasswordBearer(tokenUrl=f"{settings.API_V1_STR}/auth/login")

_STRATEGY_RUNTIME = None

async def get_strategy_runtime() -> 'StrategyRuntime':
    """获取全局策略运行时实例"""
    global _STRATEGY_RUNTIME
    if _STRATEGY_RUNTIME is None:
        from .runtime import StrategyRuntime
        _STRATEGY_RUNTIME = StrategyRuntime()
        await _STRATEGY_RUNTIME.start()
        
        # 初始化market_data_service属性，使用全局单例
        try:
            # 使用全局单例
            _STRATEGY_RUNTIME.set_market_data_service(market_data_service)
            logging.info("成功初始化MarketDataService（使用全局单例）")
        except Exception as e:
            logging.warning(f"无法设置MarketDataService，市场数据功能将不可用: {str(e)}")
            # 创建一个空的市场数据服务对象
            class DummyMarketDataService:
                async def get_realtime_quotes(self, stock_codes=None, indicators=None):
                    logging.warning("使用DummyMarketDataService，返回空DataFrame")
                    return pd.DataFrame()
            
            _STRATEGY_RUNTIME.set_market_data_service(DummyMarketDataService())
    
    return _STRATEGY_RUNTIME

async def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    """创建访问令牌"""
    try:
        to_encode = data.copy()
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(
                minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES
            )
        to_encode.update({"exp": expire})
        encoded_jwt = jwt.encode(
            to_encode,
            settings.SECRET_KEY,
            algorithm=settings.ALGORITHM
        )
        logger.info(f"成功创建访问令牌，用户: {data.get('sub')}")
        return encoded_jwt
    except Exception as e:
        logger.error(f"创建访问令牌失败: {str(e)}", exc_info=True)
        raise

async def get_current_user(token: str = Depends(oauth2_scheme)) -> Dict[str, Any]:
    """
    获取当前用户
    
    Args:
        token: JWT token
        
    Returns:
        Dict[str, Any]: 用户信息
        
    Raises:
        HTTPException: 如果token无效或用户不存在
    """
    try:
        # 解析token
        payload = jwt.decode(
            token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM]
        )
        token_data = TokenPayload(**payload)
        
        # 检查token是否过期
        if token_data.exp < datetime.utcnow().timestamp():
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Token已过期",
                headers={"WWW-Authenticate": "Bearer"},
            )
    except (jwt.JWTError, ValidationError):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的认证凭据",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # 获取用户
    user = await User.get_by_id(token_data.sub)
    if not user:
        raise HTTPException(status_code=404, detail="用户不存在")
    
    # 转换为字典并确保id存在且为字符串类型
    user_dict = {
        "id": user.id,
        "username": user.username,
        "email": user.email,
        "is_active": user.is_active,
        "is_superuser": user.is_superuser,
        "created_at": user.created_at,
        "updated_at": user.updated_at,
        "last_login": user.last_login,
        "avatar": user.avatar,
        "favorites": user.favorites
    }
    
    # 记录调试信息
    logger.info(f"当前用户ID: {user_dict['id']}")
    
    return user_dict

async def get_current_active_user(
    current_user: Dict[str, Any] = Depends(get_current_user),
) -> Dict[str, Any]:
    """获取当前活跃用户"""
    if not current_user.get("is_active", True):
        logger.error(f"用户已被禁用: {current_user.get('username')}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户已被禁用"
        )
    return current_user

async def get_current_active_superuser(
    current_user: Dict[str, Any] = Depends(get_current_user),
) -> Dict[str, Any]:
    """获取当前活跃的超级用户"""
    if not current_user.get("is_superuser", False):
        logger.error(f"用户权限不足: {current_user.get('username')}")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足"
        )
    return current_user

async def get_strategy_service(runtime: StrategyRuntime = Depends(get_strategy_runtime)):
    """获取策略服务实例"""
    # 在函数内部导入，避免循环引用
    from ..services.strategy_service import StrategyService
    return StrategyService(runtime)

async def get_cache_service():
    """获取缓存服务实例，使用全局单例"""
    return cache_service

async def get_market_data_service():
    """获取市场数据服务实例，使用全局单例"""
    return market_data_service

async def get_stock_info_service():
    """获取股票基础信息服务实例，使用全局单例"""
    return stock_info_service

async def get_current_user_ws(websocket: WebSocket) -> str:
    """
    WebSocket连接的用户认证
    
    Args:
        websocket: WebSocket连接
        
    Returns:
        str: 用户ID
        
    Raises:
        WebSocketDisconnect: 如果认证失败
    """
    try:
        # 从查询参数获取token
        token = websocket.query_params.get("token")
        if not token:
            logger.error("WebSocket连接缺少token")
            await websocket.close(code=1008)  # 策略错误
            return None
            
        # 解析token
        payload = jwt.decode(
            token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM]
        )
        token_data = TokenPayload(**payload)
        
        # 检查token是否过期
        if token_data.exp < datetime.utcnow().timestamp():
            logger.error("WebSocket连接使用的token已过期")
            await websocket.close(code=1008)
            return None
            
        # 获取用户
        user = await User.get_by_id(token_data.sub)
        if not user:
            logger.error(f"WebSocket连接用户不存在，用户ID: {token_data.sub}")
            await websocket.close(code=1008)
            return None
            
        return str(user.id)
    except (JWTError, ValidationError) as e:
        logger.error(f"WebSocket连接Token验证失败: {str(e)}")
        await websocket.close(code=1008)
        return None
    except Exception as e:
        logger.error(f"WebSocket连接认证时出现未知错误: {str(e)}", exc_info=True)
        await websocket.close(code=1011)  # 内部错误
        return None