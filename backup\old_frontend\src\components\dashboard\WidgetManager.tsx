import React from 'react';
import { Button, message } from 'antd';
import { PlusOutlined, SaveOutlined, ReloadOutlined } from '@ant-design/icons';
import { GridStack } from 'gridstack';
import { userLayoutApi } from '../../services/api';
import { DashboardWidget } from '../../types/dashboard';
import './WidgetManager.scss';

interface WidgetManagerProps {
  gridInstance: GridStack | null;
  isLoading: boolean;
  onAddWidget: () => void;
  onReset: () => void;
  onSave: () => void;
}

/**
 * 小部件管理器组件 - 包含添加、保存和重置布局的按钮
 */
const WidgetManager: React.FC<WidgetManagerProps> = ({
  gridInstance,
  isLoading,
  onAddWidget,
  onReset,
  onSave
}) => {
  return (
    <div className="dashboard-tools">
      <Button
        type="primary"
        icon={<PlusOutlined />}
        onClick={onAddWidget}
        className="add-widget-btn"
      >
        添加小部件
      </Button>
      <Button
        icon={<SaveOutlined />}
        onClick={onSave}
        loading={isLoading}
        className="save-layout-btn"
      >
        保存布局
      </Button>
      <Button
        icon={<ReloadOutlined />}
        onClick={onReset}
        className="reset-layout-btn"
      >
        重置布局
      </Button>
    </div>
  );
};

export default WidgetManager; 