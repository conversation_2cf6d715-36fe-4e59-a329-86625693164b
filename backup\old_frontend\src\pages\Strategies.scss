@use "sass:map";
@use "@/styles/themes/variables" as *;

.strategies-container {
  padding: map.get($spacing, 6);
  background: linear-gradient(to bottom right, map.get($color-neutral, gray-50), map.get($color-neutral, gray-100));
  min-height: 100vh;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
      radial-gradient(circle at 10% 20%, rgba($color-primary, 0.05) 0%, transparent 20%),
      radial-gradient(circle at 90% 80%, rgba(map.get($color-functional, info), 0.05) 0%, transparent 20%);
    pointer-events: none;
  }

  .create-button {
    background: linear-gradient(45deg, $color-primary, map.get($color-primary-light, 300));
    border: none;
    box-shadow: map.get($shadow, md);
    transition: map.get($transition, base);
    color: map.get($color-neutral, white);

    &:hover {
      transform: map.get($hover-effect, lift);
      box-shadow: map.get($hover-effect, glow);
    }
  }
}

.strategy-battlefield {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: map.get($spacing, 6);
  margin-top: map.get($spacing, 6);
  perspective: 1000px;
}

.strategy-card {
  position: relative;
  background: map.get($color-neutral, white);
  border: 1px solid map.get($color-neutral, gray-200);
  border-radius: map.get($border-radius, xl);
  padding: map.get($spacing, 5);
  transition: map.get($transition, base);
  transform-style: preserve-3d;
  cursor: pointer;
  box-shadow: map.get($shadow, sm);

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, 
      rgba($color-primary, 0.03) 0%, 
      rgba($color-primary, 0.01) 50%, 
      transparent 100%
    );
    border-radius: map.get($border-radius, xl);
    opacity: 0;
    transition: map.get($transition, base);
  }

  &:hover {
    transform: translateY(-4px);
    box-shadow: map.get($shadow, md);

    &::before {
      opacity: 1;
    }
  }

  h3 {
    color: map.get($color-neutral, gray-900);
    margin-bottom: map.get($spacing, 3);
    font-size: map.get($font-size, xl);
    font-weight: 600;
  }

  p {
    color: map.get($color-neutral, gray-600);
    margin-bottom: map.get($spacing, 4);
    font-size: map.get($font-size, sm);
  }
}

.card-metrics {
  display: flex;
  justify-content: space-between;
  margin-bottom: map.get($spacing, 4);

  .metric {
    display: flex;
    flex-direction: column;
    align-items: center;

    .label {
      color: map.get($color-neutral, gray-500);
      font-size: map.get($font-size, xs);
      margin-bottom: map.get($spacing, 1);
    }

    .value {
      color: map.get($color-neutral, gray-900);
      font-size: map.get($font-size, xl);
      font-weight: 600;
    }
  }
}

.card-stats {
  margin-bottom: map.get($spacing, 4);

  .stat-bar {
    height: 4px;
    background: map.get($color-neutral, gray-100);
    border-radius: map.get($border-radius, sm);
    overflow: hidden;

    .stat-fill {
      height: 100%;
      background: $color-primary;
      transition: width map.get($transition, base);
    }
  }
}

.card-actions {
  display: flex;
  justify-content: flex-end;
  gap: map.get($spacing, 2);
}

.energy-flow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  pointer-events: none;

  .energy-particle {
    position: absolute;
    width: 2px;
    height: 2px;
    background: $color-primary;
    border-radius: 50%;
    animation: flow 2s linear infinite;

    @for $i from 1 through 5 {
      &:nth-child(#{$i}) {
        left: random(100) * 1%;
        animation-delay: -$i * 0.4s;
      }
    }
  }
}

.hologram {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba($color-primary, 0.1);
  border-radius: map.get($border-radius, xl);
  backdrop-filter: blur(5px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;

  .hologram-content {
    text-align: center;
    color: map.get($color-neutral, white);
    text-shadow: 0 0 10px rgba($color-primary, 0.5);

    .hologram-metric {
      margin: map.get($spacing, 2) 0;
      
      span:first-child {
        margin-right: map.get($spacing, 2);
        opacity: 0.7;
      }
    }
  }
}

@keyframes flow {
  0% {
    transform: translateY(0) scale(1);
    opacity: 0;
  }
  50% {
    transform: translateY(100px) scale(1.5);
    opacity: 0.5;
  }
  100% {
    transform: translateY(200px) scale(1);
    opacity: 0;
  }
}

.strategies-page {
  min-height: 100vh;
  background: linear-gradient(135deg, map.get($color-neutral, gray-50), map.get($color-neutral, gray-100));
  padding: map.get($spacing, 6);

  .strategies-content {
    .strategy-section {
      .section-title {
        color: map.get($color-neutral, gray-900);
        font-size: map.get($font-size, "2xl");
        font-weight: 600;
        margin-bottom: map.get($spacing, 2);
      }

      .section-description {
        color: map.get($color-neutral, gray-600);
        font-size: map.get($font-size, base);
        margin-bottom: map.get($spacing, 6);
      }

      .strategy-card {
        background: map.get($color-neutral, white);
        border-radius: map.get($border-radius, lg);
        border: 1px solid map.get($color-neutral, gray-200);
        transition: map.get($transition, base);
        height: 100%;

        &:hover {
          transform: translateY(-4px);
          box-shadow: map.get($shadow, md);
        }

        .ant-card-meta {
          margin-bottom: map.get($spacing, 4);

          .ant-card-meta-title {
            color: map.get($color-neutral, gray-900);
            font-size: map.get($font-size, lg);
            margin-bottom: map.get($spacing, 2);
          }

          .ant-card-meta-description {
            color: map.get($color-neutral, gray-600);
            font-size: map.get($font-size, sm);
          }
        }

        .strategy-info {
          margin-bottom: map.get($spacing, 4);

          .ant-tag {
            margin: map.get($spacing, 1);
            border-radius: map.get($border-radius, full);
            font-size: map.get($font-size, xs);
            padding: 2px map.get($spacing, 2);
          }
        }

        .strategy-metrics {
          display: grid;
          grid-template-columns: repeat(3, 1fr);
          gap: map.get($spacing, 3);
          padding: map.get($spacing, 4);
          background: map.get($color-neutral, gray-50);
          border-radius: map.get($border-radius, lg);
          margin-top: map.get($spacing, 4);

          .metric {
            text-align: center;

            .label {
              color: map.get($color-neutral, gray-500);
              font-size: map.get($font-size, xs);
              margin-bottom: map.get($spacing, 1);
            }

            .value {
              color: map.get($color-neutral, gray-900);
              font-size: map.get($font-size, lg);
              font-weight: 600;

              &.positive {
                color: map.get($color-functional, success);
              }

              &.negative {
                color: map.get($color-functional, error);
              }
            }
          }
        }

        .ant-card-actions {
          background: transparent;
          border-top: 1px solid map.get($color-neutral, gray-100);
          
          li {
            margin: map.get($spacing, 2);
          }
        }
      }
    }
  }
} 
 