import { Variants } from 'framer-motion';
import { theme } from '../themes';

// 基础过渡配置
export const transitions = {
  base: {
    duration: 0.2,
    ease: 'easeInOut',
  },
  slow: {
    duration: 0.3,
    ease: 'easeInOut',
  },
  slower: {
    duration: 0.5,
    ease: 'easeInOut',
  },
};

// 预设动画变体
export const variants: Record<string, Variants> = {
  fadeIn: {
    initial: { opacity: 0 },
    animate: { opacity: 1 },
    exit: { opacity: 0 },
    transition: transitions.base,
  },
  slideInRight: {
    initial: { x: 20, opacity: 0 },
    animate: { x: 0, opacity: 1 },
    exit: { x: -20, opacity: 0 },
    transition: transitions.base,
  },
  slideInLeft: {
    initial: { x: -20, opacity: 0 },
    animate: { x: 0, opacity: 1 },
    exit: { x: 20, opacity: 0 },
    transition: transitions.base,
  },
  slideInUp: {
    initial: { y: 20, opacity: 0 },
    animate: { y: 0, opacity: 1 },
    exit: { y: -20, opacity: 0 },
    transition: transitions.base,
  },
  slideInDown: {
    initial: { y: -20, opacity: 0 },
    animate: { y: 0, opacity: 1 },
    exit: { y: 20, opacity: 0 },
    transition: transitions.base,
  },
  scale: {
    initial: { scale: 0.95, opacity: 0 },
    animate: { scale: 1, opacity: 1 },
    exit: { scale: 0.95, opacity: 0 },
    transition: transitions.base,
  },
  rotate: {
    initial: { rotate: -180, opacity: 0 },
    animate: { rotate: 0, opacity: 1 },
    exit: { rotate: 180, opacity: 0 },
    transition: transitions.slow,
  },
};

// 页面过渡动画
export const pageTransitions: Variants = {
  initial: { opacity: 0, y: 20 },
  animate: { opacity: 1, y: 0 },
  exit: { opacity: 0, y: -20 },
  transition: transitions.base,
};

// 列表项动画
export const listItemAnimations = {
  initial: { opacity: 0, x: -20 },
  animate: (index: number) => ({
    opacity: 1,
    x: 0,
    transition: {
      delay: index * 0.1,
      ...transitions.base,
    },
  }),
  exit: { opacity: 0, x: 20 },
};

// 卡片悬停动画
export const cardHoverAnimations = {
  whileHover: {
    scale: 1.02,
    boxShadow: theme.shadows.lg,
    transition: transitions.base,
  },
  whileTap: {
    scale: 0.98,
  },
};

// 按钮点击动画
export const buttonAnimations = {
  whileTap: {
    scale: 0.95,
    transition: {
      duration: 0.1,
    },
  },
};

// 加载动画
export const loadingAnimations = {
  container: {
    initial: { opacity: 0 },
    animate: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  },
  item: {
    initial: { opacity: 0, y: 20 },
    animate: {
      opacity: 1,
      y: 0,
      transition: transitions.base,
    },
  },
}; 