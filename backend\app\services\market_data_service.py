"""
市场数据服务 - 极简版

负责获取和处理市场数据，包括实时行情、历史数据等
专注于性能最优化，移除所有冗余代码
"""
import logging
import math
import time
import asyncio
import pandas as pd
from typing import List, Dict, Any, Optional, Set, Tuple
from datetime import datetime, timedelta
import aiohttp
import json
from sqlalchemy import text

from .cache_service import cache_service
from ..core.data.db.base import db_manager

logger = logging.getLogger(__name__)

class MarketDataService:
    """市场数据服务，负责获取和处理市场数据"""
    
    def __init__(self):
        """初始化市场数据服务"""
        self.cache_service = cache_service
        self._all_stocks = None
        self._last_stock_list_update = 0
        self._update_interval = 3600  # 股票列表每小时更新一次
        self._http_session = None
        self._refresh_lock = asyncio.Lock()
        logger.info("市场数据服务已初始化")
        
    async def get_stock_list(self, force_update: bool = False) -> List[Dict[str, Any]]:
        """
        获取股票列表
        
        Args:
            force_update: 是否强制更新，不使用缓存
            
        Returns:
            股票列表
        """
        current_time = time.time()
        logger.info(f"获取股票列表请求: force_update={force_update}")
            
        # 如果缓存有效且不是强制更新，使用缓存
        if not force_update and self._all_stocks is not None and \
           (current_time - self._last_stock_list_update) < self._update_interval:
            logger.info(f"使用股票列表缓存: {len(self._all_stocks)}只股票")
            return self._all_stocks
            
        # 使用锁防止多个请求同时更新
        async with self._refresh_lock:
            # 双重检查
            if not force_update and self._all_stocks is not None and \
               (current_time - self._last_stock_list_update) < self._update_interval:
                logger.info(f"[锁内]使用股票列表缓存: {len(self._all_stocks)}只股票")
                return self._all_stocks
                
            try:
                logger.info("开始从数据库获取股票列表")
                # 从数据库获取股票列表
                async with db_manager.get_session() as session:
                    sql_query = "SELECT 代码 as code, 名称 as name, 所属交易所 as exchange FROM stock_list"
                    logger.debug(f"执行SQL: {sql_query}")
                    result = await session.execute(text(sql_query))
                    records = result.fetchall()
                    
                    if not records:
                        logger.warning("数据库中未找到股票列表")
                        return []
                        
                    # 获取列名
                    column_names = result.keys()
                    
                    # 转换为字典列表
                    stock_list = []
                    for record in records:
                        stock = {}
                        for idx, column in enumerate(column_names):
                            stock[column] = record[idx]
                        stock_list.append(stock)
                    
                    # 更新缓存
                    self._all_stocks = stock_list
                    self._last_stock_list_update = current_time
                    
                    logger.info(f"已获取股票列表: {len(stock_list)}只")
                    return stock_list
            except Exception as e:
                logger.error(f"获取股票列表失败: {str(e)}", exc_info=True)
                # 如果有旧数据，仍然返回
                if self._all_stocks:
                    logger.warning(f"返回旧的股票列表缓存: {len(self._all_stocks)}只")
                    return self._all_stocks
                # 否则返回空列表
                logger.warning("无股票列表缓存，返回空列表")
                return []
            
    async def get_market_data(self, symbols: Optional[List[str]] = None, use_db: bool = False) -> List[Dict[str, Any]]:
        """
        获取市场数据 - 统一入口函数
        支持获取指定股票和全量市场数据
        
        Args:
            symbols: 股票代码列表，None表示获取全市场数据
            use_db: 是否直接从数据库获取数据，不使用缓存
            
        Returns:
            市场数据列表
        """
        start_time = time.time()
        log_symbols = "全市场" if not symbols else f"{len(symbols)}只股票"
        logger.info(f"获取市场数据请求: {log_symbols}, use_db={use_db}")
        
        if symbols and len(symbols) <= 10:
            logger.info(f"请求股票代码: {', '.join(symbols)}")
        
        try:
            # 全量市场数据模式
            if not symbols:
                result = await self._get_all_market_data(use_db)
                elapsed = time.time() - start_time
                logger.info(f"获取全市场数据完成: {len(result)}只股票, 耗时: {elapsed:.3f}秒")
                return result
            
            # 指定股票数据模式
            # 直接使用原始股票代码，不再调用_normalize_symbols
            normalized_symbols = [s.strip() for s in symbols if s.strip()]
            
            # 直接从数据库获取模式
            if use_db:
                logger.info(f"直接从数据库获取 {len(normalized_symbols)} 只股票行情")
                result = await self._fetch_market_data_from_db(normalized_symbols)
                elapsed = time.time() - start_time
                logger.info(f"从数据库获取股票行情完成: {len(result)}只, 耗时: {elapsed:.3f}秒")
                return result
            
            # 缓存优先模式
            # 检查缓存
            cached_quotes = await self.cache_service.get_quotes(normalized_symbols)
            if len(cached_quotes) == len(normalized_symbols):
                logger.info(f"缓存命中: {len(cached_quotes)}只行情数据")
                elapsed = time.time() - start_time
                logger.info(f"从缓存获取行情数据完成, 耗时: {elapsed:.3f}秒")
                return list(cached_quotes.values())
                
            # 缓存不完整，获取全量数据并更新缓存
            logger.info(f"缓存不完整: 命中{len(cached_quotes)}/{len(normalized_symbols)}只, 从数据库获取")
            all_quotes = await self._fetch_market_data_from_db(normalized_symbols)
            
            # 生成股票代码到行情数据的映射
            quotes_dict = {q["symbol"]: q for q in all_quotes}
            
            # 更新缓存
            logger.debug(f"更新行情缓存: {len(quotes_dict)}只股票")
            await self.cache_service.store_all_quotes(quotes_dict, ttl=300)  # 5分钟过期
            
            # 返回请求的股票行情
            result = [quotes_dict.get(s, {"symbol": s, "error": True}) for s in normalized_symbols]
            elapsed = time.time() - start_time
            logger.info(f"获取并缓存行情数据完成: {len(result)}只, 耗时: {elapsed:.3f}秒")
            return result
                
        except Exception as e:
            elapsed = time.time() - start_time
            logger.error(f"获取行情数据失败: {str(e)}, 耗时: {elapsed:.3f}秒", exc_info=True)
            # 尝试返回部分缓存数据
            try:
                cached_quotes = await self.cache_service.get_quotes(normalized_symbols)
                if cached_quotes:
                    logger.warning(f"返回部分缓存数据: {len(cached_quotes)}只")
                    return list(cached_quotes.values())
            except Exception as ce:
                logger.error(f"获取缓存数据失败: {str(ce)}", exc_info=True)
            
            # 无法获取行情数据，返回基础结构
            logger.warning(f"无法获取行情数据，返回空结构: {len(normalized_symbols)}只")
            return [{"symbol": s, "name": "", "price": 0, "error": True} for s in normalized_symbols]
    
    async def _get_all_market_data(self, use_db: bool = False) -> List[Dict[str, Any]]:
        """
        获取全量市场数据
        
        Args:
            use_db: 是否直接从数据库获取数据，不使用缓存
            
        Returns:
            全量市场数据列表
        """
        if use_db:
            # 直接从数据库获取
            logger.info("直接从数据库获取全量行情数据")
            return await self._fetch_market_data_from_db()
            
        # 检查缓存是否新鲜
        if await self.cache_service.is_quotes_fresh(max_age_seconds=180):
            # 缓存新鲜，直接返回
            cached_quotes = await self.cache_service.get_quotes()
            logger.info(f"使用新鲜缓存数据: {len(cached_quotes)}只股票")
            return list(cached_quotes.values())
            
        # 缓存不新鲜，获取全量数据
        try:
            logger.info("缓存不新鲜，从数据库获取全量行情数据")
            all_quotes = await self._fetch_market_data_from_db()
            
            # 更新缓存
            quotes_dict = {q["symbol"]: q for q in all_quotes}
            logger.info(f"更新全量行情缓存: {len(quotes_dict)}只股票")
            await self.cache_service.store_all_quotes(quotes_dict, ttl=300)
            
            return all_quotes
        except Exception as e:
            logger.error(f"获取全量行情数据失败: {str(e)}", exc_info=True)
            # 尝试返回缓存数据
            cached_quotes = await self.cache_service.get_quotes()
            if cached_quotes:
                logger.warning(f"返回过期缓存数据: {len(cached_quotes)}只")
                return list(cached_quotes.values())
            raise
            
    async def _fetch_market_data_from_db(self, symbols: Optional[List[str]] = None) -> List[Dict[str, Any]]:
        """
        从数据库获取市场数据
        
        Args:
            symbols: 股票代码列表，None表示获取所有股票数据
            
        Returns:
            市场数据列表
        """
        try:
            async with db_manager.get_session() as session:
                # 只查询需要的字段，减少数据传输量
                if symbols:
                    # 构建SQL查询，直接使用表中的实际列名，只选择必要字段
                    placeholders = ", ".join([f"'{code}'" for code in symbols])
                    sql_query = f"""
                    SELECT 代码, 名称, 最新价, 总市值, 六十日涨跌幅 FROM stock_list_rt
                    WHERE 代码 IN ({placeholders})
                    """
                else:
                    # 获取所有股票行情，仅限必要字段
                    sql_query = "SELECT 代码, 名称, 最新价, 总市值, 六十日涨跌幅 FROM stock_list_rt"
                
                result = await session.execute(text(sql_query))
                rows = result.fetchall()
                
                # 转换为字典列表
                if not rows:
                    logger.warning(f"未找到行情数据: {symbols if symbols else '全市场'}")
                    return []
                    
                # 获取列名
                columns = result.keys()
                
                # 精简列映射，只包含必要字段
                column_mapping = {
                    '代码': 'symbol',
                    '名称': 'name',
                    '最新价': 'price',
                    '总市值': 'market_cap',
                    '六十日涨跌幅': 'day60_change'
                }
                
                # 转换为字典列表
                quotes = []
                for row in rows:
                    quote = {}
                    for idx, column in enumerate(columns):
                        field_name = column_mapping.get(column, column)
                        quote[field_name] = row[idx]
                    quotes.append(quote)
                    
                logger.info(f"成功从数据库获取 {len(quotes)} 条行情记录")
                return quotes
                
        except Exception as e:
            logger.error(f"从数据库获取行情数据失败: {str(e)}", exc_info=True)
            # 返回空列表
            return []
    
    async def close(self):
        """关闭市场数据服务"""
        pass

# 创建单例实例
market_data_service = MarketDataService()