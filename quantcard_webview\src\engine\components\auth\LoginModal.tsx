/**
 * 🔐 登录模态框组件 - 统一游戏风格
 * 支持用户登录、访客提示和无缝集成
 */

import React, { useState, useEffect } from 'react'
import styled from 'styled-components'
import { motion, AnimatePresence } from 'framer-motion'
import { useAuth } from '../../../store/hooks/useAuth'

// 🎨 模态框容器
const ModalOverlay = styled(motion.div)`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(8px);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
`

const ModalContainer = styled(motion.div)`
  background: linear-gradient(135deg, #1e293b 0%, #334155 25%, #475569 50%, #64748b 75%, #94a3b8 100%);
  border-radius: 24px;
  padding: 2rem;
  max-width: 420px;
  width: 100%;
  box-shadow: 
    0 25px 50px rgba(0, 0, 0, 0.5),
    0 8px 32px rgba(59, 130, 246, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.2);
  position: relative;
  color: #ffffff;
`

// 🎨 头部
const ModalHeader = styled.div`
  text-align: center;
  margin-bottom: 2rem;
  
  .logo {
    font-size: 3rem;
    margin-bottom: 0.5rem;
  }
  
  .title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 0.5rem;
    font-family: 'SF Pro Display', system-ui, sans-serif;
  }
  
  .subtitle {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
  }
`

// 🎨 表单样式
const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: 1rem;
`

const InputGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
`

const Label = styled.label`
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.9rem;
  font-weight: 600;
`

const Input = styled.input`
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 0.875rem 1rem;
  color: #ffffff;
  font-size: 1rem;
  transition: all 0.2s ease;
  
  &::placeholder {
    color: rgba(255, 255, 255, 0.5);
  }
  
  &:focus {
    outline: none;
    border-color: #3b82f6;
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
`

const CheckboxGroup = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0.5rem 0;
  
  input[type="checkbox"] {
    width: 16px;
    height: 16px;
    accent-color: #3b82f6;
  }
  
  label {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.85rem;
    cursor: pointer;
  }
`

// 🎨 按钮样式
const ButtonGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-top: 1rem;
`

const Button = styled(motion.button)<{ $variant: 'primary' | 'secondary' | 'ghost' }>`
  background: ${props => {
    switch(props.$variant) {
      case 'primary': return 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)'
      case 'secondary': return 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)'
      case 'ghost': return 'rgba(255, 255, 255, 0.1)'
    }
  }};
  border: ${props => props.$variant === 'ghost' ? '1px solid rgba(255, 255, 255, 0.2)' : 'none'};
  color: #ffffff;
  padding: 0.875rem 1.5rem;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: ${props => {
      switch(props.$variant) {
        case 'primary': return '0 8px 25px rgba(59, 130, 246, 0.4)'
        case 'secondary': return '0 8px 25px rgba(139, 92, 246, 0.4)'
        case 'ghost': return '0 4px 15px rgba(255, 255, 255, 0.1)'
      }
    }};
  }
  
  &:active {
    transform: translateY(0);
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }
`

const CloseButton = styled(motion.button)`
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.8);
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 1.2rem;
  transition: all 0.2s ease;
  
  &:hover {
    background: rgba(239, 68, 68, 0.2);
    border-color: rgba(239, 68, 68, 0.3);
    color: #ef4444;
    transform: scale(1.1);
  }
`

// 🎨 错误提示
const ErrorMessage = styled(motion.div)`
  background: rgba(239, 68, 68, 0.2);
  border: 1px solid rgba(239, 68, 68, 0.3);
  color: #fecaca;
  padding: 0.75rem 1rem;
  border-radius: 12px;
  font-size: 0.85rem;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  
  &::before {
    content: '⚠️';
    font-size: 1rem;
  }
`

// 🎨 加载指示�?
const LoadingSpinner = styled(motion.div)`
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid #ffffff;
  border-radius: 50%;
`

// 🔐 组件属�?
interface LoginModalProps {
  isOpen: boolean
  onClose: () => void
  onSuccess?: () => void
  title?: string
  subtitle?: string
  showGuestOption?: boolean
}

/**
 * 🔐 登录模态框组件
 */
export const LoginModal: React.FC<LoginModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
  title = "欢迎来到 QuantCard",
  subtitle = "登录后体验完整的量化策略游戏",
  showGuestOption = true
}) => {
  const [username, setUsername] = useState('')
  const [password, setPassword] = useState('')
  const [remember, setRemember] = useState(false)
  
  const { login, guestLogin, loading, error, clearError } = useAuth()

  // 清理表单和错�?
  useEffect(() => {
    if (isOpen) {
      setUsername('')
      setPassword('')
      setRemember(false)
      clearError()
    }
  }, [isOpen, clearError])

  // 处理用户登录
  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!username.trim() || !password.trim()) {
      return
    }

    try {
      const success = await login(username.trim(), password, remember)
      if (success) {
        onSuccess?.()
        onClose()
      }
    } catch (error) {
      console.error('登录失败:', error)
    }
  }

  // 处理访客登录
  const handleGuestLogin = async () => {
    try {
      const success = await guestLogin()
      if (success) {
        onSuccess?.()
        onClose()
      }
    } catch (error) {
      console.error('访客登录失败:', error)
    }
  }

  // 动画配置
  const overlayVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1 },
    exit: { opacity: 0 }
  }

  const modalVariants = {
    hidden: { 
      opacity: 0, 
      scale: 0.8,
      y: 50 
    },
    visible: { 
      opacity: 1, 
      scale: 1,
      y: 0,
      transition: {
        type: 'spring',
        damping: 25,
        stiffness: 300
      }
    },
    exit: { 
      opacity: 0, 
      scale: 0.8,
      y: 50,
      transition: {
        duration: 0.2
      }
    }
  }

  return (
    <AnimatePresence>
      {isOpen && (
        <ModalOverlay
          variants={overlayVariants}
          initial="hidden"
          animate="visible"
          exit="exit"
          onClick={onClose}
        >
          <ModalContainer
            variants={modalVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
            onClick={(e) => e.stopPropagation()}
          >
            <CloseButton
              onClick={onClose}
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.95 }}
            >
              ×
            </CloseButton>

            <ModalHeader>
              <div className="logo">🃏</div>
              <h2 className="title">{title}</h2>
              <p className="subtitle">{subtitle}</p>
            </ModalHeader>

            {error && (
              <ErrorMessage
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
              >
                {error}
              </ErrorMessage>
            )}

            <Form onSubmit={handleLogin}>
              <InputGroup>
                <Label htmlFor="username">用户名</Label>
                <Input
                  id="username"
                  type="text"
                  placeholder="请输入用户名"
                  value={username}
                  onChange={(e) => setUsername(e.target.value)}
                  disabled={loading}
                  autoComplete="username"
                />
              </InputGroup>

              <InputGroup>
                <Label htmlFor="password">密码</Label>
                <Input
                  id="password"
                  type="password"
                  placeholder="请输入密码"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  disabled={loading}
                  autoComplete="current-password"
                />
              </InputGroup>

              <CheckboxGroup>
                <input
                  type="checkbox"
                  id="remember"
                  checked={remember}
                  onChange={(e) => setRemember(e.target.checked)}
                  disabled={loading}
                />
                <label htmlFor="remember">记住我（保持登录状态）</label>
              </CheckboxGroup>

              <ButtonGroup>
                <Button
                  type="submit"
                  $variant="primary"
                  disabled={loading || !username.trim() || !password.trim()}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  {loading ? (
                    <>
                      <LoadingSpinner
                        animate={{ rotate: 360 }}
                        transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
                      />
                      登录中...
                    </>
                  ) : (
                    <>
                      🔑 用户登录
                    </>
                  )}
                </Button>

                {showGuestOption && (
                  <Button
                    type="button"
                    $variant="ghost"
                    onClick={handleGuestLogin}
                    disabled={loading}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    👤 访客体验
                  </Button>
                )}
              </ButtonGroup>
            </Form>
          </ModalContainer>
        </ModalOverlay>
      )}
    </AnimatePresence>
  )
}

export default LoginModal 
