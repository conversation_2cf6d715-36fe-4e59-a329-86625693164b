/**
 * 🏗️策略状态Hook - 完整版
 * 导出策略管理的所有功能，包括执行、查看、设置等
 */

import { useStore } from '../index'

// 🌐 核心策略状态Hook
export const useStrategyState = () => {
  const groups = useStore(state => state.groups)
  const currentGroupId = useStore(state => state.currentGroupId)
  const loading = useStore(state => state.loading)
  const error = useStore(state => state.error)
  const loadGroups = useStore(state => state.loadGroups)
  const createGroup = useStore(state => state.createGroup)
  const deleteGroup = useStore(state => state.deleteGroup)
  const setCurrentGroup = useStore(state => state.setCurrentGroup)
  const addCardToGroup = useStore(state => state.addCardToGroup)
  const removeCardFromGroup = useStore(state => state.removeCardFromGroup)
  const executeGroup = useStore(state => state.executeGroup)
  const startGroup = useStore(state => state.startGroup)
  const stopGroup = useStore(state => state.stopGroup)
  const updateGroup = useStore(state => state.updateGroup)
  const getGroupExecutionHistory = useStore(state => state.getGroupExecutionHistory)
  const clearError = useStore(state => state.clearError)

  return {
    groups,
    currentGroupId,
    loading,
    error,
    loadGroups,
    createGroup,
    deleteGroup,
    setCurrentGroup,
    addCardToGroup,
    removeCardFromGroup,
    executeGroup,
    startGroup,
    stopGroup,
    updateGroup,
    getGroupExecutionHistory,
    clearError
  }
}
