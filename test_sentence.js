// 测试buildCoreSentence函数
function buildCoreSentence(parameters, groups, values) {
  const pickValue = (name) => {
    const v = values?.[name]
    if (v !== undefined && v !== null && v !== '') return v
    return parameters[name]?.default
  }

  // 选择第一组或名为 sentence 的组
  const groupEntries = groups ? Object.entries(groups) : []
  const sentenceGroup =
    groupEntries.find(([k]) => k.toLowerCase().includes('sentence'))?.[1] ||
    groupEntries[0]?.[1]

  // 没有组：尝试用第一个参数直接展示
  if (!sentenceGroup) {
    const first = Object.keys(parameters)[0]
    if (!first) return ''
    const val = pickValue(first)
    return `${parameters[first]?.label || first} ${val ?? ''}${parameters[first]?.unit || ''}`.trim()
  }

  const names = sentenceGroup.parameters || []
  if (names.length === 0) return ''

  const parts = []
  if (sentenceGroup.prefix) parts.push(sentenceGroup.prefix)

  // 简单约定：
  // - names[0] 可能为 operator
  // - names[1] 为主值；names[2] 为区间上界（当 operator 表示区间时）
  const opName = names[0]
  const opVal = String(pickValue(opName) ?? '')
  const v1Name = names[1]
  const v1 = v1Name ? pickValue(v1Name) : undefined
  const v2Name = names[2]
  const v2 = v2Name ? pickValue(v2Name) : undefined

  // operator 映射：直接展示选项 label（若有），否则原值
  const opLabel = (() => {
    const opt = parameters[opName]?.options?.find(o => o.value === opVal)
    return opt?.label || opVal
  })()

  if (opVal) parts.push(opLabel)
  if (v1Name) parts.push(`${v1 ?? ''}${parameters[v1Name]?.unit || ''}`)

  // 区间/介于/between 之类
  const isRange = ['区间', '介于', 'range', 'between'].includes(opVal)
  if (isRange && v2Name) {
    const sep = sentenceGroup.separator || '-'
    parts.push(`${sep}${v2 ?? ''}${parameters[v2Name]?.unit || ''}`)
  }

  // 合成
  return parts
    .filter(Boolean)
    .join(' ')
    .replace(/\s+/g, ' ')
    .trim()
}

// 测试市值模板
const marketCapTemplate = {
  'operator': {
    'type': 'select',
    'label': '条件',
    'default': '大于',
    'options': [
      { 'label': '大于', 'value': '大于' },
      { 'label': '小于', 'value': '小于' },
      { 'label': '区间', 'value': '区间' }
    ]
  },
  'marketCapMin': {
    'type': 'number',
    'label': '下限',
    'default': 100,
    'unit': '亿元'
  },
  'marketCapMax': {
    'type': 'number',
    'label': '上限',
    'default': 500,
    'unit': '亿元'
  }
};

const marketCapGroups = {
  'market_cap_filter': {
    'parameters': ['operator', 'marketCapMin', 'marketCapMax'],
    'prefix': '市值',
    'separator': '-'
  }
};

console.log('=== 测试市值模板 ===');
console.log('默认值:', buildCoreSentence(marketCapTemplate, marketCapGroups, {}));
console.log('大于100:', buildCoreSentence(marketCapTemplate, marketCapGroups, {operator: '大于', marketCapMin: 100}));
console.log('区间100-500:', buildCoreSentence(marketCapTemplate, marketCapGroups, {operator: '区间', marketCapMin: 100, marketCapMax: 500}));

// 测试概念模板
const conceptTemplate = {
  'keywords': {
    'type': 'text',
    'label': '关键字',
    'default': '',
  }
};

const conceptGroups = {
  'concept_filter': {
    'parameters': ['keywords'],
    'prefix': '概念名称包含',
    'separator': ''
  }
};

console.log('\n=== 测试概念模板 ===');
console.log('默认值:', buildCoreSentence(conceptTemplate, conceptGroups, {}));
console.log('元宇宙:', buildCoreSentence(conceptTemplate, conceptGroups, {keywords: '元宇宙'}));
