"""
模板服务模块

负责策略模板的获取、管理和查询
提供统一的接口从数据库获取模板信息
"""
import logging
from typing import List, Dict, Any, Optional, Tuple
from bson import ObjectId

logger = logging.getLogger(__name__)

class TemplateService:
    """
    策略模板服务类
    
    提供策略模板的查询、获取等功能
    封装了数据库操作
    """
    
    def __init__(self):
        """初始化模板服务"""
        pass
    
    async def find_template(self, id: str, name: Optional[str] = None) -> Dict[str, Any]:
        """
        查找策略模板
        
        通过MongoDB ObjectId查询，如果未找到则尝试通过name查询
        
        Args:
            id: 策略ID (MongoDB的_id)
            name: 策略名称（可选，仅在id无法找到时使用）
            
        Returns:
            策略模板数据，如果找不到则抛出异常
            
        Raises:
            ValueError: 如果找不到模板
        """
        from ...models.strategy_template import StrategyTemplate
        from ..data.db.base import db_manager
        
        try:
            # 获取数据库连接
            db = await db_manager.get_mongodb_database("quantcard")
            
            # 查询模板
            template_obj = None
            
            # 通过ObjectId查询
            if ObjectId.is_valid(id):
                logger.info(f"通过ObjectId查询模板: {id}")
                template_raw = await db.strategy_templates.find_one({"_id": ObjectId(id)})
                if template_raw:
                    logger.info(f"找到策略模板: {template_raw.get('name', '未知')}")
                    template_obj = StrategyTemplate(**template_raw)
                    
            # 如果未找到且提供了name，则尝试通过name查询
            if not template_obj and name:
                logger.info(f"尝试通过name查询模板: {name}")
                template_raw = await db.strategy_templates.find_one({"name": name})
                if template_raw:
                    logger.info(f"通过name找到策略模板: {template_raw.get('name', '未知')}")
                    template_obj = StrategyTemplate(**template_raw)
            
            # 如果找不到模板，抛出异常
            if not template_obj:
                raise ValueError(f"未找到策略模板: id={id}")
                
            # 将模板对象转换为策略卡
            from ...models.strategy import StrategyCard
            
            # 创建StrategyCard对象
            template = StrategyCard(
                id=str(template_obj._id if hasattr(template_obj, '_id') else "unknown"),
                template_id=str(template_obj._id if hasattr(template_obj, '_id') else "unknown"),
                name=template_obj.name,
                description=template_obj.description,
                version=template_obj.version,
                author=template_obj.author,
                stars=template_obj.stars or 0,
                tags=template_obj.tags,
                parameters=template_obj.parameters,
                parameterGroups=template_obj.parameterGroups,
                ui=template_obj.ui,
                is_active=template_obj.is_active,
                template_code=template_obj.template_code
            )
            
            return template
            
        except Exception as e:
            logger.error(f"查找策略模板失败: {str(e)}", exc_info=True)
            raise
    
    async def list_strategy_templates(
        self, 
        skip: int = 0, 
        limit: int = 10, 
        id: Optional[str] = None,
        strategy_type: Optional[str] = None,
        tags: Optional[List[str]] = None
    ) -> Tuple[List[Dict[str, Any]], int]:
        """
        获取策略模板列表
        
        Args:
            skip: 跳过的数量
            limit: 返回的数量上限
            id: 模板ID (MongoDB的_id)
            strategy_type: 策略功能类型过滤，可选值：filter-选股策略, timing-择时策略
            tags: 标签过滤，可以传入多个标签进行筛选
            
        Returns:
            策略模板列表和总数
        """
        try:
            from ..data.db.base import db_manager
            db = await db_manager.get_mongodb_database("quantcard")
            
            # 构建查询条件
            query = {}
            if id and ObjectId.is_valid(id):
                query["_id"] = ObjectId(id)
            if strategy_type:
                query["strategy_type"] = strategy_type
            if tags and len(tags) > 0:
                # 使用$in操作符支持多标签查询
                query["tags"] = {"$in": tags}
                
            # 获取总数
            total = await db.strategy_templates.count_documents(query)
            
            # 查询模板
            cursor = db.strategy_templates.find(query).skip(skip).limit(limit)
            templates = await cursor.to_list(length=limit)
            
            # 处理ObjectId，统一转为id字段
            for template in templates:
                if "_id" in template:
                    template["id"] = str(template["_id"])
                    del template["_id"]
                    
            return templates, total
            
        except Exception as e:
            logger.error(f"获取策略模板列表失败: {str(e)}", exc_info=True)
            raise
            
    async def get_strategy_template(self, id: str) -> Optional[Dict[str, Any]]:
        """
        获取策略模板详情
        
        Args:
            id: 模板ID (MongoDB的_id)
            
        Returns:
            策略模板详情，如果找不到则返回None
        """
        try:
            from ..data.db.base import db_manager
            db = await db_manager.get_mongodb_database("quantcard")
            
            # 查询模板
            template = None
            if ObjectId.is_valid(id):
                template = await db.strategy_templates.find_one({"_id": ObjectId(id)})
            
            if not template:
                return None
                
            # 处理ObjectId，统一转为id字段
            if "_id" in template:
                template["id"] = str(template["_id"])
                del template["_id"]
                
            return template
            
        except Exception as e:
            logger.error(f"获取策略模板详情失败: {str(e)}", exc_info=True)
            raise 