name: "价格过滤策略"
description: "根据股票价格筛选股票"
class_name: "PriceFilterStrategy"

# 数据源配置
data_sources:
  # 回测模式数据源
  backtest:
    database:
      type: "postgresql"
      table: "daily_quotation"
    fields:
      - "代码"
      - "名称"
      - "最新价"
      - "涨跌幅"
      - "最高价"
      - "最低价"
    frequency: "daily"

  # 选股模式数据源
  filter:
    database:
      type: "postgresql"
      table: "stock_basic_info"
    fields:
      - "代码"
      - "名称"
      - "最新价"
      - "涨跌幅"
      - "市盈率"
      - "市净率"
    frequency: "daily"

  # 监控模式数据源
  monitor:
    database:
      type: "postgresql"
      table: "realtime_quotation"
    fields:
      - "代码"
      - "名称"
      - "最新价"
      - "涨跌幅"
      - "成交量"
      - "成交额"
    frequency: "realtime"
    
  # 择时模式数据源
  timing:
    database:
      type: "realtime"
      table: "realtime_quotes"
    fields:
      - "代码"
      - "名称" 
      - "最新价"
    frequency: "realtime"

# 输出配置
outputs:
  # 回测模式输出
  backtest:
    - name: "代码"
      field: "symbol"
      type: "string"
    - name: "名称"
      field: "name"
      type: "string"
    - name: "方向"
      field: "direction"
      type: "string"
    - name: "最新价"
      field: "latest_price"
      type: "number"

  # 选股模式输出
  filter:
    - name: "代码"
      field: "symbol"
      type: "string"
    - name: "名称"
      field: "name"
      type: "string"
    - name: "方向"
      field: "direction"
      type: "string"
    - name: "最新价"
      field: "latest_price"
      type: "number"

  # 监控模式输出
  monitor:
    - name: "代码"
      field: "symbol"
      type: "string"
    - name: "名称"
      field: "name"
      type: "string"
    - name: "方向"
      field: "direction"
      type: "string"
    - name: "最新价"
      field: "latest_price"
      type: "number"
      
  # 择时模式输出
  timing:
    - name: "代码"
      field: "symbol"
      type: "string"
    - name: "名称"
      field: "name"
      type: "string"
    - name: "方向"
      field: "direction"
      type: "string"
    - name: "最新价"
      field: "latest_price"
      type: "number"
    - name: "置信度"
      field: "confidence"
      type: "number"
    - name: "触发条件"
      field: "trigger_condition"
      type: "string"
