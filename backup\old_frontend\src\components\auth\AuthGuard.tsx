import React, { useEffect, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { message } from 'antd';
import { authService } from '../../services/authService';
import Loading from '../common/Loading';
import { PUBLIC_ROUTES } from '../../routes';

interface AuthGuardProps {
  children: React.ReactNode;
}

const AuthGuard: React.FC<AuthGuardProps> = ({ children }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const [isLoading, setIsLoading] = useState(true);
  const token = localStorage.getItem('token');

  useEffect(() => {
    const checkAuth = async () => {
      if (!token) {
        navigate('/login', { state: { from: location.pathname } });
        setIsLoading(false);
        return;
      }

      try {
        await authService.getCurrentUser();
        setIsLoading(false);
      } catch (error: any) {
        console.error('认证检查错误:', error);
        if (error.message) {
          message.error(error.message);
        }
        localStorage.removeItem('token');
        navigate('/login', { state: { from: location.pathname } });
        setIsLoading(false);
      }
    };

    checkAuth();
  }, [token, location.pathname, navigate]);

  if (isLoading) {
    return <Loading fullscreen />;
  }

  return <>{children}</>;
};

export default AuthGuard; 