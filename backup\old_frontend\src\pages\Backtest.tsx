import React from 'react';
import { Card, Form, DatePicker, InputNumber, Select, Button, Space, Divider } from 'antd';
import { useSearchParams, useNavigate } from 'react-router-dom';
import PageTitle from '../components/common/PageTitle';

const { RangePicker } = DatePicker;
const { Option } = Select;

const Backtest: React.FC = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const strategyId = searchParams.get('strategyId');
  const [form] = Form.useForm();

  const handleSubmit = async (values: any) => {
    console.log('Backtest values:', values);
    // TODO: 实现回测逻辑
  };

  return (
    <div>
      <PageTitle
        title="回测分析"
        subtitle="运行策略回测并分析结果"
        breadcrumbs={[
          { label: '策略管理', path: '/strategies' },
          strategyId ? { label: '策略详情', path: `/strategies/${strategyId}` } : null,
          { label: '回测分析' },
        ].filter(Boolean)}
      />

      <Space direction="vertical" size={16} style={{ width: '100%' }}>
        <Card title="回测配置">
          <Form
            form={form}
            layout="vertical"
            onFinish={handleSubmit}
            initialValues={{
              initialBalance: 10000,
              leverage: 1,
            }}
          >
            <Form.Item
              label="回测周期"
              name="dateRange"
              rules={[{ required: true, message: '请选择回测周期' }]}
            >
              <RangePicker style={{ width: '100%' }} />
            </Form.Item>

            <Form.Item
              label="初始资金"
              name="initialBalance"
              rules={[{ required: true, message: '请输入初始资金' }]}
            >
              <InputNumber
                style={{ width: '100%' }}
                min={0}
                step={1000}
                formatter={value => `$ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                parser={value => value!.replace(/\$\s?|(,*)/g, '')}
              />
            </Form.Item>

            <Form.Item
              label="杠杆倍数"
              name="leverage"
              rules={[{ required: true, message: '请选择杠杆倍数' }]}
            >
              <Select>
                <Option value={1}>1x</Option>
                <Option value={2}>2x</Option>
                <Option value={3}>3x</Option>
                <Option value={5}>5x</Option>
                <Option value={10}>10x</Option>
              </Select>
            </Form.Item>

            <Form.Item
              label="手续费率"
              name="feeRate"
              rules={[{ required: true, message: '请输入手续费率' }]}
            >
              <InputNumber
                style={{ width: '100%' }}
                min={0}
                max={1}
                step={0.0001}
                formatter={value => `${value}%`}
                parser={value => value!.replace('%', '')}
              />
            </Form.Item>

            <Form.Item
              label="滑点"
              name="slippage"
              rules={[{ required: true, message: '请输入滑点' }]}
            >
              <InputNumber
                style={{ width: '100%' }}
                min={0}
                max={1}
                step={0.0001}
                formatter={value => `${value}%`}
                parser={value => value!.replace('%', '')}
              />
            </Form.Item>

            <Divider />

            <Form.Item>
              <Space>
                <Button type="primary" htmlType="submit">
                  开始回测
                </Button>
                <Button onClick={() => navigate(-1)}>
                  返回
                </Button>
              </Space>
            </Form.Item>
          </Form>
        </Card>

        <Card title="回测结果">
          {/* 这里后续添加回测结果展示，包括图表和统计数据 */}
          <div style={{ padding: '32px', textAlign: 'center' }}>
            回测结果将在这里显示
          </div>
        </Card>
      </Space>
    </div>
  );
};

export default Backtest; 