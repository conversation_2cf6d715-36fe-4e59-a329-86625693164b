/**
 * 🗺�?InteractiveWorldMap - 可触摸拖动的游戏世界地图
 * 支持手势操作、平滑缩放、区域点击和视觉特效
 */

import React, { useRef, useState, useCallback, useEffect } from 'react'
import { motion, useMotionValue, useTransform } from 'framer-motion'
import { useGesture } from '@use-gesture/react'
import { useSpring, animated, config } from 'react-spring'
import styled from 'styled-components'
import { buildTheme } from '../../../styles/themes/dual-theme'

const LightTheme = buildTheme('light')

interface MapArea {
  id: string
  name: string
  x: number  // 百分比位�?  y: number
  radius: number
  icon: string
  color: string
  description: string
  level: number
  unlocked: boolean
  onClick: () => void
}

interface InteractiveWorldMapProps {
  areas: MapArea[]
  initialScale?: number
  maxScale?: number
  minScale?: number
  onAreaClick?: (area: MapArea) => void
  className?: string
}

// 🗺️ 主地图容器
const MapContainer = styled.div`
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
  background: linear-gradient(
    135deg,
    ${LightTheme.colors.background.void} 0%,
    ${LightTheme.colors.background.surface} 50%,
    ${LightTheme.colors.background.elevated} 100%
  );
  touch-action: none;
  user-select: none;
  cursor: grab;
  
  &:active {
    cursor: grabbing;
  }
`

// 🌍 地图背景
const MapBackground = styled(motion.div)`
  position: absolute;
  top: 0;
  left: 0;
  width: 200%;
  height: 200%; /* 更大的背景用于拖拽 */
  background-image:
    /* 主要网格 */
    linear-gradient(${LightTheme.colors.primaryColors.cyan}20 2px, transparent 2px),
    linear-gradient(90deg, ${LightTheme.colors.primaryColors.cyan}20 2px, transparent 2px),
    /* 次要网格 */
    linear-gradient(${LightTheme.colors.primaryColors.cyan}10 1px, transparent 1px),
    linear-gradient(90deg, ${LightTheme.colors.primaryColors.cyan}10 1px, transparent 1px);
  background-size: 100px 100px, 100px 100px, 20px 20px, 20px 20px;
  z-index: 1;
`

// 🏔�?地形装饰元素
const TerrainElement = styled(motion.div)<{ type: 'mountain' | 'forest' | 'city' | 'water' }>`
  position: absolute;
  font-size: ${props => {
    const sizes = { mountain: '60px', forest: '40px', city: '50px', water: '30px' }
    return sizes[props.type]
  }};
  opacity: 0.3;
  z-index: 2;
  pointer-events: none;
  filter: blur(1px);
`

// 🎯 可点击区域
const ClickableArea = styled(motion.div)<{
  area: MapArea 
  isActive: boolean
  isUnlocked: boolean 
}>`
  position: absolute;
  width: ${props => props.area.radius * 2}px;
  height: ${props => props.area.radius * 2}px;
  border-radius: 50%;
  background: ${props => props.isUnlocked 
    ? `radial-gradient(circle, ${props.area.color}40 0%, ${props.area.color}20 50%, transparent 100%)`
    : 'radial-gradient(circle, #666640 0%, #66662020 50%, transparent 100%)'
  };
  border: 3px solid ${props => props.isUnlocked ? props.area.color : '#666'};
  cursor: ${props => props.isUnlocked ? 'pointer' : 'not-allowed'};
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  
  box-shadow: ${props => props.isActive 
    ? `0 0 30px ${props.area.color}, 0 0 60px ${props.area.color}40`
    : '0 4px 20px rgba(0,0,0,0.2)'
  };
  
  &:hover {
    transform: ${props => props.isUnlocked ? 'scale(1.1)' : 'none'};
    box-shadow: ${props => props.isUnlocked 
      ? `0 0 40px ${props.area.color}, 0 0 80px ${props.area.color}60`
      : '0 4px 20px rgba(0,0,0,0.2)'
    };
  }
  
  &::after {
    content: '';
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    border-radius: 50%;
    background: ${props => props.isUnlocked ? props.area.color : '#666'}20;
    z-index: -1;
    animation: ${props => props.isActive ? 'pulse 2s ease-in-out infinite' : 'none'};
  }
  
  @keyframes pulse {
    0%, 100% { transform: scale(1); opacity: 0.7; }
    50% { transform: scale(1.2); opacity: 0.3; }
  }
`

// 📍 区域标签
const AreaLabel = styled(motion.div)<{ isUnlocked: boolean }>`
  position: absolute;
  bottom: -40px;
  left: 50%;
  transform: translateX(-50%);
  background: ${LightTheme.colors.background.card};
  color: ${props => props.isUnlocked ? LightTheme.colors.neutral.charcoal : LightTheme.colors.neutral.midGray};
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  white-space: nowrap;
  border: 1px solid ${props => props.isUnlocked ? LightTheme.colors.primaryColors.cyan : LightTheme.colors.neutral.midGray};
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  pointer-events: none;
  
  &::before {
    content: '';
    position: absolute;
    top: -6px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-bottom: 6px solid ${LightTheme.colors.background.card};
  }
`

// 🔍 缩放控制
const ZoomControls = styled.div`
  position: absolute;
  bottom: 20px;
  right: 20px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  z-index: 100;
`

const ZoomButton = styled(motion.button)`
  width: 48px;
  height: 48px;
  border-radius: 50%;
  border: none;
  background: ${LightTheme.colors.background.card};
  color: ${LightTheme.colors.neutral.charcoal};
  font-size: 20px;
  font-weight: bold;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  display: flex;
  align-items: center;
  justify-content: center;
  
  &:hover {
    background: ${LightTheme.colors.background.elevated};
    transform: scale(1.05);
  }
  
  &:active {
    transform: scale(0.95);
  }
`

// 🎮 主组件
const InteractiveWorldMap: React.FC<InteractiveWorldMapProps> = ({
  areas,
  initialScale = 1,
  maxScale = 3,
  minScale = 0.5,
  onAreaClick,
  className
}) => {
  const mapRef = useRef<HTMLDivElement>(null)
  const [activeArea, setActiveArea] = useState<string | null>(null)
  
  // 🎯 动态值管理
  const [{ x, y, scale }, setTransform] = useSpring(() => ({
    x: 0,
    y: 0,
    scale: initialScale,
    config: config.gentle
  }))
  
  // 🤏 手势识别
  const bind = useGesture({
    onDrag: ({ offset: [ox, oy], delta: [dx, dy], memo }) => {
      const newX = ox
      const newY = oy
      
      // 边界限制
      const maxOffset = 300
      const clampedX = Math.max(-maxOffset, Math.min(maxOffset, newX))
      const clampedY = Math.max(-maxOffset, Math.min(maxOffset, newY))
      
      setTransform({ x: clampedX, y: clampedY })
      return memo
    },
    onPinch: ({ offset: [s], origin: [ox, oy] }) => {
      const newScale = Math.max(minScale, Math.min(maxScale, s))
      setTransform({ scale: newScale })
    },
    onWheel: ({ delta: [, dy], event }) => {
      event.preventDefault()
      const newScale = Math.max(
        minScale, 
        Math.min(maxScale, scale.get() - dy * 0.001)
      )
      setTransform({ scale: newScale })
    }
  }, {
    drag: { from: () => [x.get(), y.get()] },
    pinch: { scaleBounds: { min: minScale, max: maxScale }, rubberband: true }
  })
  
  // 🎯 区域点击处理
  const handleAreaClick = useCallback((area: MapArea) => {
    if (!area.unlocked) return
    
    setActiveArea(area.id)
    setTimeout(() => setActiveArea(null), 2000)
    
    onAreaClick?.(area)
    area.onClick()
  }, [onAreaClick])
  
  // 🔍 缩放控制
  const handleZoomIn = () => {
    setTransform({ scale: Math.min(maxScale, scale.get() * 1.2) })
  }
  
  const handleZoomOut = () => {
    setTransform({ scale: Math.max(minScale, scale.get() / 1.2) })
  }
  
  const handleResetView = () => {
    setTransform({ x: 0, y: 0, scale: initialScale })
  }
  
  // 🌍 地形装饰元素
  const terrainElements = [
    { type: 'mountain' as const, x: '15%', y: '20%', icon: '🏔️' },
    { type: 'forest' as const, x: '70%', y: '30%', icon: '🌲' },
    { type: 'city' as const, x: '45%', y: '60%', icon: '🏙️' },
    { type: 'water' as const, x: '80%', y: '70%', icon: '🌊' },
    { type: 'forest' as const, x: '25%', y: '80%', icon: '🌳' },
    { type: 'mountain' as const, x: '60%', y: '15%', icon: '⛰️' },
  ]
  
  return (
    <MapContainer ref={mapRef} className={className} {...bind()}>
      {/* 🌍 背景地图 */}
      <animated.div
        style={{
          transform: x.to(x => y.to(y => scale.to(s => 
            `translate3d(${x}px, ${y}px, 0) scale(${s})`
          ))),
          transformOrigin: 'center center',
          width: '100%',
          height: '100%',
          position: 'relative'
        }}
      >
        <MapBackground
          animate={{
            backgroundPosition: ['0px 0px', '100px 100px', '0px 0px']
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "linear"
          }}
        />
        
        {/* 🏔�?地形装饰 */}
        {terrainElements.map((terrain, index) => (
          <TerrainElement
            key={index}
            type={terrain.type}
            style={{
              left: terrain.x,
              top: terrain.y
            }}
            animate={{
              y: [0, -10, 0],
              opacity: [0.3, 0.5, 0.3]
            }}
            transition={{
              duration: 4 + index,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          >
            {terrain.icon}
          </TerrainElement>
        ))}
        
        {/* 🎯 可点击区�?*/}
        {areas.map((area) => (
          <ClickableArea
            key={area.id}
            area={area}
            isActive={activeArea === area.id}
            isUnlocked={area.unlocked}
            style={{
              left: `calc(${area.x}% - ${area.radius}px)`,
              top: `calc(${area.y}% - ${area.radius}px)`
            }}
            onClick={() => handleAreaClick(area)}
            whileHover={{ 
              scale: area.unlocked ? 1.1 : 1,
              transition: { duration: 0.2 }
            }}
            whileTap={{ 
              scale: area.unlocked ? 0.95 : 1,
              transition: { duration: 0.1 }
            }}
            initial={{ scale: 0, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ delay: 0.5, duration: 0.6 }}
          >
            <span style={{ 
              filter: area.unlocked ? 'none' : 'grayscale(100%)',
              fontSize: area.unlocked ? '32px' : '24px'
            }}>
              {area.unlocked ? area.icon : '🔒'}
            </span>
            
            <AreaLabel
              isUnlocked={area.unlocked}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.8, duration: 0.4 }}
            >
              {area.name} {area.unlocked ? `(Lv.${area.level})` : '未解锁'}
            </AreaLabel>
          </ClickableArea>
        ))}
      </animated.div>
      
      {/* 🔍 缩放控制 */}
      <ZoomControls>
        <ZoomButton
          onClick={handleZoomIn}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          +
        </ZoomButton>
        <ZoomButton
          onClick={handleZoomOut}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          -        </ZoomButton>
        <ZoomButton
          onClick={handleResetView}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          style={{ fontSize: '16px' }}
        >
          Reset        </ZoomButton>
      </ZoomControls>
    </MapContainer>
  )
}

export default InteractiveWorldMap
export type { MapArea, InteractiveWorldMapProps }
