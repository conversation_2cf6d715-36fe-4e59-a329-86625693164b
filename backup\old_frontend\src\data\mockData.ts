import { CandlestickData } from '../components/charts/CyberpunkCandlestick';

// 模拟数据生成函数
export const generateMockData = (): CandlestickData[] => {
  const data: CandlestickData[] = [];
  const now = new Date();
  for (let i = 30; i >= 0; i--) {
    const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
    const basePrice = 100 + Math.random() * 20;
    data.push({
      time: date.toISOString().split('T')[0],
      open: basePrice,
      close: basePrice + (Math.random() - 0.5) * 10,
      high: basePrice + Math.random() * 5,
      low: basePrice - Math.random() * 5,
      volume: Math.floor(Math.random() * 1000000),
      ma5: basePrice + Math.random() * 2,
      ma10: basePrice + Math.random() * 3,
      ma20: basePrice + Math.random() * 4,
    });
  }
  return data;
};

// 市场数据
export const mockMarketData = {
  bullishCount: 1523,
  bearishCount: 892,
  limitUpCount: 42,
  limitDownCount: 28,
  divergenceCount: 156,
  totalStocks: 3000,
  vix: 22.5,
  marketTrend: 'bullish' as const,
};

// 模拟公告数据
export const mockAnnouncements = [
  {
    id: '1',
    type: 'success' as const,
    title: '策略执行成功',
    content: '动量策略A1完成今日信号生成，共产生15个交易信号',
    timestamp: new Date().toISOString(),
    strategy: '动量策略A1',
    read: false,
  },
  {
    id: '2',
    type: 'warning' as const,
    title: '市场异常波动提醒',
    content: '检测到市场波动率超过预警阈值，建议关注仓位管理',
    timestamp: new Date(Date.now() - 3600000).toISOString(),
    read: true,
  },
  {
    id: '3',
    type: 'info' as const,
    title: '系统更新通知',
    content: '系统将于今晚进行例行维护，预计耗时2小时',
    timestamp: new Date(Date.now() - 7200000).toISOString(),
    read: true,
  },
];

// 定义仪表板组件的默认布局
export interface DashboardWidget {
  id: string;
  x: number;
  y: number;
  w: number;
  h: number;
  title: string;
  component: string;
}

export const defaultLayout: DashboardWidget[] = [
  { id: 'strategy-group', x: 0, y: 0, w: 6, h: 2, title: '我的策略组', component: 'strategy-group' },
  { id: 'chart', x: 9, y: 5, w: 3, h: 4, title: '行情蜡烛图', component: 'chart' },
  { id: 'heatmap', x: 0, y: 2, w: 6, h: 5, title: '板块热力图', component: 'heatmap' },
  { id: 'sentiment', x: 9, y: 1, w: 3, h: 4, title: '市场情绪', component: 'sentiment' },
  { id: 'announcements', x: 6, y: 1, w:3, h: 6, title: '系统公告', component: 'announcements' },
]; 