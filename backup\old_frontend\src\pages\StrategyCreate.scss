@use "sass:map";
@use "sass:color";
@use "../styles/themes/variables" as *;

.strategy-create {
  min-height: 100vh;
  background: linear-gradient(135deg, map.get($color-neutral, gray-50), map.get($color-neutral, gray-100));

  .strategy-create-content {
    height: calc(100vh - 64px);
    margin: map.get($spacing, 4);
    background: transparent;
    display: flex;
    gap: map.get($spacing, 4);

    .strategy-market-sider {
      background: map.get($color-neutral, white);
      border-radius: map.get($border-radius, lg);
      box-shadow: map.get($shadow, sm);
      padding: map.get($spacing, 4);
      overflow: hidden;
      
      .ant-layout-sider-children {
        height: 100%;
        overflow-y: auto;
        
        &::-webkit-scrollbar {
          width: 4px;
        }
        
        &::-webkit-scrollbar-track {
          background: map.get($color-neutral, gray-100);
        }
        
        &::-webkit-scrollbar-thumb {
          background: map.get($color-neutral, gray-300);
          border-radius: 2px;
          
          &:hover {
            background: map.get($color-neutral, gray-400);
          }
        }
      }
    }

    .strategy-workspace {
      background: map.get($color-neutral, white);
      border-radius: map.get($border-radius, lg);
      box-shadow: map.get($shadow, sm);
      padding: map.get($spacing, 4);
      display: flex;
      flex-direction: column;
      
      .workspace-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: map.get($spacing, 4);
        padding-bottom: map.get($spacing, 4);
        border-bottom: 1px solid map.get($color-neutral, gray-200);

        .save-strategy-btn {
          width: 40px;
          height: 40px;
          padding: 0;
          border-radius: map.get($border-radius, lg);
          transition: all 0.3s ease;
          display: flex;
          align-items: center;
          justify-content: center;

          .anticon {
            font-size: map.get($font-size, lg);
          }

          &:hover {
            transform: translateY(-2px);
            box-shadow: map.get($shadow, md);
          }
        }
      }
      
      .workspace-content {
        flex: 1;
        overflow: hidden;
        position: relative;
        
        .simple-list, .strategy-canvas {
          height: 100%;
          overflow-y: auto;
          
          &::-webkit-scrollbar {
            width: 4px;
          }
          
          &::-webkit-scrollbar-track {
            background: map.get($color-neutral, gray-100);
          }
          
          &::-webkit-scrollbar-thumb {
            background: map.get($color-neutral, gray-300);
            border-radius: 2px;
            
            &:hover {
              background: map.get($color-neutral, gray-400);
            }
          }
        }
      }

      .debug-console {
        position: fixed;
        bottom: 0;
        right: 0;
        width: 50%;
        height: 300px;
        background: rgba(map.get($color-neutral, gray-900), 0.95);
        backdrop-filter: blur(8px);
        border-top-left-radius: map.get($border-radius, lg);
        box-shadow: map.get($shadow, lg);
        z-index: 1000;
        display: flex;
        flex-direction: column;
        animation: slideUp 0.3s ease-out;

        .debug-console-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: map.get($spacing, 3) map.get($spacing, 4);
          border-bottom: 1px solid rgba(map.get($color-neutral, white), 0.1);

          h4 {
            margin: 0;
            color: map.get($color-neutral, white);
            font-size: map.get($font-size, base);
          }
        }

        .debug-console-content {
          flex: 1;
          overflow-y: auto;
          padding: map.get($spacing, 4);
          margin: 0;
          font-family: 'Fira Code', monospace;
          font-size: map.get($font-size, sm);
          line-height: 1.5;
          color: map.get($color-neutral, gray-200);
          white-space: pre-wrap;
          word-break: break-all;

          &::-webkit-scrollbar {
            width: 4px;
          }

          &::-webkit-scrollbar-track {
            background: rgba(map.get($color-neutral, white), 0.1);
          }

          &::-webkit-scrollbar-thumb {
            background: rgba(map.get($color-neutral, white), 0.3);
            border-radius: 2px;

            &:hover {
              background: rgba(map.get($color-neutral, white), 0.4);
            }
          }
        }
      }
    }
  }
}

// 抽屉样式
.ant-drawer {
  .strategy-config {
    .ant-form {
      .ant-form-item {
        margin-bottom: map.get($spacing, 4);
      }
    }
  }
}

// 引导样式
.ant-tour {
  .ant-tour-inner {
    background: rgba(map.get($color-neutral, white), 0.9);
    backdrop-filter: blur(8px);
    border: 1px solid map.get($color-neutral, gray-200);
    box-shadow: map.get($shadow, lg);
    
    .ant-tour-content {
      .ant-tour-title {
        color: map.get($color-neutral, gray-900);
        font-size: map.get($font-size, lg);
        font-weight: 600;
      }
      
      .ant-tour-description {
        color: map.get($color-neutral, gray-600);
        font-size: map.get($font-size, base);
      }
    }
    
    .ant-tour-buttons {
      .ant-btn {
        border-radius: map.get($border-radius, full);
        
        &-primary {
          background: $color-primary;
          border: none;
          
          &:hover {
            background: color.adjust($color-primary, $lightness: 10%);
          }
        }
      }
    }
  }
  
  .ant-tour-arrow {
    background: rgba(map.get($color-neutral, white), 0.9);
    backdrop-filter: blur(8px);
  }
}

// 画布模式样式
.strategy-canvas {
  min-height: 600px;
  background: rgba(map.get($color-neutral, gray-900), 0.2);
  border-radius: map.get($border-radius, xl);
  padding: map.get($spacing, 5);
  position: relative;
  border: 1px solid rgba(map.get($color-neutral, white), 0.1);
  
  .canvas-tools {
    position: absolute;
    top: map.get($spacing, 5);
    left: map.get($spacing, 5);
    z-index: 1;
  }

  .canvas-container {
    width: 100%;
    height: 100%;
    position: relative;
  }

  .node {
    position: absolute;
    background: rgba(map.get($color-neutral, gray-800), 0.9);
    border: 1px solid rgba(map.get($color-neutral, white), 0.1);
    border-radius: map.get($border-radius, lg);
    padding: map.get($spacing, 3);
    min-width: 200px;
    cursor: move;
    transition: map.get($transition, base);

    &.selected {
      border-color: $color-primary;
      box-shadow: 0 0 0 2px rgba($color-primary, 0.2);
    }

    .node-header {
      display: flex;
      align-items: center;
      margin-bottom: map.get($spacing, 2);

      .node-icon {
        margin-right: map.get($spacing, 2);
      }

      .node-title {
        flex: 1;
        font-weight: 500;
        color: map.get($color-neutral, white);
      }
    }

    .node-content {
      font-size: map.get($font-size, sm);
      color: rgba(map.get($color-neutral, white), 0.6);
    }

    .node-ports {
      display: flex;
      justify-content: space-between;
      margin-top: map.get($spacing, 3);

      .port {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background: rgba(map.get($color-neutral, white), 0.2);
        cursor: pointer;
        transition: map.get($transition, base);

        &:hover {
          background: $color-primary;
        }

        &.connected {
          background: map.get($color-functional, success);
        }
      }
    }
  }

  .connection {
    position: absolute;
    pointer-events: none;
    z-index: -1;

    path {
      stroke: $color-primary;
      stroke-width: 2;
      fill: none;
      filter: drop-shadow(0 0 3px rgba($color-primary, 0.3));
    }

    &.active {
      path {
        stroke: map.get($color-functional, success);
        stroke-dasharray: 5;
        animation: flow 1s linear infinite;
      }
    }
  }
}

@keyframes flow {
  from {
    stroke-dashoffset: 10;
  }
  to {
    stroke-dashoffset: 0;
  }
}

// 自定义滚动条样式
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

// 动画效果
.step-content {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Modal样式
.ant-modal {
  .ant-modal-content {
    border-radius: map.get($border-radius, lg);
    
    .ant-modal-header {
      border-bottom: 1px solid map.get($color-neutral, gray-200);
      padding: map.get($spacing, 4);
      border-radius: map.get($border-radius, lg) map.get($border-radius, lg) 0 0;
      
      .ant-modal-title {
        font-size: map.get($font-size, lg);
        font-weight: 600;
        color: map.get($color-neutral, gray-900);
      }
    }

    .ant-modal-body {
      padding: map.get($spacing, 4);
      
      p {
        color: map.get($color-neutral, gray-700);
        margin-bottom: 0;
      }
    }

    .ant-modal-footer {
      border-top: 1px solid map.get($color-neutral, gray-200);
      padding: map.get($spacing, 3) map.get($spacing, 4);
      
      .ant-btn {
        height: 32px;
        padding: 0 map.get($spacing, 3);
        border-radius: map.get($border-radius, lg);
        font-weight: 500;
        transition: all 0.3s ease;
        
        &-default {
          border-color: map.get($color-neutral, gray-300);
          color: map.get($color-neutral, gray-600);
          
          &:hover {
            color: $color-primary;
            border-color: $color-primary;
            background: rgba($color-primary, 0.05);
          }
        }
        
        &-primary {
          background: $color-primary;
          border-color: $color-primary;
          
          &:hover {
            background: color.adjust($color-primary, $lightness: -10%);
            border-color: color.adjust($color-primary, $lightness: -10%);
          }
        }
      }
    }
  }
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
} 