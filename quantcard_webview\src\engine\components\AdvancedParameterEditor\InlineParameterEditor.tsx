/**
 * 🎯 内联参数编辑器
 * 完整迁移旧前端InlineParameterEditor功能，支持参数分组、条件显示和游戏化UI
 */

import React, { memo, useMemo } from 'react';
import styled from 'styled-components';
import ParameterField from './ParameterField';
import ParameterGroup from './ParameterGroup';
import { getUngroupedVisibleParameters } from './utils';
import type { InlineParameterEditorProps } from './types';

const EditorContainer = styled.div<{ $layout: string }>`
  display: flex;
  flex-direction: column;
  gap: 1rem;
  
  ${props => props.$layout === 'horizontal' && `
    flex-direction: row;
    flex-wrap: wrap;
    align-items: flex-start;
  `}
  
  ${props => props.$layout === 'inline' && `
    flex-direction: row;
    flex-wrap: wrap;
    align-items: center;
    gap: 0.5rem;
  `}
`;

const UngroupedParameters = styled.div<{ $layout: string }>`
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  
  ${props => props.$layout === 'horizontal' && `
    flex-direction: row;
    flex-wrap: wrap;
    align-items: flex-start;
  `}
  
  ${props => props.$layout === 'inline' && `
    flex-direction: row;
    flex-wrap: wrap;
    align-items: center;
    gap: 0.5rem;
  `}
`;

const InlineParameterEditor: React.FC<InlineParameterEditorProps> = memo(({
  parameters,
  parameterGroups,
  values,
  onChange,
  layout = 'horizontal',
  compact = true,
  readOnly = false
}) => {
  // 处理参数变化
  const handleParameterChange = (name: string, value: any) => {
    onChange({ ...values, [name]: value });
  };

  // 获取未分组的参数（使用统一的工具函数）
  const ungroupedParams = useMemo(() => {
    return getUngroupedVisibleParameters(parameters, parameterGroups, values);
  }, [parameters, parameterGroups, values]);

  return (
    <EditorContainer $layout={layout}>
      {/* 渲染参数组 */}
      {parameterGroups && Object.entries(parameterGroups).map(([name, group]) => (
        <ParameterGroup
          key={name}
          group={group}
          parameters={parameters}
          values={values}
          onChange={handleParameterChange}
          readOnly={readOnly}
        />
      ))}

      {/* 渲染未分组的参数 */}
      {ungroupedParams.length > 0 && (
        <UngroupedParameters $layout={layout}>
          {ungroupedParams.map(([name, param]) => (
            <ParameterField
              key={name}
              name={name}
              parameter={param}
              value={values[name] ?? param.default}
              onChange={(value) => handleParameterChange(name, value)}
              compact={compact}
              readOnly={readOnly}
            />
          ))}
        </UngroupedParameters>
      )}
    </EditorContainer>
  );
});

InlineParameterEditor.displayName = 'InlineParameterEditor';

export default InlineParameterEditor; 