from datetime import datetime
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, EmailStr

class WidgetLayout(BaseModel):
    """小部件布局模型"""
    id: str
    x: int
    y: int
    w: int
    h: int
    title: str
    component: str

class DashboardLayout(BaseModel):
    """仪表板布局模型"""
    layout: List[WidgetLayout]

class UserBase(BaseModel):
    """用户基础信息"""
    username: str
    is_active: bool = True
    is_superuser: bool = False
    avatar: Optional[str] = None

class UserResponse(UserBase):
    """用户响应模型"""
    id: str
    email: Optional[EmailStr] = None
    created_at: datetime
    updated_at: datetime
    last_login: Optional[datetime] = None
    favorites: list[str] = []
    dashboard_layout: Optional[List[Dict[str, Any]]] = None

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class UserCreate(BaseModel):
    """用户创建模型"""
    username: str
    email: EmailStr
    password: str

class UserUpdate(BaseModel):
    """用户更新模型"""
    username: Optional[str] = None
    email: Optional[EmailStr] = None
    password: Optional[str] = None
    is_active: Optional[bool] = None
    dashboard_layout: Optional[List[Dict[str, Any]]] = None 