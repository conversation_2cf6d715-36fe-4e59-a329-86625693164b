/**
 * 🎨 双主题系统 - 深色赛博朋克 + 浅色现代风格
 * 支持动态主题切换和渐变动画效果
 */

import type { ThemeMode } from '../../types/game'

// 🌙 深色赛博朋克主题 (原主题)
export const DarkCyberTheme = {
  name: 'dark',
  colors: {
    // 主色调 - 兼容旧版本
    primary: '#00FFFF',      // 主色
    secondary: '#FF00FF',    // 次色
    accent: '#8A2BE2',       // 强调色
    
    // 主色系 - 霓虹光谱
    primaryColors: {
      cyan: '#00FFFF',      // 青色霓虹
      magenta: '#FF00FF',   // 品红霓虹  
      purple: '#8A2BE2',    // 电光紫
      gold: '#FFD700',      // 金色光辉
      orange: '#FF6B35',    // 橘色火焰
    },

    // 背景层次
    background: {
      primary: '#000008',   // 主背景 - 兼容旧版本
      secondary: '#0A0A0F', // 次背景 - 兼容旧版本
      tertiary: '#16213E',  // 第三背景 - 兼容旧版本
      card: '#16213E',      // 卡片面
      modal: 'rgba(0, 0, 0, 0.9)', // 模态框背景
      void: '#000008',      // 深空黑
      dark: '#0A0A0F',      // 暗夜蓝
      surface: '#1A1A2E',   // 表面层
      elevated: '#252842',  // 悬浮层
    },
    
    // 文本颜色
    text: {
      primary: '#FFFFFF',   // 主文本
      secondary: '#8892B0', // 次文本
      accent: '#00FFFF',    // 强调文本
      muted: 'rgba(255, 255, 255, 0.6)', // 弱化文本
    },
    
    // 边框颜色
    border: {
      primary: 'rgba(0, 245, 255, 0.3)',
      secondary: 'rgba(255, 255, 255, 0.1)',
    },

    // 中性色阶
    neutral: {
      white: '#FFFFFF',
      silver: '#C0C0C0',
      lightGray: '#8892B0',
      midGray: '#495670', 
      darkGray: '#2D3748',
      charcoal: '#1A202C',
    },

    // 稀有度系统
    rarity: {
      common: '#9CA3AF',    // 灰白
      rare: '#3B82F6',      // 蓝色
      epic: '#8B5CF6',      // 紫色
      legendary: '#F59E0B', // 橙色
      mythic: '#EF4444',    // 红色
    },

    // 功能色系
    semantic: {
      success: '#10B981',   // 成功绿
      warning: '#F59E0B',   // 警告橙
      error: '#EF4444',     // 错误红
      info: '#3B82F6',      // 信息蓝
      profit: '#22C55E',    // 盈利绿
      loss: '#EF4444',      // 亏损红
    },

    // 渐变系统
    gradients: {
      primary: 'linear-gradient(135deg, #00FFFF 0%, #FF00FF 100%)',
      secondary: 'linear-gradient(135deg, #8A2BE2 0%, #FF6B35 100%)',
      card: 'linear-gradient(135deg, #16213E 0%, #1A1A2E 100%)',
      void: 'linear-gradient(180deg, #000008 0%, #0A0A0F 100%)',
      legendary: 'linear-gradient(135deg, #FFD700 0%, #FF6B35 50%, #8A2BE2 100%)',
    },
  },
  
  // 字体系统 - 兼容旧版本
  fonts: {
    primary: '"Orbitron", "JetBrains Mono", monospace',
    secondary: '"Inter", sans-serif',
    mono: '"JetBrains Mono", monospace'
  },
  
  // 阴影系统
  shadows: {
    small: '0 2px 8px rgba(0, 245, 255, 0.15)',
    medium: '0 4px 20px rgba(0, 245, 255, 0.2)',
    large: '0 8px 40px rgba(0, 245, 255, 0.25)',
    glow: '0 0 20px rgba(0, 245, 255, 0.5)',
  },
  
  // 动画
  animations: {
    transition: '0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
  },
  
  typography: {
    fonts: {
      primary: '"Inter", "SF Pro Display", -apple-system, BlinkMacSystemFont, sans-serif',
      code: '"Fira Code", "JetBrains Mono", "SF Mono", Consolas, monospace',
      gaming: '"Orbitron", "Exo 2", "Rajdhani", sans-serif',
      numeric: '"Space Mono", "Roboto Mono", monospace',
    },
    weights: { light: 300, regular: 400, medium: 500, semiBold: 600, bold: 700, extraBold: 800 },
    sizes: {
      xs: '0.75rem', sm: '0.875rem', base: '1rem', lg: '1.125rem', xl: '1.25rem',
      '2xl': '1.5rem', '3xl': '1.875rem', '4xl': '2.25rem', '5xl': '3rem', '6xl': '4rem'
    }
  }
} as const

// ☀️ 浅色现代主题
export const LightModernTheme = {
  name: 'light',
  colors: {
    // 主色调 - 兼容旧版本
    primary: '#3b82f6',      // 主色
    secondary: '#8b5cf6',    // 次色
    accent: '#06b6d4',       // 强调色
    
    // 主色系 - 现代商务色彩
    primaryColors: {
      cyan: '#0891B2',      // 深青蓝
      magenta: '#C026D3',   // 深品红
      purple: '#7C3AED',    // 深紫色
      gold: '#D97706',      // 深金黄
      orange: '#EA580C',    // 深橙色
    },

    // 背景层次 - 浅色系
    background: {
      primary: '#FFFFFF',   // 主背景 - 兼容旧版本
      secondary: '#F8FAFC', // 次背景 - 兼容旧版本
      tertiary: '#F1F5F9',  // 第三背景 - 兼容旧版本
      card: 'rgba(255, 255, 255, 0.9)', // 卡片背景
      modal: 'rgba(255, 255, 255, 0.95)', // 模态框背景
      void: '#FAFAFA',      // 浅灰白
      dark: '#F9FAFB',      // 极浅灰
      surface: '#F3F4F6',   // 浅灰表面
      elevated: '#E5E7EB',  // 浅灰悬浮
    },
    
    // 文本颜色
    text: {
      primary: '#1E293B',   // 主文本
      secondary: '#64748B', // 次文本
      accent: '#3B82F6',    // 强调文本
      muted: 'rgba(30, 41, 59, 0.6)', // 弱化文本
    },
    
    // 边框颜色
    border: {
      primary: 'rgba(59, 130, 246, 0.2)',
      secondary: 'rgba(0, 0, 0, 0.1)',
    },

    // 中性色阶 - 浅色适配
    neutral: {
      white: '#FFFFFF',
      silver: '#9CA3AF',
      lightGray: '#6B7280',
      midGray: '#4B5563', 
      darkGray: '#374151',
      charcoal: '#1F2937',
    },

    // 稀有度系统 - 浅色版本
    rarity: {
      common: '#6B7280',    // 中灰
      rare: '#2563EB',      // 深蓝
      epic: '#7C3AED',      // 深紫
      legendary: '#D97706', // 深橙
      mythic: '#DC2626',    // 深红
    },

    // 功能色系 - 浅色适配
    semantic: {
      success: '#059669',   // 深绿
      warning: '#D97706',   // 深橙
      error: '#DC2626',     // 深红
      info: '#2563EB',      // 深蓝
      profit: '#16A34A',    // 深绿
      loss: '#DC2626',      // 深红
    },

    // 渐变系统 - 浅色版本
    gradients: {
      primary: 'linear-gradient(135deg, #0891B2 0%, #C026D3 100%)',
      secondary: 'linear-gradient(135deg, #7C3AED 0%, #EA580C 100%)',
      card: 'linear-gradient(135deg, #FFFFFF 0%, #F9FAFB 100%)',
      void: 'linear-gradient(180deg, #FAFAFA 0%, #F9FAFB 100%)',
      legendary: 'linear-gradient(135deg, #D97706 0%, #EA580C 50%, #7C3AED 100%)',
    },
  },

  // 字体系统 - 兼容旧版本
  fonts: {
    primary: '"Inter", -apple-system, BlinkMacSystemFont, sans-serif',
    secondary: '"SF Pro Display", "Helvetica Neue", sans-serif',
    mono: '"SF Mono", "Monaco", monospace'
  },
  
  // 阴影系统
  shadows: {
    small: '0 1px 3px rgba(0, 0, 0, 0.1)',
    medium: '0 4px 12px rgba(0, 0, 0, 0.15)',
    large: '0 8px 32px rgba(0, 0, 0, 0.12)',
    glow: '0 0 20px rgba(59, 130, 246, 0.3)',
  },
  
  // 动画
  animations: {
    transition: '0.2s cubic-bezier(0.4, 0, 0.2, 1)',
  },
  
  typography: {
    fonts: {
      primary: '"Inter", "SF Pro Display", -apple-system, BlinkMacSystemFont, sans-serif',
      code: '"Fira Code", "JetBrains Mono", "SF Mono", Consolas, monospace', 
      gaming: '"Inter", "SF Pro Display", sans-serif', // 浅色主题使用更商务的字体
      numeric: '"Inter", "Roboto", sans-serif',
    },
    weights: { light: 300, regular: 400, medium: 500, semiBold: 600, bold: 700, extraBold: 800 },
    sizes: {
      xs: '0.75rem', sm: '0.875rem', base: '1rem', lg: '1.125rem', xl: '1.25rem',
      '2xl': '1.5rem', '3xl': '1.875rem', '4xl': '2.25rem', '5xl': '3rem', '6xl': '4rem'
    }
  }
} as const

export const getTheme = (mode: ThemeMode) => {
  return mode === 'dark' ? DarkCyberTheme : LightModernTheme
}

// 🎨 通用设计系统 (两个主题共享)
export const CommonDesignSystem = {
  // 空间与尺寸系统
  spacing: {
    space: {
      0: '0', 1: '0.25rem', 2: '0.5rem', 3: '0.75rem', 4: '1rem', 5: '1.25rem',
      6: '1.5rem', 8: '2rem', 10: '2.5rem', 12: '3rem', 16: '4rem', 20: '5rem', 24: '6rem'
    },
    borderRadius: {
      none: '0', sm: '0.25rem', base: '0.5rem', lg: '0.75rem', xl: '1rem', '2xl': '1.5rem', full: '9999px'
    },
  },

  // 动画系统
  animations: {
    easings: {
      linear: 'linear',
      easeIn: 'cubic-bezier(0.4, 0, 1, 1)',
      easeOut: 'cubic-bezier(0, 0, 0.2, 1)', 
      easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
      bounce: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
      cyberpunk: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)',
    },
    durations: { fast: '150ms', normal: '250ms', slow: '350ms', slower: '500ms' },
  },

  // 响应式断点
  breakpoints: {
    xs: '320px', sm: '640px', md: '768px', lg: '1024px', xl: '1280px', '2xl': '1536px',
    up: (breakpoint: string) => `@media (min-width: ${breakpoint})`,
    down: (breakpoint: string) => `@media (max-width: ${breakpoint})`,
  },

  // 阴影系统 (动态生成)
  getShadows: (theme: typeof DarkCyberTheme | typeof LightModernTheme) => ({
    none: 'none',
    sm: theme.name === 'dark' 
      ? '0 1px 2px 0 rgba(0, 255, 255, 0.1)' 
      : '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
    base: theme.name === 'dark'
      ? '0 1px 3px 0 rgba(0, 255, 255, 0.2), 0 1px 2px 0 rgba(255, 0, 255, 0.1)'
      : '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
    lg: theme.name === 'dark'
      ? '0 10px 15px -3px rgba(0, 255, 255, 0.3), 0 4px 6px -2px rgba(255, 0, 255, 0.2)'
      : '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
    neonCyan: theme.name === 'dark'
      ? '0 0 20px rgba(0, 255, 255, 0.5), 0 0 40px rgba(0, 255, 255, 0.3)'
      : '0 0 20px rgba(8, 145, 178, 0.3), 0 0 40px rgba(8, 145, 178, 0.1)',
    cardGlow: theme.name === 'dark'
      ? '0 8px 32px rgba(0, 255, 255, 0.12), 0 4px 16px rgba(255, 0, 255, 0.08)'
      : '0 8px 32px rgba(8, 145, 178, 0.08), 0 4px 16px rgba(192, 38, 211, 0.04)',
  })
}

// 🎪 主题切换动画配置
export const themeTransition = {
  duration: 0.8,
  ease: [0.25, 0.46, 0.45, 0.94], // cyberpunk easing
}

// 🌟 完整主题对象构建器
export const buildTheme = (mode: ThemeMode) => {
  const baseTheme = getTheme(mode)
  return {
    ...baseTheme,
    ...CommonDesignSystem,
    shadows: CommonDesignSystem.getShadows(baseTheme),
    mode,
  }
}

// 🎭 默认导出
export const CyberTheme = buildTheme('dark') // 默认深色主题
export const ModernTheme = buildTheme('light') // 浅色主题

export default { DarkCyberTheme, LightModernTheme, CommonDesignSystem, buildTheme }