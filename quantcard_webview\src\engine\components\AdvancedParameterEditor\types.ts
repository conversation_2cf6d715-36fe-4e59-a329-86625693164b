/**
 * 🎯 高级参数编辑器类型定义
 * 统一使用templateSentence.ts中的类型定义，避免重复
 */

// 重新导出统一的类型定义
export type {
  ParameterDef as StrategyParameter,
  ParameterGroupDef as ParameterGroup
} from '../../../utils/templateSentence';

export interface ParameterFieldProps {
  name: string;
  parameter: StrategyParameter;
  value: any;
  onChange: (value: any) => void;
  compact?: boolean;
  readOnly?: boolean;
}

export interface InlineParameterEditorProps {
  parameters: Record<string, StrategyParameter>;
  parameterGroups?: Record<string, ParameterGroup>;
  values: Record<string, any>;
  onChange: (values: Record<string, any>) => void;
  layout?: 'horizontal' | 'vertical' | 'inline';
  compact?: boolean;
  showDescription?: boolean;
  readOnly?: boolean;
} 