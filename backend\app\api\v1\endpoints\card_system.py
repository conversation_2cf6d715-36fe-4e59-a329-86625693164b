"""
卡牌系统API端点
统一管理策略模板信息、用户图鉴收集状态和抽卡功能
"""
import random
import uuid
from typing import Dict, List, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query, Body
from pydantic import BaseModel, Field

from ....core.deps import get_current_user
from ....services.user_cards_service import UserCardsService
from ....models.strategy_template import StrategyTemplate
from ....models.user_cards import UserCards
from ....utils.response_utils import ResponseFormatter

router = APIRouter()

# 请求模型
class DrawCardRequest(BaseModel):
    count: int = Field(default=1, ge=1, le=10, description="抽卡次数")
    pack_type: str = Field(default="standard", description="卡包类型")

class CardCollectionResponse(BaseModel):
    template_id: str
    name: str
    description: str
    tags: List[str]
    rarity: str
    icon: str
    color: str
    category: str
    owned: bool
    owned_count: int
    total_acquired: int
    first_acquired: Optional[str] = None

@router.get("/codex")
async def get_card_codex(
    category: Optional[str] = Query(None, description="卡牌分类筛选"),
    rarity: Optional[str] = Query(None, description="稀有度筛选"),
    owned_only: bool = Query(False, description="仅显示已拥有"),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    获取卡牌图鉴 - 合并策略模板和用户库存信息
    """
    try:
        user_id = current_user["id"]
        
        # 获取所有策略模板
        collection = await StrategyTemplate.get_collection()
        filter_dict = {"is_active": True}
        
        if category:
            filter_dict["tags"] = {"$in": [category]}
            
        templates = []
        async for doc in collection.find(filter_dict):
            doc['id'] = str(doc.pop('_id'))
            templates.append(StrategyTemplate(**doc))
        
        # 获取用户库存
        user_cards = await UserCards.get_by_user_id(user_id)
        if user_cards:
            inventory_map = user_cards.cards
        else:
            inventory_map = {}
        
        # 构建图鉴响应
        codex_items = []
        for template in templates:
            quantity = inventory_map.get(template.template_id, 0)
            
            # 确定稀有度和UI配置
            rarity_from_tags = "common"
            if "rare" in template.tags:
                rarity_from_tags = "rare"
            elif "epic" in template.tags:
                rarity_from_tags = "epic"
            elif "legendary" in template.tags:
                rarity_from_tags = "legendary"
            elif "mythic" in template.tags:
                rarity_from_tags = "mythic"
            
            # 确定分类
            category_from_tags = "filter"
            if "择时" in template.tags or "timing" in template.tags:
                category_from_tags = "timing"
            elif "选股" in template.tags or "filter" in template.tags:
                category_from_tags = "filter"
            elif "风控" in template.tags or "risk" in template.tags:
                category_from_tags = "risk_management"
            
            # 应用筛选
            if rarity and rarity_from_tags != rarity:
                continue
            if owned_only and quantity <= 0:
                continue
            
            codex_item = CardCollectionResponse(
                template_id=template.template_id,
                name=template.name,
                description=template.description,
                tags=template.tags,
                rarity=rarity_from_tags,
                icon=template.ui.icon if template.ui and template.ui.icon else "📊",
                color=template.ui.color if template.ui and template.ui.color else "#1890ff",
                category=category_from_tags,
                owned=bool(quantity > 0),
                owned_count=quantity,
                total_acquired=quantity,  # 简化处理
                first_acquired=user_cards.created_at.isoformat() if user_cards and quantity > 0 else None
            )
            
            codex_items.append(codex_item)
        
        # 统计信息
        total_cards = len(codex_items)
        owned_cards = len([item for item in codex_items if item.owned])
        collection_rate = (owned_cards / total_cards * 100) if total_cards > 0 else 0
        
        return ResponseFormatter.success({
            "items": [item.dict() for item in codex_items],
            "stats": {
                "total_cards": total_cards,
                "owned_cards": owned_cards,
                "collection_rate": round(collection_rate, 1),
                "total_owned_count": sum(item.owned_count for item in codex_items)
            }
        }, "获取卡牌图鉴成功")
        
    except Exception as e:
        return ResponseFormatter.error(f"获取卡牌图鉴失败: {str(e)}")

@router.post("/draw")
async def draw_cards(
    request: DrawCardRequest,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    抽卡功能 - 随机获取策略卡牌
    """
    try:
        user_id = current_user["id"]
        
        # 使用新的抽卡服务
        result = await UserCardsService.draw_cards(user_id, request.count, request.pack_type)
        
        return result
        
    except Exception as e:
        return ResponseFormatter.error(f"抽卡失败: {str(e)}")

@router.get("/packs")
async def get_card_packs():
    """
    获取可用的卡包类型（公开接口，无需认证）
    """
    try:
        packs = [
            {
                "type": "standard",
                "name": "标准卡包",
                "description": "包含基础策略卡牌，适合新手",
                "icon": "🎴",
                "cost": {"type": "free", "amount": 0},
                "guaranteed_rarity": "common"
            },
            {
                "type": "premium",
                "name": "高级卡包",
                "description": "更高概率获得稀有卡牌",
                "icon": "✨",
                "cost": {"type": "coins", "amount": 100},
                "guaranteed_rarity": "rare"
            },
            {
                "type": "legendary",
                "name": "传说卡包",
                "description": "保证至少一张传说品质卡牌",
                "icon": "🌟",
                "cost": {"type": "gems", "amount": 10},
                "guaranteed_rarity": "legendary"
            }
        ]
        
        return ResponseFormatter.success({
            "packs": packs
        }, "获取卡包信息成功")
        
    except Exception as e:
        return ResponseFormatter.error(f"获取卡包信息失败: {str(e)}")

@router.get("/templates/{template_id}")
async def get_card_template_detail(
    template_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    获取策略卡牌模板详细信息
    """
    try:
        user_id = current_user["id"]
        
        # 获取策略模板
        template = await StrategyTemplate.find_by_template_id(template_id)
        if not template:
            return ResponseFormatter.error("卡牌模板不存在", code=404)
        
        # 获取用户库存信息
        user_cards = await UserCards.get_by_user_id(user_id)
        quantity = user_cards.get_card_quantity(template_id) if user_cards else 0
        
        # 构建详细信息
        detail = {
            "template_id": template.template_id,
            "name": template.name,
            "description": template.description,
            "version": template.version,
            "author": template.author,
            "stars": template.stars,
            "tags": template.tags,
            "parameters": template.parameters,
            "ui": template.ui.dict() if template.ui else {},
            "owned": bool(quantity > 0),
            "owned_count": quantity,
            "total_acquired": quantity,  # 简化处理
            "first_acquired": user_cards.created_at.isoformat() if user_cards and quantity > 0 else None,
            "created_at": template.created_at.isoformat(),
            "updated_at": template.updated_at.isoformat()
        }
        
        return ResponseFormatter.success(detail, "获取卡牌详情成功")
        
    except Exception as e:
        return ResponseFormatter.error(f"获取卡牌详情失败: {str(e)}") 