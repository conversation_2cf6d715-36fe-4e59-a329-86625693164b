.announcement-board {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

  .ant-card-head {
    border-bottom: 1px solid #f0f0f0;
    min-height: 48px;
    padding: 0 20px;

    .ant-card-head-title {
      padding: 12px 0;
      
      .anticon {
        color: #bd93f9;
      }
    }
  }

  .ant-card-body {
    padding: 10px 0;

    .ant-list {
      .announcement-item {
        padding: 16px 20px;
        cursor: pointer;
        transition: all 0.3s ease;
        border-bottom: 1px solid #f0f0f0;

        &:last-child {
          border-bottom: none;
        }

        &:hover {
          background: #f8f9fa;
        }

        &.unread {
          background: rgba(189, 147, 249, 0.05);

          &:hover {
            background: rgba(189, 147, 249, 0.1);
          }

          &::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 3px;
            background: #bd93f9;
          }
        }

        .ant-list-item-meta {
          align-items: flex-start;
          margin-bottom: 0;

          .ant-list-item-meta-avatar {
            margin-top: 4px;
            margin-right: 12px;
          }

          .ant-list-item-meta-title {
            margin-bottom: 10px;
            color: #282a36;

            .ant-tag {
              margin-left: 8px;
              font-size: 12px;
              line-height: 20px;
              height: 20px;
              padding: 0 6px;
            }
          }
        }

        .announcement-content {
          color: #6272a4;
          padding-left: 2px;

          .ant-typography {
            color: inherit;
            margin-bottom: 10px;
          }

          .announcement-footer {
            font-size: 12px;
            color: #6272a4;
            margin-top: 8px;

            .anticon {
              font-size: 12px;
            }
          }
        }
      }
    }
  }

  // 响应式调整
  @media (max-width: 576px) {
    .ant-card-head-title {
      font-size: 14px;
    }

    .announcement-item {
      padding: 12px 16px !important;

      .ant-list-item-meta-title {
        font-size: 14px;
      }

      .announcement-content {
        font-size: 12px;
      }
    }
  }
} 
 