## 概览

本文面向世界地图场景（quantcard_webview/src/engine/scenes/WorldMapScene.tsx）的3D实现做一次专业分析与落地建议，回答以下问题：
- 地图构建方式与粗糙/地平线不平滑的原因
- 地标模型替换为自定义3D模型应选的格式与规范
- 地表与地标的贴图制作方法与资产规范
- 给出可实施的改进路线（不修改代码，仅方案）

---

## 1) 当前世界地图3D构建方式与问题定位

- 技术栈：Three.js + @react-three/fiber + @react-three/drei
- 地球几何：icosahedronGeometry args=[4, 2] + meshPhongMaterial；外加一层线框 icosahedron
- 地标几何：cylinderGeometry args=[0.3, 0.5, 1, 8]
- 相机与控制：Perspective 相机 + OrbitControls

问题成因（为何地表“粗糙”、弧形地平线不够平滑）
- 轮廓由几何体的“剪影”决定。当前使用 icosahedron（正二十面体细分）且细分较低（2），球体的轮廓呈明显折线，导致“地平线”观感不圆润。
- 材质为 Phong，虽能平滑法线插值，但无法改变几何轮廓的折线本质；轮廓想变圆必须提高细分或换用更适配的几何体（sphereGeometry）/视角策略（半球+相机限制）。
- 地标圆柱切分段数仅 8，也带来明显的多边形感。

判定依据（代码摘录位置）
- 地球体：WorldMapScene.tsx 中 Earth3D 组件使用 <icosahedronGeometry args={[4, 2]} />
- 地标体：LandmarkMarker 组件使用 <cylinderGeometry ... segments=8 />

---

## 2) 改善“弧形地平线”与地表平滑度的三档方案

注：以下为效果与性能折中建议，供后续迭代时参考。

A. 最小成本（直接替换几何参数）
- 将地球改为 sphereGeometry，或提高 icosahedron 细分：
  - sphereGeometry args=[4, 32, 16]（中端设备推荐）
  - 或 icosahedronGeometry args=[4, 3]（适度细分）
- 材质：meshStandardMaterial + flatShading=false（平滑着色），替代 Phong，保持现代 PBR 路线和更稳的高光。
- 优点：实现简单；轮廓更圆润；立竿见影。
- 代价：三角面数相对增加（注意保持在移动端预算内）。

B. 视觉策略（半球+相机限制）
- 仅渲染上半球：sphereGeometry args=[4, 24, 12, 0, 2π, 0, π/2]
- 相机限制只看上半球：minPolarAngle≈π/6，maxPolarAngle≈π/2.2，minDistance≈8，maxDistance≈18
- 可叠加轻薄大气层（透明渐变球壳）弱化边缘锯齿感。
- 优点：在较低细分下也能获得“弧形地平线”的观感，性能优于全球高细分。
- 代价：用户无法从底部观察；更偏“俯视地球”的设计取向。

C. 视觉增强（可选）
- 添加淡淡的大气层壳体（半径略大于地球，透明蓝色渐变）。
- 后处理（FXAA/SSAA）与 Stars 背景配合，进一步柔化边缘感。

附：地标几何的快速提质
- 地标圆柱 segments 提升至 12～16；或使用简易几何组合（圆柱+圆锥/盒子），在<100面/个内获得更好的形体语言。

---

## 3) 将地标替换为自定义模型：选用什么格式？

首选：glTF 2.0（GLB/GLTF）
- GLB（二进制打包，一体化）优先：一个文件内包含网格、材质、贴图、动画，加载最稳，缓存友好。
- 生态最佳：three.js/drei 原生支持 useGLTF；广泛的DCC工具（Blender/Maya/Max）导出；配套压缩（Draco/Meshopt）与贴图压缩（KTX2/BasisU）。
- 移动端友好：体积可控，可用 gltf-transform/gltfpack 优化。

次选/中转：FBX/OBJ
- 仅用于制作管线的中间格式；上线前请转成 GLB。

交付规范（强烈建议遵守）
- 轴向/单位：Y-Up、Z-forward、1单位=1米（与 three.js 习惯一致）。
- 构图与基点：模型原点在“模型底面中心”，便于贴地摆放；模型自身“上方向”为 +Y，方便对齐地表法线。
- 尺寸与预算：单个地标 < 500 三角面（普通），重点地标 < 2K 三角面（谨慎）；合规 LOD 可选。
- 材质：尽量合并材质（1～2个），减少 draw call；PBR 参数规范化；避免巨纹理。
- 压缩：优先 Meshopt/Draco 网格压缩 + KTX2 纹理压缩。

放置与朝向说明（重要）
- 地标应沿 +Y 方向为“竖直方向”。
- 放置在球面时，用位置向量归一化得到法线 n=normalize(position)，将 +Y 对齐到 n 即可；若用 lookAt，注意 three.js 默认让 -Z 指向目标，如模型“侧向”朝外，则需额外旋转修正或用四元数 setFromUnitVectors((0,1,0), n)。

---

## 4) 地表与地标贴图制作方法（PBR 与卡通两套思路）

A. 地表（地球）贴图
- 风格选择：
  - 写实 PBR：Albedo/Color、Normal、Roughness（金属度基本为0）、AO；可选 Night（夜晚灯光）、Clouds（云层，单独半透明壳）。
  - 轻写实/卡通：以纯色/渐变+轻微噪声为主，减少纹理体积；配合 toon/standard 材质。
- 展开与投影：
  - 使用等距长方投影（Equirectangular）制作地球漫反射/法线贴图，匹配 sphereGeometry 的默认 UV。
- 分辨率与格式：
  - 建议 1024～2048（最多 2K），JPEG（漫反射）/PNG（带透明），优先 KTX2 压缩交付（BasisU ETC1S/UASTC）。
- 技术要点：
  - 颜色贴图 encoding=sRGB，法线/粗糙/AO 保持线性空间；启用 mipmap，合理 anisotropy（2～4）；wrap 设置重复或 clamp 视需求。
  - 若只追求“弧形地平线”，可不使用贴图，仅用渐变材质 + 半球策略即可。

B. 地标贴图
- 管线建议：轻量优先，贴图尽量合并为 1 张小型贴图（Texture Atlas），512～1024 即可。
- 风格选项：
  - 卡通/扁平：用基础色（BaseColor）+ 少量手绘阴影即可，配合 MeshToonMaterial 或 Standard + Outlines。
  - 写实轻量：BaseColor + Normal + Roughness，金属度大多为 0～0.3；避免高频噪声。
- 制作要点：
  - UV 合理铺展，重要面给足像素密度；减少接缝；一致的 texel density。
  - 可烘焙 AO/Curvature 供美术表现（在 BaseColor 中微弱体现，或作为单独 AO 纹理）。
- 体积控制与交付：
  - 贴图 < 512KB/张（KTX2 后更小）；总材质数尽量 1；合并网格/材质减少 draw call。

KTX2 压缩（推荐）
- 工具链：gltf-transform 或 BasisU。示例命令：
  - gltf-transform etc1s in.glb out.glb --slots "baseColorTexture,normalTexture,metallicRoughnessTexture,occlusionTexture,emissiveTexture"
  - gltf-transform uastc in.glb out.glb --level 2
- 说明：ETC1S 体积极优、质量中等；UASTC 质量更高、体积稍大。移动端兼容性好。

---

## 5) 资源组织结构与命名建议

public/
- models/
  - landmarks/ museum.glb, exchange.glb, tower.glb, center.glb, adventure.glb
  - props/ ...
- textures/
  - earth/ earth_basecolor_1k.ktx2, earth_normal_1k.ktx2, earth_rough_1k.ktx2, earth_ao_1k.ktx2, earth_night_1k.ktx2
  - common/ toon_ramp.png, noise_small.png

命名规范
- 贴图：<asset>_<slot>_<res>.<ext>（如 buildingA_basecolor_1k.ktx2）
- 模型：<category>_<name>.glb（如 landmark_museum.glb）

---

## 6) 接入要点（不改代码，仅供后续实现参考）

- Drei 加载：useGLTF('/models/landmarks/museum.glb')
- 对齐地表法线：
  - n = normalize(position)
  - q = new THREE.Quaternion().setFromUnitVectors(new THREE.Vector3(0,1,0), n)
  - group.quaternion.copy(q)
- 尺寸与位置：以当前 landmarks 的 position 为参考半径（≈4.xx），模型原点在底部中心，放置后可微升高 0.1～0.2 以避免与地表 Z-fighting。
- 材质空间：BaseColor 贴图设为 sRGB，其余线性；注意 renderer.outputColorSpace = SRGBColorSpace（three r152+）。

---

## 7) 建议的实施顺序（后续迭代可选）

1. 快速提质（1小时）
- 将地球换为 sphereGeometry 32×16，材质换 meshStandardMaterial flatShading=false。
- 地标圆柱 segments 提至 12～16。

2. 视觉策略（半天）
- 上半球渲染 + 相机极角限制，获得稳定“弧形地平线”。
- 可加轻薄大气层壳。

3. 自定义地标导入（1天）
- 输出 GLB（含 KTX2 压缩贴图），放入 public/models/landmarks。
- 替换 Cylinder 为 useGLTF 模型，按法线对齐。

4. 贴图与压缩（并行）
- 地球：1K/2K Equirectangular 贴图（或纯程序化）。
- 地标：合图+KTX2，控制体积与 draw call。

---

## 结论
- 粗糙与地平线不平滑的根因在于几何细分与轮廓；提升细分或采用“半球+相机限制”是最经济有效的方案。
- 地标自定义模型推荐 GLB（glTF 2.0），并配合 Meshopt/Draco 与 KTX2；控制三角面数与材质数量。
- 贴图制作遵循 PBR 轻量化与移动端优先原则：小分辨率、合图、压缩、正确的色彩空间设置。

