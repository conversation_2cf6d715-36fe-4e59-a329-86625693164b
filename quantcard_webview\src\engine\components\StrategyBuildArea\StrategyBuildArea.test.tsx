/**
 * 🧪 策略构建区组件测试
 */

import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import StrategyBuildArea from './index';
import type { StrategyGroup } from '../../../types/game';

// Mock dependencies
jest.mock('../UniversalCard', () => ({
  UniversalCard: ({ card }: any) => <div data-testid={`card-${card.id}`}>{card.name}</div>
}));

jest.mock('../../../utils/templateSentence', () => ({
  buildCoreSentence: () => 'Mock core sentence'
}));

jest.mock('../AdvancedParameterEditor/ParameterModal', () => ({
  __esModule: true,
  default: ({ open, onClose }: any) => 
    open ? <div data-testid="parameter-modal" onClick={onClose}>Parameter Modal</div> : null
}));

jest.mock('../../../services/api/strategy', () => ({
  fetchStrategyTemplate: jest.fn().mockResolvedValue({
    data: { parameters: {}, parameterGroups: {} }
  })
}));

const mockGroup: StrategyGroup = {
  id: 'test-group',
  name: '测试策略',
  description: '测试描述',
  cards: [
    {
      id: 'card-1',
      template_id: 'test-template-1',
      parameters: {},
      position: { x: 0, y: 0 },
      created_at: new Date(),
      order: 0
    }
  ],
  execution_mode: 'sequential',
  status: 'inactive',
  performance: {
    total_return: 0,
    win_rate: 0,
    max_drawdown: 0,
    sharpe_ratio: 0
  },
  created_at: new Date(),
  updated_at: new Date()
};

const mockTemplateCache = {
  'test-template-1': {
    id: 'test-template-1',
    name: '测试卡牌',
    description: '测试卡牌描述',
    category: 'timing',
    rarity: 'common',
    parameters: {},
    parameterGroups: {}
  }
};

const defaultProps = {
  currentGroup: mockGroup,
  templateCache: mockTemplateCache,
  isDragOver: false,
  onCardAdd: jest.fn(),
  onCardRemove: jest.fn(),
  onCardUpdate: jest.fn(),
  onDragOver: jest.fn(),
  onDragLeave: jest.fn(),
  onDrop: jest.fn()
};

describe('StrategyBuildArea', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders cards correctly', () => {
    render(<StrategyBuildArea {...defaultProps} />);
    expect(screen.getByTestId('card-test-template-1')).toBeInTheDocument();
  });

  test('shows empty state when no cards', () => {
    const emptyGroup = { ...mockGroup, cards: [] };
    render(<StrategyBuildArea {...defaultProps} currentGroup={emptyGroup} />);
    expect(screen.getByText('拖拽卡片到此处')).toBeInTheDocument();
  });

  test('handles card removal', () => {
    render(<StrategyBuildArea {...defaultProps} />);
    const removeButton = screen.getByText('−');
    fireEvent.click(removeButton);
    expect(defaultProps.onCardRemove).toHaveBeenCalledWith('card-1');
  });

  test('handles drag over state', () => {
    const { container } = render(<StrategyBuildArea {...defaultProps} isDragOver={true} />);
    const cardsArea = container.querySelector('.drag-over');
    expect(cardsArea).toBeInTheDocument();
  });
});
