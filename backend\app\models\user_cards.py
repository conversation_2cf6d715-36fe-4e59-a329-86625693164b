"""
简化的用户策略卡牌模型
将每个用户的所有策略卡信息存储在单个文档中
"""
from datetime import datetime
from typing import Dict, Any, Optional, List
from pydantic import BaseModel, Field, validator
from app.core.base_model import BaseDBModel

class UserCards(BaseDBModel):
    """用户策略卡库存模型 - 简化版"""
    user_id: str = Field(..., description="用户ID")
    cards: Dict[str, int] = Field(default_factory=dict, description="卡牌数量映射 {template_id: quantity}")
    total_cards: int = Field(default=0, description="总卡牌数量")
    total_drawn: int = Field(default=0, description="累计抽卡数量")
    total_used: int = Field(default=0, description="累计使用数量")
    last_draw_time: Optional[datetime] = Field(None, description="最后抽卡时间")
    
    @validator('total_cards', always=True)
    def calculate_total_cards(cls, v, values):
        """自动计算总卡牌数量"""
        cards = values.get('cards', {})
        return sum(cards.values())

    class Config:
        arbitrary_types_allowed = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

    @classmethod
    def get_collection_name(cls) -> str:
        return "user_cards"

    @classmethod
    async def get_by_user_id(cls, user_id: str) -> Optional["UserCards"]:
        """获取用户卡牌库存"""
        return await cls.find_one({"user_id": user_id})

    async def add_cards(self, template_id: str, quantity: int) -> "UserCards":
        """添加卡牌"""
        current_qty = self.cards.get(template_id, 0)
        self.cards[template_id] = current_qty + quantity
        self.total_drawn += quantity
        self.last_draw_time = datetime.utcnow()
        self.updated_at = datetime.utcnow()
        await self.save()
        return self

    async def use_cards(self, template_id: str, quantity: int) -> bool:
        """使用卡牌"""
        current_qty = self.cards.get(template_id, 0)
        if current_qty < quantity:
            return False
        
        self.cards[template_id] = current_qty - quantity
        if self.cards[template_id] == 0:
            del self.cards[template_id]  # 移除数量为0的卡牌
        
        self.total_used += quantity
        self.updated_at = datetime.utcnow()
        await self.save()
        return True

    def get_card_quantity(self, template_id: str) -> int:
        """获取指定卡牌数量"""
        return self.cards.get(template_id, 0)

    def has_cards(self, template_id: str, quantity: int = 1) -> bool:
        """检查是否拥有足够数量的卡牌"""
        return self.get_card_quantity(template_id) >= quantity

    def to_inventory_format(self) -> List[Dict[str, Any]]:
        """转换为前端期望的库存格式"""
        items = []
        for template_id, quantity in self.cards.items():
            items.append({
                "template_id": template_id,
                "quantity": quantity,
                "acquired_at": self.created_at,
                "total_acquired": quantity,  # 简化处理
                "source": "various"
            })
        return items

class UserCardLog(BaseDBModel):
    """用户卡牌操作日志 - 可选"""
    user_id: str = Field(..., description="用户ID")
    operation: str = Field(..., description="操作类型: draw|use|grant|admin")
    changes: Dict[str, int] = Field(..., description="卡牌变化 {template_id: delta}")
    source: str = Field(..., description="操作来源")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="额外信息")
    
    class Config:
        arbitrary_types_allowed = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

    @classmethod
    def get_collection_name(cls) -> str:
        return "user_card_logs"

    @classmethod
    async def log_operation(cls, user_id: str, operation: str, changes: Dict[str, int], 
                           source: str, metadata: Dict[str, Any] = None) -> "UserCardLog":
        """记录操作日志"""
        log = cls(
            user_id=user_id,
            operation=operation,
            changes=changes,
            source=source,
            metadata=metadata or {}
        )
        await log.save()
        return log 