"""
分钟K线数据服务模块

提供东方财富分钟K线数据的管理功能
"""
import logging
from typing import List, Optional, Dict, Any, Union, Tuple
import pandas as pd
import asyncio
from datetime import datetime, timedelta
import traceback

from ..core.data.db.base import db_manager
# 代理服务暂时注释，保留未来使用可能性
# from .proxy_service import ProxyService

logger = logging.getLogger(__name__)

class MinuteKlineService:
    """分钟K线数据服务，用于获取东方财富分钟K线数据"""
    
    VALID_PERIODS = ['1min', '5min', '15min', '30min', '60min']
    
    _instances = {}
    
    @classmethod
    def get_instance(cls, *args, **kwargs):
        """获取单例实例"""
        if cls not in cls._instances:
            cls._instances[cls] = cls(*args, **kwargs)
        return cls._instances[cls]
    
    def __init__(self):
        """初始化分钟K线数据服务"""
        self.table_name = "minute_klines"
        # 初始化内存缓存
        self._symbol_cache = {}  # 按股票代码缓存的完整数据
        self._last_fetch_time = {}  # 记录每个股票+周期的上次获取时间
        # 代理服务相关代码暂时注释，保留未来使用可能性
        # # 获取代理服务实例
        # self.proxy_service = ProxyService.get_instance()
        # # 是否启用代理
        # self.use_proxy = True
        # # 代理失败重试次数
        # self.proxy_retry_count = 3
        
    def _get_table_name(self, period: str) -> str:
        """获取表名"""
        # 验证周期
        if period not in self.VALID_PERIODS:
            raise ValueError(f"不支持的K线周期: {period}，支持的周期: {', '.join(self.VALID_PERIODS)}")
        
        return f"kline_{period}"
        
    async def get_klines_batch(self, symbols: List[str], 
                              period: str = '5min',
                              from_time: Optional[datetime] = None,
                              to_time: Optional[datetime] = None,
                              limit: int = 1000,
                              incremental: bool = False,
                              adjust: str = "qfq",
                              save_to_db: bool = True) -> Dict[str, pd.DataFrame]:
        """批量获取多个标的的K线数据 - 优化版"""
        if not symbols:
            return {}
        
        current_time = datetime.now()
        table_name = self._get_table_name(period)
        result = {}
        cache_key = f"klines_{','.join(symbols)}_{period}"
        
        # 增量模式：使用上次获取数据的时间作为起始时间
        if incremental:
            for symbol in symbols:
                symbol_period_key = f"{symbol}_{period}"
                if symbol_period_key in self._last_fetch_time:
                    # 获取上次获取时间之后的数据
                    last_time = self._last_fetch_time[symbol_period_key]
                    # 向前偏移5分钟，确保不遗漏数据
                    from_time_inc = last_time - timedelta(minutes=5)
                    
                    # 获取单个标的的增量数据 - 不传递to_time以获取最新数据
                    df = await self._fetch_single_kline(symbol, period, from_time_inc, None, adjust)
                    
                    if df is not None and not df.empty:
                        # 如果已有缓存，则合并新数据
                        if symbol_period_key in self._symbol_cache:
                            # 合并数据并去重
                            cached_df = self._symbol_cache[symbol_period_key]
                            combined_df = pd.concat([cached_df, df]).drop_duplicates(subset=['time']).sort_values('time')
                            # 更新缓存
                            self._symbol_cache[symbol_period_key] = combined_df
                            result[symbol] = combined_df
                        else:
                            # 没有缓存，直接使用新获取的数据
                            self._symbol_cache[symbol_period_key] = df
                            result[symbol] = df
                    elif symbol_period_key in self._symbol_cache:
                        # 没有新数据但有缓存，使用缓存
                        result[symbol] = self._symbol_cache[symbol_period_key]
        else:
            # 非增量模式：优先从缓存获取数据
            cache_data = db_manager.get_questdb_cache(cache_key)
            if cache_data:
                cache_time = cache_data.get('time', datetime.min)
                # 缓存有效期5分钟
                if (current_time - cache_time).total_seconds() < 300:
                    return cache_data.get('data', {})
                
            # 没有缓存或缓存过期，从API并行获取数据
            tasks = []
            for symbol in symbols:
                task = asyncio.create_task(self._fetch_single_kline(symbol, period, from_time, None, adjust))
                tasks.append((symbol, task))
            
            # 等待所有任务完成
            for symbol, task in tasks:
                try:
                    df = await task
                    if df is not None and not df.empty:
                        # 更新最后获取时间和缓存
                        symbol_period_key = f"{symbol}_{period}"
                        self._last_fetch_time[symbol_period_key] = current_time
                        self._symbol_cache[symbol_period_key] = df
                        result[symbol] = df
                except Exception as e:
                    logger.error(f"获取标的 {symbol} 的K线数据失败: {str(e)}")
            
            # 缓存结果
            if result:
                db_manager.set_questdb_cache(cache_key, {
                    'time': current_time,
                    'data': result
                })
                # 设置缓存过期（5分钟后）
                asyncio.create_task(self._expire_cache(cache_key, 300))
        
        return result
    
    async def _expire_cache(self, key: str, seconds: int):
        """异步过期缓存"""
        await asyncio.sleep(seconds)
        db_manager.clear_questdb_cache(key)
    
    async def _fetch_single_kline(self, symbol: str, period: str, 
                                 from_time: Optional[datetime] = None, 
                                 to_time: Optional[datetime] = None,
                                 adjust: str = "qfq") -> Optional[pd.DataFrame]:
        """获取单个标的的K线数据 - 优化版"""
        # 导入AKShare
        import akshare as ak
        
        # 处理周期格式
        period_num = period.replace('min', '')
        
        # 处理时间范围 - 根据需求调整不同周期的回溯天数
        days_lookup = {
            '1min': 1,   # 1分钟K线获取最近1天
            '5min': 3,   # 5分钟K线获取最近3天
            '15min': 7,  # 15分钟K线获取最近7天
            '30min': 14, # 30分钟K线获取最近14天
            '60min': 30  # 60分钟K线获取最近30天
        }
        
        if not from_time:
            # 根据周期设置合适的回溯时间
            days = days_lookup.get(period, 7)
            from_time = datetime.now() - timedelta(days=days)
        
        # 转换开始时间格式
        start_date_str = from_time.strftime("%Y-%m-%d %H:%M:%S")
        
        # 代理服务相关代码暂时注释，保留未来使用可能性
        # # 如果启用代理，则使用代理获取数据
        # retry_count = self.proxy_retry_count if self.use_proxy else 1
        # last_exception = None
        # 
        # for attempt in range(retry_count):
        #     try:
        #         proxies = self.proxy_service.get_request_proxies() if self.use_proxy else None
        #         if proxies:
        #             logger.info(f"尝试 #{attempt+1}: 使用代理 {proxies.get('http')} 获取标的 {symbol} 的 {period} 数据")
        #         
        #         # 使用asyncio.to_thread在线程池中执行同步函数，不传递end_date参数
        #         stock_data = await asyncio.to_thread(
        #             ak.stock_zh_a_hist_min_em,
        #             symbol=symbol, 
        #             period=period_num,
        #             start_date=start_date_str,
        #             adjust=adjust,  # 前复权
        #             proxies=proxies  # 传递代理
        #         )
        #         
        #         if stock_data is None or stock_data.empty:
        #             logger.warning(f"标的 {symbol} 的 {period} 数据返回为空")
        #             if self.use_proxy and proxies:
        #                 # 报告代理失败并尝试获取新代理
        #                 proxy_str = proxies.get('http', '').replace('http://', '')
        #                 self.proxy_service.report_proxy_failure(proxy_str)
        #             continue
        #             
        #         logger.info(f"成功获取标的 {symbol} 的 {period} 数据，共 {len(stock_data)} 条记录")
        #         
        #         # 转换列名与数据库字段对应并提前进行类型转换
        #         stock_data = stock_data.rename(columns={
        #             '时间': 'time',
        #             '开盘': 'open',
        #             '收盘': 'close',
        #             '最高': 'high',
        #             '最低': 'low',
        #             '成交量': 'volume',
        #             '成交额': 'amount',
        #             '涨跌幅': 'change_pct',
        #             '涨跌额': 'change_price',
        #             '振幅': 'amplitude',
        #             '换手率': 'turnover'
        #         })
        #         
        #         # 添加股票代码和其他信息
        #         stock_data['symbol'] = symbol
        #         stock_data['adjust_type'] = adjust
        #         stock_data['data_source'] = "eastmoney"
        #         
        #         # 确保时间列是datetime类型 - 只做一次转换
        #         stock_data['time'] = pd.to_datetime(stock_data['time'])
        #         
        #         # 确保数值列是数值类型 - 提前转换
        #         numeric_columns = ['open', 'high', 'low', 'close', 'volume', 'amount', 'amplitude', 'turnover']
        #         existing_numeric_cols = [col for col in numeric_columns if col in stock_data.columns]
        #         if existing_numeric_cols:
        #             stock_data[existing_numeric_cols] = stock_data[existing_numeric_cols].astype(float)
        #         
        #         # 更新最后获取时间
        #         symbol_period_key = f"{symbol}_{period}"
        #         self._last_fetch_time[symbol_period_key] = datetime.now()
        #         
        #         return stock_data
        #         
        #     except Exception as e:
        #         last_exception = e
        #         logger.warning(f"尝试 #{attempt+1}: 获取标的 {symbol} 的 {period} 数据失败: {str(e)}")
        #         
        #         if self.use_proxy and proxies:
        #             # 报告代理失败并尝试获取新代理
        #             proxy_str = proxies.get('http', '').replace('http://', '')
        #             self.proxy_service.report_proxy_failure(proxy_str)
        # 
        # # 所有重试都失败
        # logger.error(f"获取标的 {symbol} 的 {period} 数据失败，已重试 {retry_count} 次: {str(last_exception)}")
        # return None
        
        # 使用原有代码，不使用代理
        try:
            # 使用asyncio.to_thread在线程池中执行同步函数，不传递end_date参数
            stock_data = await asyncio.to_thread(
                ak.stock_zh_a_hist_min_em,
                symbol=symbol, 
                period=period_num,
                start_date=start_date_str,
                adjust=adjust  # 前复权
            )
            
            if stock_data is None or stock_data.empty:
                return None
                
            logger.info(f"成功获取标的 {symbol} 的 {period} 数据，共 {len(stock_data)} 条记录")
            
            # 转换列名与数据库字段对应并提前进行类型转换
            stock_data = stock_data.rename(columns={
                '时间': 'time',
                '开盘': 'open',
                '收盘': 'close',
                '最高': 'high',
                '最低': 'low',
                '成交量': 'volume',
                '成交额': 'amount',
                '涨跌幅': 'change_pct',
                '涨跌额': 'change_price',
                '振幅': 'amplitude',
                '换手率': 'turnover'
            })
            
            # 添加股票代码和其他信息
            stock_data['symbol'] = symbol
            stock_data['adjust_type'] = adjust
            stock_data['data_source'] = "eastmoney"
            
            # 确保时间列是datetime类型 - 只做一次转换
            stock_data['time'] = pd.to_datetime(stock_data['time'])
            
            # 确保数值列是数值类型 - 提前转换
            numeric_columns = ['open', 'high', 'low', 'close', 'volume', 'amount', 'amplitude', 'turnover']
            existing_numeric_cols = [col for col in numeric_columns if col in stock_data.columns]
            if existing_numeric_cols:
                stock_data[existing_numeric_cols] = stock_data[existing_numeric_cols].astype(float)
            
            # 更新最后获取时间
            symbol_period_key = f"{symbol}_{period}"
            self._last_fetch_time[symbol_period_key] = datetime.now()
            
            return stock_data
            
        except Exception as e:
            logger.error(f"获取标的 {symbol} 的 {period} 数据失败: {str(e)}")
            return None
    
    async def get_klines_db(self, symbols: List[str], 
                           period: str = '5min',
                           from_time: Optional[datetime] = None,
                           to_time: Optional[datetime] = None,
                           limit: int = 1000) -> Dict[str, pd.DataFrame]:
        """从数据库获取K线数据 - 优化版"""
        if not symbols:
            return {}
            
        if not to_time:
            to_time = datetime.now()
            
        if not from_time:
            from_time = to_time - timedelta(days=30)
            
        try:
            # 获取表名
            table_name = self._get_table_name(period)
            
            # 创建结果容器
            result = {}
            
            # 构建SQL查询 - 优化查询方式
            symbols_str = "','".join(symbols)
            query = f"""
            SELECT * FROM {table_name} 
            WHERE symbol IN ('{symbols_str}')
            AND time >= '{from_time.isoformat()}'
            AND time <= '{to_time.isoformat()}'
            ORDER BY symbol, time
            LIMIT {limit}
            """
            
            # 执行查询并一次性转换数据类型
            df = await db_manager.execute_questdb_query(query)
            
            if df.empty:
                return {}
            
            # 提前进行所有必要的类型转换
            if 'time' in df.columns:
                df['time'] = pd.to_datetime(df['time'])
                
            # 数值列一次性转换
            numeric_columns = ['open', 'high', 'low', 'close', 'volume', 'amount', 'amplitude', 'turnover']
            existing_numeric_cols = [col for col in numeric_columns if col in df.columns]
            if existing_numeric_cols:
                df[existing_numeric_cols] = df[existing_numeric_cols].astype(float)
                
            # 高效地按股票代码分组
            for symbol in symbols:
                symbol_df = df[df['symbol'] == symbol]
                if not symbol_df.empty:
                    result[symbol] = symbol_df.copy()  # 使用copy避免视图引用问题
                    
            return result
            
        except Exception as e:
            logger.error(f"从数据库获取K线数据失败: {str(e)}")
            return {}
            
    async def get_last_kline_time(self, symbol: str, period: str) -> Optional[datetime]:
        """获取数据库中最后一条K线数据的时间"""
        try:
            table_name = self._get_table_name(period)
            
            query = f"""
            SELECT MAX(time) FROM {table_name}
            WHERE symbol = '{symbol}'
            """
            
            # 执行查询
            conn = await db_manager.get_questdb_connection()
            with conn.cursor() as cursor:
                cursor.execute(query)
                result = cursor.fetchone()
                
            if result and result[0]:
                return result[0]
            return None
        except Exception as e:
            logger.error(f"获取最后K线时间失败: {str(e)}")
            return None
    
    async def get_initial_klines(self, symbols: List[str], period: str = '5min', 
                                days: int = 30, adjust: str = "qfq") -> Dict[str, pd.DataFrame]:
        """
        获取初始K线数据（大范围历史数据）
        
        Args:
            symbols: 股票代码列表
            period: K线周期
            days: 回溯天数，默认30天
            adjust: 复权模式，qfq-前复权，hfq-后复权，空字符串-不复权
            
        Returns:
            Dict[str, pd.DataFrame]: 股票代码到数据的映射
        """
        to_time = datetime.now()
        from_time = to_time - timedelta(days=days)
        
        logger.info(f"获取初始大范围历史数据: {len(symbols)}个标的, 周期={period}, 天数={days}")
        
        # 使用非增量模式获取大范围历史数据
        result = await self.get_klines_batch(
            symbols=symbols,
            period=period,
            from_time=from_time,
            to_time=to_time,
            adjust=adjust
        )
        
        return result 