/**
 * 🏗️ Store类型定义 - 简化版
 * 为slice架构提供核心类型，支持原生immutable更新
 */

import type { StateCreator } from 'zustand'
import type { GameSession, InventoryItem, StrategyGroup, GameScene } from '../../types/game'
import type { AuthSlice } from '../slices/authSlice'

// 🎭 切片创建器类型 - 移除immer中间件
export type SliceCreator<T, U = T> = StateCreator<
  T,
  [['zustand/subscribeWithSelector', never]],
  [],
  U
>

// 🎮 游戏切片
export interface GameSlice {
  // 状态
  playerLevel: number
  totalExperience: number
  currency: { coins: number; gems: number }
  loading: boolean
  error?: string
  
  // 方法
  initializeSession: (userId: string) => Promise<void>
  updateCurrency: (type: 'coins' | 'gems', amount: number) => void
  addExperience: (amount: number) => void
  clearError: () => void
  resetGameState: () => void
}

// 🎒 卡牌切片
export interface CardsSlice {
  // 状态
  inventory: InventoryItem[]
  searchIndex: Map<string, Set<string>>
  loading: boolean
  error?: string
  
  // 方法
  loadInventory: () => Promise<void>
  addCards: (templateId: string, quantity: number, source: string) => Promise<void>
  consumeCard: (templateId: string, quantity: number) => Promise<boolean>
  tempUseCard: (templateId: string, quantity: number) => boolean
  releaseTempCard: (templateId: string, quantity: number) => void
  getAvailableQuantity: (templateId: string) => number
  getTotalQuantity: (templateId: string) => number
  searchCards: (query: string) => InventoryItem[]
  clearError: () => void
  resetCardsState: () => void
}

// 🎨 UI切片
export interface UISlice {
  // 状态
  currentScene: GameScene
  previousScene?: GameScene
  modals: Record<string, boolean>
  selectedCards: Set<string>
  selectedGroup: string | null
  filters: {
    rarity: string[]
    category: string[]
    availability: 'all' | 'available' | 'unavailable'
  }
  loading: boolean
  error?: string
  
  // 方法
  switchScene: (scene: GameScene) => Promise<void>
  goBack: () => void
  openModal: (modalName: string) => void
  closeModal: (modalName: string) => void
  selectCard: (cardId: string) => void
  unselectCard: (cardId: string) => void
  clearSelection: () => void
  setSelectedGroup: (groupId: string | null) => void
  updateFilters: (filters: Partial<UISlice['filters']>) => void
  clearError: () => void
  resetUIState: () => void
}

// 🏗️ 策略切片
export interface StrategySlice {
  // 状态
  groups: StrategyGroup[]
  currentGroupId: string | null
  loading: boolean
  error?: string
  
  // 方法
  loadGroups: () => Promise<void>
  createGroup: (name: string, description: string) => Promise<string>
  deleteGroup: (groupId: string) => Promise<void>
  setCurrentGroup: (groupId: string | null) => void
  addCardToGroup: (groupId: string, cardInstance: any) => Promise<void>
  removeCardFromGroup: (groupId: string, cardId: string) => Promise<void>
  resetStrategyState: () => void
}

// 🌐 根Store类型
export type RootStore = GameSlice & CardsSlice & UISlice & StrategySlice & AuthSlice
