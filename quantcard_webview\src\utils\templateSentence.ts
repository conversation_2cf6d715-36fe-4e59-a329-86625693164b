/**
 * 构造策略模板的“核心句式”展示文案（前端）
 * 基于参数定义与参数组信息（不依赖后端改动）
 */

export interface ParameterDef {
  type: 'number' | 'text' | 'select' | 'boolean' | 'enum'
  label?: string
  description?: string
  default?: any
  unit?: string
  options?: Array<{ label: string; value: any }>
  validation?: { min?: number; max?: number; step?: number; required?: boolean; pattern?: string }
  visibleWhen?: {
    param?: string
    parameter?: string  // 向后兼容
    eq?: any
    in?: any[]
    value?: any  // 向后兼容
  }
}

export interface ParameterGroupDef {
  parameters: string[]
  prefix?: string
  separator?: string
  layout?: 'horizontal' | 'vertical'
  style?: 'sentence' | 'block' | 'inline'
  sentenceTemplate?: string
  displayMode?: string
  title?: string
}

export function buildCoreSentence(
  parameters: Record<string, ParameterDef>,
  groups?: Record<string, ParameterGroupDef>,
  values?: Record<string, any>
): string {
  const pickValue = (name: string) => {
    const v = values?.[name]
    if (v !== undefined && v !== null && v !== '') return v
    return parameters[name]?.default
  }

  // 选择第一组或名为 sentence 的组
  const groupEntries = groups ? Object.entries(groups) : []
  const sentenceGroup =
    groupEntries.find(([k]) => k.toLowerCase().includes('sentence'))?.[1] ||
    groupEntries[0]?.[1]

  // 没有组：尝试用第一个参数直接展示
  if (!sentenceGroup) {
    const first = Object.keys(parameters)[0]
    if (!first) return ''
    const val = pickValue(first)
    return `${parameters[first]?.label || first} ${val ?? ''}${parameters[first]?.unit || ''}`.trim()
  }

  const names = sentenceGroup.parameters || []
  if (names.length === 0) return ''

  // 如果有句式模板，使用模板渲染
  if (sentenceGroup.sentenceTemplate) {
    return renderSentenceTemplate(sentenceGroup.sentenceTemplate, parameters, values)
  }

  // 使用改进的句式生成逻辑
  return buildNaturalSentence(sentenceGroup, parameters, values)
}

/**
 * 使用句式模板渲染
 */
function renderSentenceTemplate(
  template: string,
  parameters: Record<string, ParameterDef>,
  values?: Record<string, any>
): string {
  const pickValue = (name: string) => {
    const v = values?.[name]
    if (v !== undefined && v !== null && v !== '') return v
    return parameters[name]?.default
  }

  // 替换模板中的占位符 {paramName}
  return template.replace(/\{(\w+)\}/g, (match, paramName) => {
    const value = pickValue(paramName)
    const param = parameters[paramName]

    if (!param) return match

    // 对于select类型，显示label
    if (param.type === 'select' && param.options) {
      const option = param.options.find(opt => opt.value === value)
      return option?.label || String(value || '')
    }

    // 对于数值类型，添加单位
    if (param.type === 'number' && param.unit) {
      return `${value || ''}${param.unit}`
    }

    return String(value || '')
  })
}

/**
 * 构建自然语言句式
 */
function buildNaturalSentence(
  group: ParameterGroupDef,
  parameters: Record<string, ParameterDef>,
  values?: Record<string, any>
): string {
  const pickValue = (name: string) => {
    const v = values?.[name]
    if (v !== undefined && v !== null && v !== '') return v
    return parameters[name]?.default
  }

  const names = group.parameters || []
  const parts: string[] = []

  // 添加前缀
  if (group.prefix) parts.push(group.prefix)

  // 智能处理参数组合
  if (names.length >= 2) {
    const opName = names[0]
    const opVal = String(pickValue(opName) ?? '')
    const v1Name = names[1]
    const v1 = v1Name ? pickValue(v1Name) : undefined
    const v2Name = names[2]
    const v2 = v2Name ? pickValue(v2Name) : undefined

    // operator 映射：直接展示选项 label（若有），否则原值
    const opLabel = (() => {
      const opt = parameters[opName]?.options?.find(o => o.value === opVal)
      return opt?.label || opVal
    })()

    // 构建更自然的句式
    if (opVal && v1Name) {
      const isRange = ['区间', '介于', 'range', 'between'].includes(opVal)

      if (isRange && v2Name && v2 !== undefined && v2 !== null) {
        // 区间模式：市值 100-500亿元
        const unit1 = parameters[v1Name]?.unit || ''
        const unit2 = parameters[v2Name]?.unit || ''
        const sep = group.separator || '-'
        parts.push(`${v1}${sep}${v2}${unit1 || unit2}`)
      } else if (!isRange) {
        // 单值比较：市值 > 100亿元
        const unit = parameters[v1Name]?.unit || ''
        const operator = opLabel === '大于' ? '>' : opLabel === '小于' ? '<' : opLabel
        parts.push(`${operator} ${v1}${unit}`)
      } else {
        // 区间模式但没有上限值
        const unit = parameters[v1Name]?.unit || ''
        parts.push(`${opLabel} ${v1}${unit}`)
      }
    } else if (opVal) {
      parts.push(opLabel)
    }
  } else {
    // 单参数情况
    names.forEach(name => {
      const value = pickValue(name)
      const param = parameters[name]
      if (value !== undefined && value !== null && value !== '') {
        if (param?.type === 'select' && param.options) {
          const option = param.options.find(opt => opt.value === value)
          parts.push(option?.label || String(value))
        } else {
          parts.push(`${value}${param?.unit || ''}`)
        }
      }
    })
  }

  // 合成最终句式
  return parts
    .filter(Boolean)
    .join(' ')
    .replace(/\s+/g, ' ')
    .trim()
}

