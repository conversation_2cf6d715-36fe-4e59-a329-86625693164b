你是一位高级架构师、高级程序员、游戏开发专家、艺术家、技术美术、高级前端工程师、应用开发专家，请分析项目代码，后端是backend文件夹，打包的跨平台webview前端方案在quantcard_webview文件夹中，请仔细思考并完成以下工作：1、检查UniversalCard，项目统一卡牌的样式，现在卡牌是深色方案，请发挥你的设计水平，改为白色80%不透明度的浅色样式，并对样式进行优化；2、检查InventoryDrawer，确保卡牌排列时，卡牌之间的间距不超过15px，距离要近一些；3、用户在InventoryDrawer中点击一个卡牌时，应当出现深色全屏遮罩，显示卡牌的描述信息，描述信息下面有两个简单的按钮，‘使用卡片’和‘进入图鉴’，点击使用卡片进入策略组构建场景，点击进入图鉴，进入卡牌图鉴场景，两个按钮的下面是一个关闭按钮，点击关闭遮罩层。你作为专家，全面统筹、进行系统性修改，修改过程中确保工具调用命令准确，不需要保证向后兼容性，清除掉过时的旧组件和函数，确保代码简洁，结构清晰，减少数据转化次数，避免冗余的函数和方法。

我注意到你添加了不少方法，这个修改是最优的吗，请梳理卡牌状态相关的所有代码，确定最简洁、有效的改造方式。


你的修改没有减少代码行数，反而不断增加，这是不对的，不应该不断地建立新方法
，不需要保证向后兼容性，追求最佳框架、最少冗余、最优性能，能够落地。