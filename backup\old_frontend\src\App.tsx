import React from 'react';
import { BrowserRouter } from 'react-router-dom';
import { ConfigProvider, App as AntdApp } from 'antd';
import { Provider } from 'react-redux';
import { store } from './store';
import zhCN from 'antd/locale/zh_CN';
import AppRoutes from './routes';
import ErrorBoundary from './components/common/ErrorBoundary';
import GameShell from './game/GameShell';
import SceneRouter from './game/SceneRouter';

const App: React.FC = () => {
  return (
    <Provider store={store}>
      <ConfigProvider
        locale={zhCN}
        theme={{
          token: {
            colorPrimary: '#00ffff',
            colorBgBase: '#000000',
            colorTextBase: '#ffffff',
          },
        }}
      >
        <AntdApp>
          <ErrorBoundary>
            <GameShell>
              <SceneRouter />
            </GameShell>
          </ErrorBoundary>
        </AntdApp>
      </ConfigProvider>
    </Provider>
  );
};

export default App;
